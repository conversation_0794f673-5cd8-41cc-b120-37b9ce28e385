-- Update trigger function to enqueue update_shopify_product_title tasks with shopify_store_id
-- This function is called by trigger: trg_t_products_name_after_shopify_upload
-- Tri<PERSON> fires when t_products.name changes and shopify_uploaded_at is not null

CREATE OR REPLACE FUNCTION public.enqueue_update_shopify_title_task()
RETURNS TRIGGER AS $$
BEGIN
  -- Only enqueue if the product has been uploaded to Shopify
  -- and has a shopify_store_id
  IF NEW.shopify_uploaded_at IS NOT NULL AND NEW.shopify_store_id IS NOT NULL THEN
    INSERT INTO public.t_task_queue (
      task_type,
      payload,
      status,
      scheduled_at,
      created_at,
      enqueued_by
    ) VALUES (
      'update_shopify_product_title',
      jsonb_build_object(
        'shopify_handle', NEW.shopify_handle,
        'shopify_store_id', NEW.shopify_store_id,
        'product_id', NEW.id
      ),
      'pending',
      NOW() + INTERVAL '5 minutes',
      NOW(),
      'trigger:t_products.name_changed'
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- The trigger itself should already exist:
-- CREATE TRIGGER trg_t_products_name_after_shopify_upload
-- AFTER UPDATE OF name ON t_products
-- FOR EACH ROW
-- WHEN (OLD.name IS DISTINCT FROM NEW.name AND NEW.shopify_uploaded_at IS NOT NULL)
-- EXECUTE FUNCTION enqueue_update_shopify_title_task();

-- Confirmation message
DO $$
BEGIN
  RAISE NOTICE 'Updated enqueue_update_shopify_title_task function to include shopify_store_id in payload';
END $$;

