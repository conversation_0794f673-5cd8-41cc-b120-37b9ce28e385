# Security Fixes: Quick Checklist

## 🚨 CRITICAL - Do These First

### Credential Rotation
- [ ] Go to Supabase Dashboard
- [ ] Settings → API Keys
- [ ] Rotate all keys
- [ ] Update .env file with new keys
- [ ] Restart all services

### Remove Hardcoded Credentials
- [ ] Delete `.env-tp.txt` file
- [ ] Remove credentials from `update_task_queue_worker.js` (lines 5-6)
- [ ] Remove credentials from `enqueue_match_task.js` (lines 5-6)
- [ ] Remove credentials from `update_task_locking.js` (lines 4-5)
- [ ] Remove credentials from `informedReportDownloader.cjs` (lines 46-47)
- [ ] Commit changes: `git add -A && git commit -m "Remove hardcoded credentials"`

---

## 🔴 HIGH PRIORITY - Do This Week

### Update Worker to Use Service Role Key

**File: taskQueueWorker.js (line 71)**
- [ ] Change `process.env.SUPABASE_KEY` to `process.env.SUPABASE_SERVICE_ROLE_KEY`
- [ ] Test worker still processes tasks

**File: taskQueueWorkerDaemon.js (line 11)**
- [ ] Change `process.env.SUPABASE_KEY` to `process.env.SUPABASE_SERVICE_ROLE_KEY`
- [ ] Restart daemon: `pm2 restart taskQueueWorkerDaemon`

**File: enqueueWorkerStatusTask.js (line 9)**
- [ ] Change `process.env.SUPABASE_KEY` to `process.env.SUPABASE_SERVICE_ROLE_KEY`
- [ ] Test status updates work

### Add Authentication Middleware

**Create authMiddleware.js**
- [ ] Copy code from SECURITY_FIX_IMPLEMENTATION.md
- [ ] Save as `authMiddleware.js`
- [ ] Test middleware works

**Update adminServer.js (after line 32)**
- [ ] Add: `import { authMiddleware } from './authMiddleware.js';`
- [ ] Add: `app.use('/api/', authMiddleware);`
- [ ] Restart admin server: `pm2 restart adminServer`
- [ ] Test admin interface (should require login)

---

## 🟡 MEDIUM PRIORITY - Do Next Week

### Update admin.html with Auth Headers

**Add helper function (in script section)**
- [ ] Add `getAuthToken()` function
- [ ] Test it returns valid token

**Update all fetch calls**
- [ ] Search for `fetch('/api/` in admin.html
- [ ] Add `'Authorization': 'Bearer ' + getAuthToken()` to headers
- [ ] Test all buttons work
- [ ] Verify no 401 errors in console

### Add Input Validation

**For each admin endpoint:**
- [ ] Validate request parameters
- [ ] Check data types
- [ ] Check value ranges
- [ ] Return 400 for invalid input

---

## 🟢 TESTING CHECKLIST

### Before Deployment
- [ ] No hardcoded credentials in code
- [ ] All fetch calls have Authorization headers
- [ ] Worker uses service role key
- [ ] Admin endpoints require authentication
- [ ] Error messages are clear
- [ ] Logs show successful operations

### After Deployment
- [ ] Admin interface loads
- [ ] Login works
- [ ] All buttons functional
- [ ] Tasks process successfully
- [ ] Worker logs show no errors
- [ ] Email notifications work
- [ ] No permission denied errors
- [ ] No 401 errors in console

### Security Verification
- [ ] Unauthenticated requests rejected (401)
- [ ] Non-admin users blocked (403)
- [ ] Service role key used by worker
- [ ] No credentials in version control
- [ ] Credentials rotated in Supabase

---

## 📋 FILES TO MODIFY

### Create New Files
- [ ] `authMiddleware.js` - Authentication middleware

### Modify Existing Files
- [ ] `adminServer.js` - Add auth middleware
- [ ] `taskQueueWorker.js` - Change to service role key
- [ ] `taskQueueWorkerDaemon.js` - Change to service role key
- [ ] `enqueueWorkerStatusTask.js` - Change to service role key
- [ ] `admin.html` - Add Authorization headers

### Delete Files
- [ ] `.env-tp.txt` - Contains hardcoded credentials

### Clean Up Files
- [ ] `update_task_queue_worker.js` - Remove hardcoded credentials
- [ ] `enqueue_match_task.js` - Remove hardcoded credentials
- [ ] `update_task_locking.js` - Remove hardcoded credentials
- [ ] `informedReportDownloader.cjs` - Remove hardcoded credentials

---

## 🔄 ROLLBACK PLAN

If something breaks:

```bash
# 1. Revert code changes
git revert <commit-hash>

# 2. Restart services
pm2 restart all

# 3. Verify functionality
pm2 logs adminServer
pm2 logs taskQueueWorkerDaemon
```

---

## 📞 TROUBLESHOOTING

### Admin interface shows "Missing or invalid authorization header"
- [ ] Check auth token is being sent
- [ ] Verify user is logged in
- [ ] Check user has admin role in user_roles table

### Worker tasks fail with permission errors
- [ ] Verify service role key is set in .env
- [ ] Check RLS policies allow service role
- [ ] Restart worker: `pm2 restart taskQueueWorkerDaemon`

### "SUPABASE_SERVICE_ROLE_KEY not set" error
- [ ] Check .env file exists
- [ ] Verify key is set: `echo $SUPABASE_SERVICE_ROLE_KEY`
- [ ] Restart service: `pm2 restart all`

### Admin buttons don't work
- [ ] Check browser console for errors
- [ ] Verify Authorization header is sent
- [ ] Check user is admin in user_roles table
- [ ] Restart admin server: `pm2 restart adminServer`

---

## ✅ COMPLETION CHECKLIST

- [ ] All credentials rotated
- [ ] All hardcoded credentials removed
- [ ] Authentication middleware added
- [ ] Worker uses service role key
- [ ] Admin.html has Authorization headers
- [ ] Input validation added
- [ ] All tests pass
- [ ] No errors in logs
- [ ] Admin interface works
- [ ] Worker processes tasks
- [ ] Documentation updated

---

## 📚 REFERENCE DOCUMENTS

1. **SECURITY_REVIEW_SUMMARY.md** - Overview of all issues
2. **SECURITY_REVIEW_POST_LOVABLE.md** - Detailed findings
3. **SECURITY_FIX_IMPLEMENTATION.md** - Step-by-step fixes
4. **SECURITY_FIXES_IMPACT_ANALYSIS.md** - What might break

---

**Status:** ⚠️ ACTION REQUIRED
**Priority:** CRITICAL
**Timeline:** Start immediately, complete within 2 weeks

