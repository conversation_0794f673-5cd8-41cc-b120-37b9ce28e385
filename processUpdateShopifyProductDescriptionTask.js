// processUpdateShopifyProductDescriptionTask.js
// Updates a Shopify product's description (body_html) by handle
// Source for new description:
// - t_products.description for the product with an exact shopify_handle match
// No fallback to tags; if no exact match in t_products, the task errors with an explanation.
// Supports multiple Shopify stores via shopify_store_id in payload

import { getShopifyCredentials, getProductsBaseUrl, shopifyGraphQLRequest as makeGraphQLRequest } from './shopifyStoreConfig.js';

function extractNumericIdFromGid(gid) {
  if (!gid) return null;
  const parts = String(gid).split('/');
  return parts[parts.length - 1];
}

function looksLikeHtml(s) {
  if (!s) return false;
  return /<[^>]+>/.test(String(s));
}

function parseTagsToMap(tags) {
  const arr = Array.isArray(tags) ? tags : typeof tags === 'string' ? tags.split(',') : [];
  const map = new Map();
  for (const raw of arr) {
    const t = String(raw).trim();
    if (!t) continue;
    const [k, ...rest] = t.split('_');
    if (!map.has(k)) map.set(k, []);
    map.get(k).push(rest.join('_'));
  }
  return map;
}

async function buildOslBodyHtmlFromTags(supabase, tags) {
  const tagMap = parseTagsToMap(tags);
  const brand = (tagMap.get('disc') || []).find(x => x.startsWith('brand'));
  const mold = (tagMap.get('disc') || []).find(x => x.startsWith('mold'));
  const plastic = (tagMap.get('disc') || []).find(x => x.startsWith('plastic'));
  const stamp = (tagMap.get('disc') || []).find(x => x.startsWith('stamp'));

  const brandName = brand ? brand.replace(/^brand_/, '') : null;
  const moldName = mold ? mold.replace(/^mold_/, '') : null;
  const plasticName = plastic ? plastic.replace(/^plastic_/, '') : null;
  const stampName = stamp ? stamp.replace(/^stamp_/, '') : null;

  if (!brandName || !moldName || !plasticName || !stampName) return { body_html: null, reason: 'Missing disc_* tags' };

  // Resolve IDs
  const { data: brandRow } = await supabase.from('t_brands').select('id, brand').ilike('brand', brandName).maybeSingle();
  const { data: plasticRow } = await supabase.from('t_plastics').select('id, plastic').ilike('plastic', plasticName).maybeSingle();
  const { data: moldRow } = await supabase
    .from('t_molds')
    .select('id, mold, brand_id')
    .ilike('mold', moldName)
    .eq('brand_id', brandRow.id)
    .maybeSingle();
  const { data: stampRow } = await supabase.from('t_stamps').select('id, stamp').ilike('stamp', stampName).maybeSingle();

  if (!brandRow || !plasticRow || !moldRow || !stampRow) return { body_html: null, reason: 'Could not resolve IDs from tags' };

  // Use mps group if available for blurb; otherwise just combine view blurbs
  const { data: mpsRow } = await supabase
    .from('t_mps')
    .select('id, g_blurb_with_link')
    .eq('mold_id', moldRow.id)
    .eq('plastic_id', plasticRow.id)
    .eq('stamp_id', stampRow.id)
    .maybeSingle();

  const { data: moldView } = await supabase.from('v_molds').select('blurb_with_link').eq('id', moldRow.id).maybeSingle();
  const { data: plasticView } = await supabase.from('v_plastics').select('blurb_with_link').eq('id', plasticRow.id).maybeSingle();
  const { data: stampData } = await supabase.from('t_stamps').select('blurb').eq('id', stampRow.id).maybeSingle();

  let body_html = '';
  body_html += (mpsRow?.g_blurb_with_link || '');
  body_html += (moldView?.blurb_with_link || '');
  body_html += (plasticView?.blurb_with_link || '');
  body_html += (stampData?.blurb || '');

  return { body_html, reason: 'derived_from_disc_tags' };
}

export default async function processUpdateShopifyProductDescriptionTask(task, { supabase, updateTaskStatus, logError }) {
  try {
    // Parse payload
    const payload = typeof task.payload === 'object' && task.payload !== null ? task.payload : JSON.parse(task.payload || '{}');
    const handle = payload.shopify_handle;
    const shopifyStoreId = payload.shopify_store_id;

    if (!handle) {
      await updateTaskStatus(task.id, 'error', { message: 'Missing shopify_handle in payload' });
      return;
    }

    if (!shopifyStoreId) {
      await updateTaskStatus(task.id, 'error', { message: 'Missing shopify_store_id in payload' });
      return;
    }

    await updateTaskStatus(task.id, 'processing');

    // Get credentials for the specified store
    let credentials;
    try {
      credentials = getShopifyCredentials(shopifyStoreId);
    } catch (error) {
      await updateTaskStatus(task.id, 'error', { message: `Failed to get Shopify credentials: ${error.message}` });
      return;
    }

    const { endpoint, accessToken, storeName } = credentials;

    // Find Shopify product by handle and get tags
    const data = await makeGraphQLRequest(`
      query getProductByHandle($handle: String!) {
        productByHandle(handle: $handle) { id handle title tags }
      }
    `, { handle }, shopifyStoreId);

    const product = data?.productByHandle;
    if (!product) {
      await updateTaskStatus(task.id, 'error', { message: `No Shopify product found for handle ${handle} on ${storeName}` });
      return;
    }

    const productId = extractNumericIdFromGid(product.id);

    // Fetch the exact local product by handle; no fallbacks
    const { data: prodRow, error: prodErr } = await supabase
      .from('t_products')
      .select('id, description, shopify_handle')
      .eq('shopify_handle', handle)
      .maybeSingle();

    if (prodErr) {
      await logError?.(`[processUpdateShopifyProductDescriptionTask] DB error: ${prodErr.message}`, { handle });
    }

    if (!prodRow) {
      await updateTaskStatus(task.id, 'error', { message: `No local t_products row found for exact shopify_handle '${handle}'` });
      return;
    }

    let body_html = (prodRow.description ?? '');
    let source = 't_products.description';

    // Paragraph wrap for plain text descriptions from t_products when non-empty
    const trimmed = String(body_html).trim();
    if (trimmed && !looksLikeHtml(trimmed)) {
      body_html = `<p>${trimmed}</p>`;
    }

    // Update Shopify body_html via REST API
    const baseUrl = getProductsBaseUrl(endpoint);
    const url = `${baseUrl}/products/${productId}.json`;
    const updatePayload = { product: { id: Number(productId), body_html: body_html } };

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': accessToken,
      },
      body: JSON.stringify(updatePayload),
    });

    const result = await response.json();
    if (!response.ok) {
      throw new Error(`Error updating product ${productId} on ${storeName}: ${JSON.stringify(result)}`);
    }

    const updated = result.product;

    await updateTaskStatus(task.id, 'completed', {
      message: `Updated Shopify product description on ${storeName}`,
      handle,
      product_id: productId,
      source,
      updated_title: updated?.title || null,
      body_html_length: body_html.length,
      store: storeName,
      store_id: shopifyStoreId,
    });
  } catch (error) {
    await logError?.(`[processUpdateShopifyProductDescriptionTask] Error: ${error.message}`, { taskId: task?.id });
    await updateTaskStatus(task.id, 'error', { message: 'Error updating Shopify product description', error: error.message });
  }
}

