// processSizeTierShippingOrPackingUpdatedTask.js
// Task handler for size_tier_shipping_or_packing_updated task type
// When a size tier's shipping or packing costs change, enqueue update_informed_pricing tasks
// for all listings that use that size tier

import dotenv from 'dotenv';
dotenv.config();

/**
 * Process a size_tier_shipping_or_packing_updated task
 * @param {Object} task - The task object from t_task_queue
 * @param {Object} context - Context object with supabase, updateTaskStatus, logError
 */
async function processSizeTierShippingOrPackingUpdatedTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processSizeTierShippingOrPackingUpdatedTask] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[processSizeTierShippingOrPackingUpdatedTask] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[processSizeTierShippingOrPackingUpdatedTask] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[processSizeTierShippingOrPackingUpdatedTask] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        console.log(`[processSizeTierShippingOrPackingUpdatedTask] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload type: ${typeof task.payload}`);
      }
    } catch (parseError) {
      console.error(`[processSizeTierShippingOrPackingUpdatedTask] Error parsing payload for task ${task.id}:`, parseError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to parse payload: ${parseError.message}`,
        error: parseError.message
      });
      return;
    }

    // Extract size_tier_id from payload
    const { size_tier_id } = payload;

    if (!size_tier_id) {
      console.error(`[processSizeTierShippingOrPackingUpdatedTask] Task ${task.id} missing size_tier_id in payload`);
      await updateTaskStatus(task.id, 'error', {
        message: 'Missing size_tier_id in payload',
        error: 'size_tier_id is required'
      });
      return;
    }

    const sizeTierId = size_tier_id;
    console.log(`[processSizeTierShippingOrPackingUpdatedTask] Task ${task.id} processing size_tier_id: ${sizeTierId}`);

    // Update task status to processing
    await updateTaskStatus(task.id, 'processing');

    // Find all records in v_rpro_informed_pricing_calculations with matching size_tier_id
    // Filter out records with null listing_sku
    console.log(`[processSizeTierShippingOrPackingUpdatedTask] Looking up listings with size_tier_id = ${sizeTierId}`);

    const { data: affectedListings, error: listingsError } = await supabase
      .from('v_rpro_informed_pricing_calculations')
      .select('listing_sku, rpro_ivno, size_tier_id')
      .eq('size_tier_id', sizeTierId)
      .not('listing_sku', 'is', null);

    if (listingsError) {
      console.error(`[processSizeTierShippingOrPackingUpdatedTask] Error looking up affected listings:`, listingsError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to look up affected listings: ${listingsError.message}`,
        error: listingsError.message
      });
      return;
    }

    if (!affectedListings || affectedListings.length === 0) {
      console.log(`[processSizeTierShippingOrPackingUpdatedTask] No listings found for size_tier_id ${sizeTierId} (with non-null listing_sku)`);
      await updateTaskStatus(task.id, 'completed', {
        message: `No listings found for size_tier_id ${sizeTierId} (with non-null listing_sku)`,
        size_tier_id: sizeTierId,
        listings_found: 0,
        tasks_enqueued: 0
      });
      return;
    }

    console.log(`[processSizeTierShippingOrPackingUpdatedTask] Found ${affectedListings.length} listings for size_tier_id ${sizeTierId}`);

    // Enqueue update_informed_pricing task for each listing
    const tasksToEnqueue = [];
    const now = new Date();
    // Schedule immediately - rate limiting is handled by the processUpdateInformedPricingTask handler

    for (const listing of affectedListings) {
      console.log(`[processSizeTierShippingOrPackingUpdatedTask] Enqueueing update_informed_pricing for listing_sku: ${listing.listing_sku}`);

      tasksToEnqueue.push({
        task_type: 'update_informed_pricing',
        payload: {
          listing_sku: listing.listing_sku
        },
        status: 'pending',
        scheduled_at: now.toISOString(),
        created_at: now.toISOString(),
        enqueued_by: `size_tier_shipping_or_packing_updated:${task.id}`
      });
    }

    // Insert all tasks in a single batch
    console.log(`[processSizeTierShippingOrPackingUpdatedTask] Enqueueing ${tasksToEnqueue.length} update_informed_pricing tasks`);
    
    const { data: enqueuedTasks, error: enqueueError } = await supabase
      .from('t_task_queue')
      .insert(tasksToEnqueue)
      .select('id');

    if (enqueueError) {
      console.error(`[processSizeTierShippingOrPackingUpdatedTask] Error enqueueing tasks:`, enqueueError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to enqueue update_informed_pricing tasks: ${enqueueError.message}`,
        error: enqueueError.message,
        size_tier_id: sizeTierId,
        listings_found: affectedListings.length
      });
      return;
    }

    console.log(`[processSizeTierShippingOrPackingUpdatedTask] Successfully enqueued ${enqueuedTasks.length} tasks`);

    // Mark task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully enqueued ${enqueuedTasks.length} update_informed_pricing tasks for size_tier_id ${sizeTierId}`,
      size_tier_id: sizeTierId,
      listings_found: affectedListings.length,
      tasks_enqueued: enqueuedTasks.length,
      note: 'Rate limiting (15 min between API calls) is handled by processUpdateInformedPricingTask',
      enqueued_task_ids: enqueuedTasks.map(t => t.id)
    });

  } catch (error) {
    console.error(`[processSizeTierShippingOrPackingUpdatedTask] Unexpected error processing task ${task.id}:`, error);
    await logError(task.id, error.message);
    await updateTaskStatus(task.id, 'error', {
      message: `Unexpected error: ${error.message}`,
      error: error.message
    });
  }
}

export default processSizeTierShippingOrPackingUpdatedTask;

