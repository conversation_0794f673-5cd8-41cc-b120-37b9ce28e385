# Image Verification Flow for Product Variants

## Overview
The image verification system ensures that product variant images are verified before marking variants as "Ready to Publish". The system prevents infinite loops while allowing manual re-checks.

## Components

### 1. `verify_t_images_image` Task
- **Type**: Spawned child process
- **Handler**: `verifyImage.js`
- **What it does**:
  - Fetches the t_images record
  - Constructs the image URL from config and variant ID
  - Performs HEAD request to verify image is accessible
  - Sets `t_images.image_verified = true/false`
  - Updates `t_product_variants.todo` with failure message if needed

### 2. `check_if_product_variant_is_ready` Task
- **Type**: Inline task in taskQueueWorker.js
- **What it does**:
  - Checks if variant meets all readiness criteria
  - **NEW**: If image is not verified, enqueues a `verify_t_images_image` task
  - Updates `t_product_variants.todo` with status

### 3. Trigger: `t_images_enqueue_variant_ready_on_image_verified`
- **Fires**: When `t_images.image_verified` changes from FALSE to TRUE
- **For**: `t_product_variants` records only
- **What it does**:
  - Enqueues a NEW `check_if_product_variant_is_ready` task
  - **Loop Prevention**: Only enqueues if no pending/processing readiness check exists for that variant

## Flow Diagram

### Scenario 1: Manual "Check Ready" Button Click (Image Not Verified)

```
User clicks "Check Ready" button
    ↓
Enqueues: check_if_product_variant_is_ready (Task A)
    ↓
Worker processes Task A
    ↓
Checks: Is image verified?
    ├─ NO → Enqueues: verify_t_images_image (Task B)
    │        Updates todo: "image not verified"
    │        Completes Task A
    │
    └─ YES → Continues with other checks
             Updates todo: "Ready to Publish" or other reasons
             Completes Task A
    ↓
Worker processes Task B (verify_t_images_image)
    ↓
Spawns: verifyImage.js
    ↓
Checks image accessibility at:
https://s3.amazonaws.com/paintball/shopify/dgaccessories/{variantId}.jpg
    ↓
Sets: t_images.image_verified = true
    ↓
Trigger fires: t_images_enqueue_variant_ready_on_image_verified
    ↓
Enqueues: check_if_product_variant_is_ready (Task C)
    ├─ Loop Prevention: Only enqueues because Task A is already completed
    │
    ↓
Worker processes Task C
    ↓
Image is now verified ✓
    ↓
Continues with other checks
    ↓
Updates todo: "Ready to Publish" (if all checks pass)
```

### Scenario 2: Image Upload System (Automatic)

```
Image uploaded to S3 at:
shopify/dgaccessories/{variantId}.jpg
    ↓
Upload system creates/updates t_images record
    ↓
Upload system enqueues: verify_t_images_image
    ↓
Worker processes verify task
    ↓
Spawns: verifyImage.js
    ↓
Verifies image is accessible
    ↓
Sets: t_images.image_verified = true
    ↓
Trigger fires: t_images_enqueue_variant_ready_on_image_verified
    ↓
Enqueues: check_if_product_variant_is_ready
    ↓
Worker processes readiness check
    ↓
Image is verified ✓
    ↓
Updates todo: "Ready to Publish" (if all checks pass)
```

## Loop Prevention Mechanism

The trigger `t_images_enqueue_variant_ready_on_image_verified` includes this check:

```sql
WHERE NOT EXISTS (
  SELECT 1 FROM public.t_task_queue tq
  WHERE tq.task_type = 'check_if_product_variant_is_ready'
    AND (tq.status = 'pending' OR tq.status = 'processing')
    AND (tq.payload->>'id')::INT = NEW.record_id
);
```

This ensures:
- If a readiness check is already pending/processing, no new one is enqueued
- Once the first readiness check completes, the trigger can enqueue a new one
- No infinite loops

## Key Points

1. **Manual "Check Ready" button** now triggers image verification if needed
2. **Image verification is automatic** - happens in background
3. **No loops** - trigger prevents duplicate tasks
4. **Idempotent** - running the same check multiple times is safe
5. **Transparent** - todo field shows current status at each step

## Configuration

Image URLs are constructed from:
- `public_image_server`: `https://s3.amazonaws.com/paintball/shopify`
- `folder_product_variants`: `dgaccessories`
- Variant ID: `{variantId}`

Result: `https://s3.amazonaws.com/paintball/shopify/dgaccessories/{variantId}.jpg`

