// importAmazonFbaFeePreview.js - Import Amazon FBA Fee Preview Report into Supabase

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('[importAmazonFbaFeePreview] Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Configuration
const DATA_DIR = 'C:\\Users\\<USER>\\supabase_project\\data\\external data\\Amazon FBA Fee Preview';
const TABLE_NAME = 'it_amaz_fba_fee_preview';

// Function to find the most recent file by modification timestamp
function findLatestFile() {
  try {
    if (!fs.existsSync(DATA_DIR)) {
      throw new Error(`Directory does not exist: ${DATA_DIR}`);
    }

    const files = fs.readdirSync(DATA_DIR)
      .filter(file => file.endsWith('.txt'))
      .map(file => {
        const fullPath = path.join(DATA_DIR, file);
        const stats = fs.statSync(fullPath);

        return {
          filename: file,
          fullPath: fullPath,
          modifiedTime: stats.mtime
        };
      })
      .sort((a, b) => b.modifiedTime - a.modifiedTime); // Sort by file modification time descending (newest first)

    if (files.length === 0) {
      throw new Error('No FBA Fee Preview files found in the directory');
    }

    const latestFile = files[0];
    console.log(`[importAmazonFbaFeePreview] Found latest file: ${latestFile.filename} (modified: ${latestFile.modifiedTime.toISOString()})`);

    return latestFile;
  } catch (error) {
    console.error(`[importAmazonFbaFeePreview] Error finding latest file: ${error.message}`);
    throw error;
  }
}

// Function to parse a tab-delimited line with proper handling of empty fields
function parseTabDelimitedLine(line, expectedFieldCount) {
  const fields = line.split('\t');
  
  // Pad with empty strings if we have fewer fields than expected
  while (fields.length < expectedFieldCount) {
    fields.push('');
  }
  
  // Truncate if we have more fields than expected
  if (fields.length > expectedFieldCount) {
    fields.splice(expectedFieldCount);
  }
  
  return fields;
}

// Function to convert field value to appropriate type
function convertFieldValue(value, fieldName) {
  if (!value || value.trim() === '' || value.trim() === '--') {
    return null;
  }

  const trimmedValue = value.trim();

  // Handle numeric fields (prices, fees, dimensions, weights)
  if ([
    'your_price', 'sales_price', 'longest_side', 'median_side', 'shortest_side', 
    'length_and_girth', 'item_package_weight', 'estimated_fee_total',
    'estimated_referral_fee_per_unit', 'estimated_variable_closing_fee',
    'estimated_fixed_closing_fee', 'estimated_order_handling_fee_per_order',
    'estimated_pick_pack_fee_per_unit', 'estimated_weight_handling_fee_per_unit',
    'expected_fulfillment_fee_per_unit', 'estimated_future_fee',
    'estimated_future_order_handling_fee_per_order', 'estimated_future_pick_pack_fee_per_unit',
    'estimated_future_weight_handling_fee_per_unit', 'expected_future_fulfillment_fee_per_unit'
  ].includes(fieldName)) {
    const numValue = parseFloat(trimmedValue);
    return isNaN(numValue) ? null : numValue;
  }

  return trimmedValue;
}

// Main import function
async function importAmazonFbaFeePreview() {
  const startTime = Date.now();
  
  try {
    console.log('[importAmazonFbaFeePreview] Starting Amazon FBA Fee Preview import...');

    // Find the latest file by modification timestamp
    const fileInfo = findLatestFile();
    console.log(`[importAmazonFbaFeePreview] Processing file: ${fileInfo.filename}`);

    // Read and parse the file
    const fileContent = fs.readFileSync(fileInfo.fullPath, 'utf-8');
    const lines = fileContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

    if (lines.length === 0) {
      throw new Error('File is empty or contains no valid data');
    }

    // Parse header line to get field names
    const headerLine = lines[0];
    const headers = parseTabDelimitedLine(headerLine, 32); // 32 fields based on the file structure
    
    console.log(`[importAmazonFbaFeePreview] Found ${headers.length} columns in header`);
    console.log(`[importAmazonFbaFeePreview] Processing ${lines.length - 1} data rows`);

    // Map header names to database column names
    const fieldMapping = {
      'sku': 'sku',
      'fnsku': 'fnsku',
      'asin': 'asin',
      'amazon-store': 'amazon_store',
      'product-name': 'product_name',
      'product-group': 'product_group',
      'brand': 'brand',
      'fulfilled-by': 'fulfilled_by',
      'your-price': 'your_price',
      'sales-price': 'sales_price',
      'longest-side': 'longest_side',
      'median-side': 'median_side',
      'shortest-side': 'shortest_side',
      'length-and-girth': 'length_and_girth',
      'unit-of-dimension': 'unit_of_dimension',
      'item-package-weight': 'item_package_weight',
      'unit-of-weight': 'unit_of_weight',
      'product-size-tier': 'product_size_tier',
      'currency': 'currency',
      'estimated-fee-total': 'estimated_fee_total',
      'estimated-referral-fee-per-unit': 'estimated_referral_fee_per_unit',
      'estimated-variable-closing-fee': 'estimated_variable_closing_fee',
      'estimated-fixed-closing-fee': 'estimated_fixed_closing_fee',
      'estimated-order-handling-fee-per-order': 'estimated_order_handling_fee_per_order',
      'estimated-pick-pack-fee-per-unit': 'estimated_pick_pack_fee_per_unit',
      'estimated-weight-handling-fee-per-unit': 'estimated_weight_handling_fee_per_unit',
      'expected-fulfillment-fee-per-unit': 'expected_fulfillment_fee_per_unit',
      'estimated-future-fee (Current Selling on Amazon + Future Fulfillment fees)': 'estimated_future_fee',
      'estimated-future-order-handling-fee-per-order': 'estimated_future_order_handling_fee_per_order',
      'estimated-future-pick-pack-fee-per-unit': 'estimated_future_pick_pack_fee_per_unit',
      'estimated-future-weight-handling-fee-per-unit': 'estimated_future_weight_handling_fee_per_unit',
      'expected-future-fulfillment-fee-per-unit': 'expected_future_fulfillment_fee_per_unit'
    };

    // Parse data lines
    const dataRows = [];
    let skippedRows = 0;
    let skippedNonUS = 0;

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      if (!line.trim()) {
        skippedRows++;
        continue;
      }

      const values = parseTabDelimitedLine(line, headers.length);
      const row = {};

      // Map each field
      for (let j = 0; j < headers.length && j < values.length; j++) {
        const headerName = headers[j];
        const dbFieldName = fieldMapping[headerName] || headerName.toLowerCase().replace(/[^a-z0-9]/g, '_');
        const value = convertFieldValue(values[j], dbFieldName);

        if (dbFieldName && value !== undefined) {
          row[dbFieldName] = value;
        }
      }

      // Only include US amazon_store records
      if (row.amazon_store !== 'US') {
        skippedNonUS++;
        continue;
      }

      dataRows.push(row);
    }

    console.log(`[importAmazonFbaFeePreview] Parsed ${dataRows.length} valid US rows, skipped ${skippedRows} empty rows and ${skippedNonUS} non-US rows`);

    if (dataRows.length === 0) {
      throw new Error('No valid data rows found to import');
    }

    // Upsert data in chunks (using sku as the unique key)
    const chunkSize = 500;
    let totalUpserted = 0;

    for (let i = 0; i < dataRows.length; i += chunkSize) {
      const chunk = dataRows.slice(i, i + chunkSize);
      
      console.log(`[importAmazonFbaFeePreview] Upserting chunk ${Math.floor(i / chunkSize) + 1} (${chunk.length} records)`);

      const { data, error } = await supabase
        .from(TABLE_NAME)
        .upsert(chunk, { onConflict: 'sku' });

      if (error) {
        throw new Error(`Failed to upsert chunk starting at row ${i + 1}: ${error.message}`);
      }

      totalUpserted += chunk.length;
    }

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    
    console.log(`[importAmazonFbaFeePreview] Import completed successfully!`);
    console.log(`[importAmazonFbaFeePreview] File: ${fileInfo.filename}`);
    console.log(`[importAmazonFbaFeePreview] Total records upserted: ${totalUpserted}`);
    console.log(`[importAmazonFbaFeePreview] Duration: ${duration} seconds`);

    // Return summary for API response
    return {
      success: true,
      fileName: fileInfo.filename,
      totalRecords: totalUpserted,
      duration: `${duration} seconds`
    };

  } catch (error) {
    console.error(`[importAmazonFbaFeePreview] Import failed: ${error.message}`);
    throw error;
  }
}

// Run the import if this script is executed directly
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

if (process.argv[1] === __filename) {
  importAmazonFbaFeePreview()
    .then(result => {
      console.log('[importAmazonFbaFeePreview] Import completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('[importAmazonFbaFeePreview] Import failed:', error.message);
      process.exit(1);
    });
}

export { importAmazonFbaFeePreview };

