// enqueueSizeTierShippingOrPackingUpdatedTask.js
// Helper script to manually enqueue a size_tier_shipping_or_packing_updated task

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function enqueueSizeTierShippingOrPackingUpdatedTask(sizeTierId) {
  console.log('📋 Enqueueing size_tier_shipping_or_packing_updated task...');
  console.log('='.repeat(60));

  try {
    // Validate the size_tier_id exists and check how many listings will be affected
    // Filter out records with null listing_sku
    console.log(`\n🔍 Checking size_tier_id "${sizeTierId}" in v_rpro_informed_pricing_calculations...`);

    const { data: affectedListings, error: listingsError } = await supabase
      .from('v_rpro_informed_pricing_calculations')
      .select('listing_sku, rpro_ivno, size_tier_id')
      .eq('size_tier_id', sizeTierId)
      .not('listing_sku', 'is', null);

    if (listingsError) {
      console.error('❌ Error looking up affected listings:', listingsError);
      return;
    }

    if (!affectedListings || affectedListings.length === 0) {
      console.log(`⚠️  No listings found for size_tier_id ${sizeTierId} (with non-null listing_sku)`);
      console.log('This task will complete immediately without enqueueing any update_informed_pricing tasks.');
    } else {
      console.log(`✅ Found ${affectedListings.length} listings that will be affected:`);
      
      // Show first 10 listings
      const displayCount = Math.min(10, affectedListings.length);
      for (let i = 0; i < displayCount; i++) {
        const listing = affectedListings[i];
        console.log(`   ${i + 1}. listing_sku: ${listing.listing_sku}, sku: ${listing.sku}`);
      }
      
      if (affectedListings.length > 10) {
        console.log(`   ... and ${affectedListings.length - 10} more`);
      }
      
      console.log(`\n💡 This will enqueue ${affectedListings.length} update_informed_pricing tasks.`);
    }

    // Enqueue the task
    console.log('\n📝 Enqueueing size_tier_shipping_or_packing_updated task...');
    
    const taskData = {
      task_type: 'size_tier_shipping_or_packing_updated',
      payload: {
        size_tier_id: sizeTierId
      },
      status: 'pending',
      scheduled_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      enqueued_by: 'manual_enqueue_size_tier_shipping_or_packing_updated'
    };

    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .insert([taskData])
      .select()
      .single();

    if (taskError) {
      console.error('❌ Error enqueueing task:', taskError);
      return;
    }

    console.log(`\n✅ Task enqueued successfully!`);
    console.log(`   Task ID: ${task.id}`);
    console.log(`   Status: ${task.status}`);
    console.log(`   Scheduled at: ${task.scheduled_at}`);
    console.log('\n💡 The task queue worker will process this task automatically.');
    console.log('   It will enqueue update_informed_pricing tasks immediately (no delay).');
    console.log('   All update_informed_pricing tasks will be batched together in a single upload.');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Get size_tier_id from command line argument
const sizeTierId = process.argv[2];

if (!sizeTierId) {
  console.error('❌ Usage: node enqueueSizeTierShippingOrPackingUpdatedTask.js <size_tier_id>');
  console.error('   Example: node enqueueSizeTierShippingOrPackingUpdatedTask.js 1');
  process.exit(1);
}

enqueueSizeTierShippingOrPackingUpdatedTask(parseInt(sizeTierId));

