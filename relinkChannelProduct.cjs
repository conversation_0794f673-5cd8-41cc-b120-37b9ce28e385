const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔗 VEEQO CHANNEL PRODUCT RELINKER');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    if (method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to update channel sellable
async function updateChannelSellable(productId, channelProductId, channelSellableId, newSellableId) {
  console.log(`\n🔗 Updating channel sellable...`);
  console.log(`   Product ID: ${productId}`);
  console.log(`   Channel Product ID: ${channelProductId}`);
  console.log(`   Channel Sellable ID: ${channelSellableId}`);
  console.log(`   New Sellable ID: ${newSellableId}`);
  
  const endpoint = `https://api.veeqo.com/products/${productId}/channel_products/${channelProductId}/channel_sellables/${channelSellableId}`;
  
  const data = {
    channel_sellable: {
      sellable_id: newSellableId
    }
  };
  
  console.log(`\n   Trying PUT ${endpoint}...`);
  const result = await makeVeeqoRequest(endpoint, 'PUT', data);
  
  if (result.success) {
    console.log(`   ✅ Successfully updated channel sellable!`);
    return { success: true, data: result.data };
  } else {
    console.log(`   ❌ Failed: ${result.error}`);
    
    // Try PATCH
    console.log(`\n   Trying PATCH ${endpoint}...`);
    const patchResult = await makeVeeqoRequest(endpoint, 'PATCH', data);
    
    if (patchResult.success) {
      console.log(`   ✅ Successfully updated channel sellable!`);
      return { success: true, data: patchResult.data };
    } else {
      console.log(`   ❌ Failed: ${patchResult.error}`);
      return { success: false, error: patchResult.error };
    }
  }
}

// Function to get product details
async function getProductDetails(productId) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!result.success) {
    console.error(`❌ Error fetching product: ${result.error}`);
    return null;
  }
  
  return result.data;
}

// Function to find sellable by SKU
async function findSellableBySku(sku) {
  console.log(`\n🔍 Searching for SKU: ${sku}`);
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products?query=${encodeURIComponent(sku)}`);
  
  if (!result.success) {
    console.error(`   ❌ Error searching: ${result.error}`);
    return null;
  }
  
  for (const product of result.data) {
    if (product.sellables) {
      const sellable = product.sellables.find(s => s.sku_code === sku);
      if (sellable) {
        console.log(`   ✅ Found: Product ID ${product.id}, Sellable ID ${sellable.id}`);
        return { product, sellable };
      }
    }
  }
  
  console.log(`   ❌ Not found`);
  return null;
}

// Main function
async function relinkChannelProduct(oldProductId, channelProductId, newSku) {
  console.log(`\n🔗 Relinking channel product ${channelProductId} from product ${oldProductId} to SKU ${newSku}`);
  console.log('─'.repeat(50));
  
  // Get old product details
  console.log(`\n📦 Fetching old product ${oldProductId}...`);
  const oldProduct = await getProductDetails(oldProductId);
  
  if (!oldProduct) {
    return false;
  }
  
  console.log(`   Title: ${oldProduct.title}`);
  
  // Find the channel product
  const channelProduct = oldProduct.channel_products?.find(cp => cp.id === parseInt(channelProductId));
  
  if (!channelProduct) {
    console.error(`❌ Channel product ${channelProductId} not found in product ${oldProductId}`);
    return false;
  }
  
  console.log(`\n📺 Channel Product:`);
  console.log(`   ID: ${channelProduct.id}`);
  console.log(`   Channel: ${channelProduct.channel?.name || 'Unknown'}`);
  console.log(`   Status: ${channelProduct.status}`);
  console.log(`   Remote Title: ${channelProduct.remote_title}`);
  
  // Find new sellable
  const newTarget = await findSellableBySku(newSku);
  
  if (!newTarget) {
    console.error(`❌ Could not find target SKU: ${newSku}`);
    return false;
  }
  
  console.log(`\n🎯 Target:`);
  console.log(`   Product ID: ${newTarget.product.id}`);
  console.log(`   Sellable ID: ${newTarget.sellable.id}`);
  console.log(`   Title: ${newTarget.product.title}`);
  console.log(`   SKU: ${newTarget.sellable.sku_code}`);
  
  // Update each channel sellable
  if (!channelProduct.channel_sellables || channelProduct.channel_sellables.length === 0) {
    console.error(`❌ No channel sellables found`);
    return false;
  }
  
  console.log(`\n🔄 Updating ${channelProduct.channel_sellables.length} channel sellable(s)...`);
  
  let successCount = 0;
  for (const channelSellable of channelProduct.channel_sellables) {
    console.log(`\n   Channel Sellable ID: ${channelSellable.id}`);
    console.log(`   Remote SKU: ${channelSellable.remote_sku}`);
    console.log(`   Current Sellable ID: ${channelSellable.sellable_id}`);
    
    const result = await updateChannelSellable(
      oldProductId,
      channelProductId,
      channelSellable.id,
      newTarget.sellable.id
    );
    
    if (result.success) {
      successCount++;
    }
  }
  
  if (successCount === channelProduct.channel_sellables.length) {
    console.log(`\n✅ Successfully relinked all channel sellables!`);
    return true;
  } else {
    console.log(`\n⚠️  Relinked ${successCount} of ${channelProduct.channel_sellables.length} channel sellables`);
    return false;
  }
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 3) {
  console.log(`
Usage: node relinkChannelProduct.cjs <old-product-id> <channel-product-id> <new-sku>

Example:
  node relinkChannelProduct.cjs 116629736 229745894 R13756

This will relink channel product 229745894 from product 116629736 to the product/sellable for R13756.
`);
  process.exit(1);
}

const oldProductId = args[0];
const channelProductId = args[1];
const newSku = args[2];

relinkChannelProduct(oldProductId, channelProductId, newSku)
  .then(success => {
    if (success) {
      console.log(`\n✅ All done!`);
      process.exit(0);
    } else {
      console.log(`\n❌ Relinking failed`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

