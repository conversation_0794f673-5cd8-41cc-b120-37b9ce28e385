-- =====================================================
-- Materialized View Refresh Tracking System
-- Generic system for tracking when any MV was last refreshed
-- =====================================================

-- 1. Create tracking table (works for any MV)
CREATE TABLE IF NOT EXISTS t_mv_refresh_log (
  mv_name TEXT PRIMARY KEY,
  last_refreshed_at TIMESTAMPTZ,
  refresh_started_at TIMESTAMPTZ,
  refresh_duration_ms INTEGER,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add comment
COMMENT ON TABLE t_mv_refresh_log IS 'Tracks when materialized views were last refreshed';
COMMENT ON COLUMN t_mv_refresh_log.refresh_started_at IS 'Set when refresh begins, cleared when complete. Used to prevent concurrent refreshes.';

-- =====================================================
-- 2. Create refresh function for mv_dgacc_informed_pricing_calculations
-- This logs the refresh time and duration
-- =====================================================
CREATE OR REPLACE FUNCTION refresh_mv_dgacc_informed_pricing()
RETURNS void AS $$
DECLARE
  start_time TIMESTAMPTZ;
  duration_ms INTEGER;
BEGIN
  start_time := clock_timestamp();

  -- Mark refresh as started
  INSERT INTO t_mv_refresh_log (mv_name, refresh_started_at, updated_at)
  VALUES ('mv_dgacc_informed_pricing_calculations', now(), now())
  ON CONFLICT (mv_name) DO UPDATE SET
    refresh_started_at = now(),
    updated_at = now();

  -- Refresh the materialized view
  REFRESH MATERIALIZED VIEW mv_dgacc_informed_pricing_calculations;

  -- Calculate duration
  duration_ms := EXTRACT(MILLISECONDS FROM clock_timestamp() - start_time)::INTEGER;

  -- Log the refresh completion (clear refresh_started_at)
  UPDATE t_mv_refresh_log SET
    last_refreshed_at = now(),
    refresh_started_at = NULL,
    refresh_duration_ms = duration_ms,
    updated_at = now()
  WHERE mv_name = 'mv_dgacc_informed_pricing_calculations';

  RAISE NOTICE 'Refreshed mv_dgacc_informed_pricing_calculations in % ms', duration_ms;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 3. Create function to check staleness and refresh if needed
-- Returns: 'fresh' | 'refreshed' | 'refresh_in_progress'
-- =====================================================
CREATE OR REPLACE FUNCTION ensure_mv_dgacc_pricing_fresh(min_refresh_after TIMESTAMPTZ)
RETURNS TEXT AS $$
DECLARE
  last_refresh TIMESTAMPTZ;
  refresh_started TIMESTAMPTZ;
BEGIN
  -- Get the last refresh time and in-progress status
  SELECT last_refreshed_at, refresh_started_at
  INTO last_refresh, refresh_started
  FROM t_mv_refresh_log
  WHERE mv_name = 'mv_dgacc_informed_pricing_calculations';

  -- Check if a refresh is already in progress (started within last 5 minutes)
  IF refresh_started IS NOT NULL AND refresh_started > now() - INTERVAL '5 minutes' THEN
    RAISE NOTICE 'MV refresh already in progress (started: %)', refresh_started;
    RETURN 'refresh_in_progress';
  END IF;

  -- If never refreshed or refresh was before the required time, refresh now
  IF (last_refresh IS NULL) OR (last_refresh < min_refresh_after) THEN
    RAISE NOTICE 'MV is stale (last refresh: %, required after: %). Refreshing...',
      COALESCE(last_refresh::text, 'never'), min_refresh_after;
    PERFORM refresh_mv_dgacc_informed_pricing();
    RETURN 'refreshed';
  END IF;

  RAISE NOTICE 'MV is fresh (last refresh: %, required after: %)', last_refresh, min_refresh_after;
  RETURN 'fresh';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 4. Grant permissions
-- =====================================================
GRANT SELECT, INSERT, UPDATE ON t_mv_refresh_log TO authenticated;
GRANT SELECT, INSERT, UPDATE ON t_mv_refresh_log TO service_role;
GRANT EXECUTE ON FUNCTION refresh_mv_dgacc_informed_pricing() TO service_role;
GRANT EXECUTE ON FUNCTION ensure_mv_dgacc_pricing_fresh(TIMESTAMPTZ) TO service_role;

-- =====================================================
-- NOTES:
-- 
-- Before running this, you need to create the materialized view:
--
-- CREATE MATERIALIZED VIEW mv_dgacc_informed_pricing_calculations AS
--   SELECT * FROM v_dgacc_informed_pricing_calculations;
--
-- CREATE UNIQUE INDEX idx_mv_dgacc_pricing_listing_sku 
--   ON mv_dgacc_informed_pricing_calculations(listing_sku);
--
-- The UNIQUE index is required for CONCURRENT refresh.
-- =====================================================

