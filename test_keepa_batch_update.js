import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Test the batch update logic with a simulated ASIN list
 */
async function testBatchUpdate() {
  console.log('=== Testing Keepa Batch Update Logic ===\n');
  
  // Get some real ASINs from the database to test with
  const { data: realAsins, error: fetchError } = await supabase
    .from('t_sdasins')
    .select('asin')
    .not('asin', 'is', null)
    .limit(1200); // Get 1200 to test chunking (will process in 3 chunks of 500)
  
  if (fetchError) {
    console.error('Error fetching ASINs:', fetchError);
    return;
  }
  
  console.log(`Fetched ${realAsins.length} real ASINs from database for testing\n`);
  
  // Create a simulated ASIN list (mix of existing and fake new ones)
  const asinList = [
    ...realAsins.map(r => r.asin),
    'B0TESTFAKE1', // Fake ASINs that don't exist
    'B0TESTFAKE2',
    'B0TESTFAKE3'
  ];
  
  console.log(`Total test ASIN list: ${asinList.length} ASINs`);
  console.log(`  - ${realAsins.length} existing ASINs`);
  console.log(`  - 3 fake new ASINs\n`);
  
  const currentTimestamp = new Date().toISOString();
  let updatedCount = 0;
  let newAsinCount = 0;
  
  // Step 1: Fetch existing ASINs in chunks
  console.log('Step 1: Fetching existing ASINs from database...');
  const existingAsinSet = new Set();
  const chunkSize = 500;
  
  for (let i = 0; i < asinList.length; i += chunkSize) {
    const chunk = asinList.slice(i, i + chunkSize);
    console.log(`  Fetching chunk ${Math.floor(i / chunkSize) + 1}/${Math.ceil(asinList.length / chunkSize)} (${chunk.length} ASINs)...`);
    
    const { data: existingAsins, error } = await supabase
      .from('t_sdasins')
      .select('asin')
      .in('asin', chunk);
    
    if (error) {
      console.error('Error:', error);
      return;
    }
    
    if (existingAsins) {
      existingAsins.forEach(record => existingAsinSet.add(record.asin));
    }
  }
  
  console.log(`✓ Found ${existingAsinSet.size} existing ASINs\n`);
  
  // Step 2: Separate into update vs insert
  const asinsToUpdate = [];
  const asinsToInsert = [];
  
  asinList.forEach((asin, index) => {
    const rank = index + 1;
    if (existingAsinSet.has(asin)) {
      asinsToUpdate.push({ asin, rank });
    } else {
      asinsToInsert.push({ asin, rank });
    }
  });
  
  console.log(`ASINs to update: ${asinsToUpdate.length}`);
  console.log(`New ASINs to insert: ${asinsToInsert.length}\n`);
  
  // Step 3: Batch update existing ASINs
  if (asinsToUpdate.length > 0) {
    console.log('Step 2: Updating existing ASINs using batch SQL...');
    const updateBatchSize = 500;
    
    for (let i = 0; i < asinsToUpdate.length; i += updateBatchSize) {
      const batch = asinsToUpdate.slice(i, i + updateBatchSize);
      console.log(`  Updating batch ${Math.floor(i / updateBatchSize) + 1}/${Math.ceil(asinsToUpdate.length / updateBatchSize)} (${batch.length} ASINs)...`);
      
      const whenClauses = batch.map(({ asin, rank }) =>
        `WHEN asin = '${asin}' THEN ${rank}`
      ).join(' ');
      
      const asinList_sql = batch.map(({ asin }) => `'${asin}'`).join(', ');
      
      const updateSql = `
        UPDATE t_sdasins
        SET
          so_rank_30day_avg = CASE ${whenClauses} END,
          so_rank_30day_avg_date = '${currentTimestamp}'
        WHERE asin IN (${asinList_sql})
      `;
      
      const { error } = await supabase.rpc('exec_sql', { sql_query: updateSql });
      
      if (error) {
        console.error('Error:', error);
        return;
      }
      
      updatedCount += batch.length;
      console.log(`  ✓ Updated ${batch.length} ASINs`);
    }
    
    console.log(`✓ Successfully updated ${updatedCount} ASINs total\n`);
  }
  
  // Step 4: Insert new ASINs
  if (asinsToInsert.length > 0) {
    console.log(`Step 3: Inserting ${asinsToInsert.length} new ASINs...`);
    
    const recordsToInsert = asinsToInsert.map(({ asin, rank }) => ({
      asin: asin,
      notes: `TEST: Competitor product (Rank ${rank})`,
      so_rank_30day_avg: rank,
      so_rank_30day_avg_date: currentTimestamp
    }));
    
    const { data, error } = await supabase
      .from('t_sdasins')
      .insert(recordsToInsert)
      .select('id');
    
    if (error) {
      console.error('Error inserting:', error);
    } else {
      newAsinCount = data.length;
      console.log(`✓ Inserted ${newAsinCount} new ASINs\n`);
    }
  }
  
  console.log('=== Test Complete ===');
  console.log(`Total processed: ${asinList.length}`);
  console.log(`Updated: ${updatedCount}`);
  console.log(`Inserted: ${newAsinCount}`);
}

testBatchUpdate().then(() => process.exit(0)).catch(err => {
  console.error('Test failed:', err);
  process.exit(1);
});

