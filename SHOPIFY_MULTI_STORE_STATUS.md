# Shopify Multi-Store Implementation Status

## ✅ COMPLETED FILES

### Core Infrastructure
1. **shopifyStoreConfig.js** - Central credential management
2. **shopifyGraphQL.js** - All utility functions updated with `storeId` parameter

### Task Handlers - Fully Updated
1. **processUpdateShopifyProductTitleTask.js** ✅
2. **processUpdateShopifyProductDescriptionTask.js** ✅
3. **processDeleteSkuFromShopifyTask.js** ✅
4. **processDeleteVariantFromShopifyTask.js** ✅

## 🔄 REMAINING TASK HANDLERS

All remaining handlers follow the same pattern. For each file:

### Pattern to Apply:
1. Add import: `import { getShopifyCredentials } from './shopifyStoreConfig.js';`
2. Remove hardcoded credentials (if any)
3. In main handler function:
   - Extract `shopify_store_id` from payload
   - Add: `const storeId = shopify_store_id || 1;`
   - Add: `const { storeName } = getShopifyCredentials(storeId);`
   - Pass `storeId` to all Shopify API calls
   - Include `store: storeName, store_id: storeId` in completion/error results

### Files Needing Updates:

#### 1. processUpdateDiscVariantPriceOnShopifyTask.js
- **What it does:** Updates variant price on Shopify
- **Shopify calls:** Uses Shopify GraphQL/REST API
- **Priority:** HIGH (price updates are critical)

#### 2. processUpdateDiscVariantMsrpOnShopifyTask.js
- **What it does:** Updates variant MSRP (compare-at price) on Shopify
- **Shopify calls:** Uses Shopify GraphQL/REST API
- **Priority:** HIGH (price updates are critical)

#### 3. processSyncProductVariantToShopifyTask.js
- **What it does:** Syncs product variant data to Shopify
- **Shopify calls:** Uses Shopify GraphQL/REST API
- **Priority:** HIGH (core sync functionality)

#### 4. processShopifySaleStartTask.js
- **What it does:** Applies sale prices to Shopify products
- **Shopify calls:** Uses Shopify GraphQL/REST API
- **Priority:** HIGH (time-sensitive sales)
- **Note:** Uses `findVariantBySku` from shopifyGraphQL.js (already updated)

#### 5. processShopifySaleEndTask.js
- **What it does:** Removes sale prices from Shopify products
- **Shopify calls:** Uses Shopify GraphQL/REST API
- **Priority:** HIGH (time-sensitive sales)
- **Note:** Uses `findVariantBySku` from shopifyGraphQL.js (already updated)

#### 6. processDiscSoldOrUnsoldUpdateShopifyDirectlyTask.js
- **What it does:** Updates Shopify inventory when disc is sold/unsold
- **Shopify calls:** Uses Shopify GraphQL/REST API
- **Priority:** MEDIUM (inventory management)

#### 7. processDiscUpdatedDeleteFromShopifyTask.js
- **What it does:** Deletes disc from Shopify when updated
- **Shopify calls:** Uses Shopify GraphQL/REST API
- **Priority:** MEDIUM

#### 8. processUpdateShopifyDiscTitleTask.js
- **What it does:** Updates disc title on Shopify
- **Shopify calls:** Uses Shopify GraphQL/REST API
- **Priority:** MEDIUM

#### 9. processClearShopifyCountForSoldDiscTask.js
- **What it does:** Clears Shopify inventory count for sold discs
- **Shopify calls:** Uses Shopify GraphQL/REST API
- **Priority:** MEDIUM

#### 10. processFixOslShopifyProductWo3OptionsTask.js
- **What it does:** Fixes OSL products without 3 options
- **Shopify calls:** Uses Shopify GraphQL/REST API
- **Priority:** LOW (maintenance task)

## 📝 Quick Reference for Each File

### For files that use shopifyGraphQL.js functions:
- `findVariantBySku(sku)` → `findVariantBySku(sku, storeId)`
- `setInventoryItemQuantity(id, qty)` → `setInventoryItemQuantity(id, qty, storeId)`
- `setProductInventoryToZero(id)` → `setProductInventoryToZero(id, storeId)`
- `executeShopifyGraphQL(query, vars)` → `executeShopifyGraphQL(query, vars, storeId)`

### For files with local Shopify API calls:
- Replace `process.env.SHOPIFY_ENDPOINT` with `getShopifyCredentials(storeId).endpoint`
- Replace `process.env.SHOPIFY_ACCESS_TOKEN` with `getShopifyCredentials(storeId).accessToken`

## 🎯 Testing After Updates

For each updated file, test with:
1. Task with NO `shopify_store_id` → should use DZ Discs (store 1)
2. Task with `shopify_store_id: 2` → should use Tippmann Parts

## 📊 Progress Tracker

- Total Shopify handlers: 14
- Completed: 4 (29%)
- Remaining: 10 (71%)

**Estimated time per file:** 5-10 minutes
**Total remaining time:** ~1-2 hours

## 🚀 Next Steps

1. Update high-priority handlers first (price updates, sync, sales)
2. Test each handler after updating
3. Update medium-priority handlers
4. Update low-priority handlers
5. Run comprehensive test suite
6. Update documentation

## ⚠️ Important Notes

- All updates are **backward compatible**
- Existing tasks without `shopify_store_id` will continue to work
- Only new tasks with `shopify_store_id: 2` will use Tippmann Parts
- No database changes required
- No trigger changes required (already done)

