import 'dotenv/config';
import fs from 'fs';
import path from 'path';
import { parse as csvParse } from 'csv-parse/sync';
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY in environment');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

function parseMysteryPacksCsv(filePath) {
  const raw = fs.readFileSync(filePath, 'utf8');
  const records = csvParse(raw, { columns: true, skip_empty_lines: true, trim: true });
  const ids = [];
  for (const r of records) {
    const discStr = r.Disc || '';
    const m = discStr.match(/#(\d{6})/);
    if (m) ids.push(Number(m[1]));
  }
  return Array.from(new Set(ids));
}

async function markSold(ids) {
  if (!ids.length) {
    console.log('No IDs parsed from CSV. Nothing to update.');
    return { updatedIds: [], missingIds: [] };
  }
  const idList = ids.join(',');
  const sql = `
    UPDATE t_discs
       SET sold_date = NOW(),
           sold_channel = 'DZDiscs'
     WHERE id IN (${idList})
  RETURNING id;
  `;
  const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
  if (error) throw new Error(`exec_sql failed: ${error.message}`);
  let updatedIds = (data || []).map(r => Number(r.id));

  // Fallback: if nothing updated, try direct table update API (in case exec_sql has restrictions)
  if (updatedIds.length === 0) {
    const { data: upd2, error: err2 } = await supabase
      .from('t_discs')
      .update({ sold_date: new Date().toISOString(), sold_channel: 'DZDiscs' })
      .in('id', ids)
      .select('id');
    if (err2) throw new Error(`direct update failed: ${err2.message}`);
    updatedIds = (upd2 || []).map(r => Number(r.id));
  }

  const updatedSet = new Set(updatedIds);
  const missingIds = ids.filter(id => !updatedSet.has(id));
  return { updatedIds, missingIds };
}

async function main() {
  const csvPath = path.join('data', 'external data', 'delete', 'mysterypacks.csv');
  if (!fs.existsSync(csvPath)) {
    console.error(`CSV not found at ${csvPath}`);
    process.exit(1);
  }

  const ids = parseMysteryPacksCsv(csvPath);
  console.log(`Parsed ${ids.length} disc IDs from CSV.`);

  try {
    const { updatedIds, missingIds } = await markSold(ids);
    console.log(`Updated ${updatedIds.length} discs with sold_date set and sold_channel='DZDiscs'.`);
    if (missingIds.length) {
      console.warn(`Warning: ${missingIds.length} IDs not found or not updated:`);
      console.warn(missingIds.join(','));
    }
  } catch (e) {
    console.error('Update failed:', e.message);
    process.exit(1);
  }
}

main().catch(err => {
  console.error('Fatal error:', err.message);
  process.exit(1);
});

