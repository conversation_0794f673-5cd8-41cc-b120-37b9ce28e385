import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function testBatchUpdate() {
  console.log('Testing batch update approach...\n');
  
  // Get a small sample of existing ASINs to test with
  const { data: sampleAsins, error: fetchError } = await supabase
    .from('t_sdasins')
    .select('asin')
    .not('asin', 'is', null)
    .limit(5);
  
  if (fetchError) {
    console.error('Error fetching sample ASINs:', fetchError);
    return;
  }
  
  if (!sampleAsins || sampleAsins.length === 0) {
    console.log('No ASINs found in database');
    return;
  }
  
  console.log(`Found ${sampleAsins.length} sample ASINs to test with`);
  
  // Create test data
  const testUpdates = sampleAsins.map((record, index) => ({
    asin: record.asin,
    rank: 1000 + index // Test ranks
  }));
  
  console.log('Test ASINs:', testUpdates.map(u => `${u.asin} -> rank ${u.rank}`).join(', '));
  
  // Build the batch update SQL
  const currentTimestamp = new Date().toISOString();
  const whenClauses = testUpdates.map(({ asin, rank }) => 
    `WHEN asin = '${asin}' THEN ${rank}`
  ).join(' ');
  
  const asinList = testUpdates.map(({ asin }) => `'${asin}'`).join(', ');
  
  const updateSql = `
    UPDATE t_sdasins
    SET 
      so_rank_30day_avg = CASE ${whenClauses} END,
      so_rank_30day_avg_date = '${currentTimestamp}'
    WHERE asin IN (${asinList})
  `;
  
  console.log('\nGenerated SQL:');
  console.log(updateSql);
  console.log('\nExecuting batch update...');
  
  try {
    const { data, error } = await supabase.rpc('exec_sql', {
      sql_query: updateSql
    });
    
    if (error) {
      console.error('Error executing batch update:', error);
      return;
    }
    
    console.log('✓ Batch update successful!');
    console.log('Response:', data);
    
    // Verify the updates
    console.log('\nVerifying updates...');
    const { data: verifyData, error: verifyError } = await supabase
      .from('t_sdasins')
      .select('asin, so_rank_30day_avg, so_rank_30day_avg_date')
      .in('asin', testUpdates.map(u => u.asin));
    
    if (verifyError) {
      console.error('Error verifying:', verifyError);
      return;
    }
    
    console.log('Updated records:');
    verifyData.forEach(record => {
      console.log(`  ${record.asin}: rank ${record.so_rank_30day_avg}, updated ${record.so_rank_30day_avg_date}`);
    });
    
  } catch (err) {
    console.error('Exception:', err);
  }
}

testBatchUpdate().then(() => process.exit(0));

