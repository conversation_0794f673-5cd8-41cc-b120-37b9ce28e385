const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔧 UPDATE ORPHANED CHANNEL SELLABLES');
console.log('='.repeat(60));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // 204 No Content is a success response with no body
    if (response.status === 204 || method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to get product details
async function getProductDetails(productId) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!result.success) {
    console.error(`❌ Error fetching product: ${result.error}`);
    return null;
  }
  
  return result.data;
}

// Function to update channel sellable
async function updateChannelSellable(channelSellableId, newSellableId) {
  const data = {
    channel_sellable: {
      sellable_id: newSellableId
    }
  };
  
  const result = await makeVeeqoRequest(
    `https://api.veeqo.com/channel_sellables/${channelSellableId}`,
    'PUT',
    data
  );
  
  return result;
}

// Main function
async function updateOrphanedChannelSellables(productId, remoteSku, correctSellableId) {
  console.log(`\n🔧 Updating orphaned channel sellables in product ${productId}`);
  console.log(`   Remote SKU: ${remoteSku}`);
  console.log(`   Target Sellable ID: ${correctSellableId}`);
  console.log('─'.repeat(60));
  
  // Get product details
  console.log(`\n📦 Fetching product ${productId}...`);
  const product = await getProductDetails(productId);
  
  if (!product) {
    return false;
  }
  
  console.log(`   Title: ${product.title}`);
  
  // Find all channel sellables with the specified remote_sku
  const channelSellablesToUpdate = [];
  
  if (product.channel_products) {
    for (const channelProduct of product.channel_products) {
      if (channelProduct.channel_sellables) {
        for (const channelSellable of channelProduct.channel_sellables) {
          if (channelSellable.remote_sku === remoteSku) {
            channelSellablesToUpdate.push({
              channelProduct,
              channelSellable
            });
          }
        }
      }
    }
  }
  
  console.log(`\n📊 Found ${channelSellablesToUpdate.length} channel sellable(s) with remote_sku=${remoteSku}`);
  
  if (channelSellablesToUpdate.length === 0) {
    console.log(`✅ No channel sellables found with remote_sku=${remoteSku}`);
    return true;
  }
  
  // Update each channel sellable
  let successCount = 0;
  for (const item of channelSellablesToUpdate) {
    const { channelProduct, channelSellable } = item;
    
    console.log(`\n📺 Channel Product: ${channelProduct.id} - ${channelProduct.remote_title}`);
    console.log(`   Channel Sellable: ${channelSellable.id}`);
    console.log(`   Remote SKU: ${channelSellable.remote_sku}`);
    console.log(`   Current Sellable ID: ${channelSellable.sellable_id}`);
    console.log(`   Updating to: ${correctSellableId}`);
    
    const result = await updateChannelSellable(
      channelSellable.id,
      correctSellableId
    );
    
    if (result.success) {
      console.log(`   ✅ Updated successfully`);
      successCount++;
    } else {
      console.log(`   ❌ Failed: ${result.error}`);
    }
  }
  
  console.log(`\n${'='.repeat(60)}`);
  console.log(`📊 Summary: ${successCount} of ${channelSellablesToUpdate.length} channel sellables updated`);
  console.log(`${'='.repeat(60)}`);
  
  return successCount === channelSellablesToUpdate.length;
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 3) {
  console.log(`
Usage: node updateOrphanedToCorrectSellable.cjs <product-id> <remote-sku> <correct-sellable-id>

Example:
  node updateOrphanedToCorrectSellable.cjs 86512012 FM13953 191469738

This will find all channel sellables in product 86512012 with remote_sku=FM13953
and update them to point to sellable 191469738.

Use this when you've merged a variant and the old SKU's channel sellables
need to be updated to point to the new variant's sellable.
`);
  process.exit(1);
}

const productId = args[0];
const remoteSku = args[1];
const correctSellableId = parseInt(args[2]);

updateOrphanedChannelSellables(productId, remoteSku, correctSellableId)
  .then(success => {
    if (success) {
      console.log(`\n✅ All done!`);
      process.exit(0);
    } else {
      console.log(`\n⚠️  Some updates failed`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

