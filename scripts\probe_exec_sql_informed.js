import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY in .env');
  process.exit(2);
}

const supabase = createClient(supabaseUrl, supabaseKey);

function logShape(label, data) {
  const shape = {
    type: typeof data,
    isArray: Array.isArray(data),
    keys: data && typeof data === 'object' ? Object.keys(data).slice(0, 10) : undefined,
  };
  console.log(label, shape);
}

async function run() {
  console.log('Supabase URL host:', new URL(supabaseUrl).host);

  // 1) Check if tables exist in information_schema.tables
  const tblSql = `
    select table_schema, table_name
    from information_schema.tables
    where table_schema = 'public'
      and table_name in ('it_infor_all_fields','it_infor_competition_landscape','it_infor_no_buy_box')
    order by table_name
  `;
  let resp = await supabase.rpc('exec_sql', { sql_query: tblSql });
  console.log('\nTables query error:', resp.error?.message || null);
  logShape('Tables query data shape:', resp.data);
  console.log('Tables query data sample:', JSON.stringify(resp.data, null, 2).slice(0, 800));

  // 2) Fetch columns for one table via information_schema.columns
  const colSql = `
    select column_name
    from information_schema.columns
    where table_schema='public' and table_name='it_infor_all_fields'
    order by ordinal_position
  `;
  resp = await supabase.rpc('exec_sql', { sql_query: colSql });
  console.log('\nColumns query error:', resp.error?.message || null);
  logShape('Columns query data shape:', resp.data);
  console.log('Columns query data sample:', JSON.stringify(resp.data, null, 2).slice(0, 800));

  // 3) Fallback via pg_attribute
  const fbSql = `
    select a.attname as column_name
    from pg_attribute a
    join pg_class c on c.oid = a.attrelid
    join pg_namespace n on n.oid = c.relnamespace
    where n.nspname='public' and c.relname='it_infor_all_fields'
      and a.attnum > 0 and not a.attisdropped
    order by a.attnum
  `;
  resp = await supabase.rpc('exec_sql', { sql_query: fbSql });
  console.log('\nFallback pg_attribute error:', resp.error?.message || null);
  logShape('Fallback data shape:', resp.data);
  console.log('Fallback data sample:', JSON.stringify(resp.data, null, 2).slice(0, 800));

  // 4) Direct table select to test access and existence (no schema introspection needed)
  const tables = ['it_infor_all_fields','it_infor_competition_landscape','it_infor_no_buy_box'];
  for (const t of tables) {
    const { data, error } = await supabase.from(t).select('*').limit(1);
    console.log(`\nDirect select from ${t}: error=`, error?.message || null, ' rows=', Array.isArray(data) ? data.length : null);
    if (Array.isArray(data) && data.length) {
      console.log(`Sample keys for ${t}:`, Object.keys(data[0]).slice(0, 10));
    }
  }
}

run().catch(err => {
  console.error('Probe error:', err?.message || err);
  process.exit(1);
});
