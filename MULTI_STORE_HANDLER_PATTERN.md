# Multi-Store Shopify Handler Pattern

This document describes the pattern for updating Shopify task handlers to support multiple stores with backward compatibility.

## Pattern Overview

All Shopify task handlers should:
1. Import `getShopifyCredentials` from `shopifyStoreConfig.js`
2. Extract `shopify_store_id` from payload, defaulting to `1` (DZ Discs) if not provided
3. Get credentials using `getShopifyCredentials(storeId)`
4. Pass `storeId` to all Shopify API functions
5. Include `store` and `store_id` in completion/error results

## Code Pattern

### 1. Import Statement
```javascript
import { getShopifyCredentials } from './shopifyStoreConfig.js';
```

### 2. Remove Hardcoded Credentials
**BEFORE:**
```javascript
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
```

**AFTER:**
Remove these lines - credentials will be fetched dynamically

### 3. Update Helper Functions
**BEFORE:**
```javascript
async function shopifyGraphQLRequest(query, variables = {}) {
  const response = await fetch(shopifyEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken,
    },
    body: JSON.stringify({ query, variables }),
  });
  // ...
}
```

**AFTER:**
```javascript
async function shopifyGraphQLRequest(query, variables = {}, storeId = 1) {
  const { endpoint, accessToken } = getShopifyCredentials(storeId);
  
  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': accessToken,
    },
    body: JSON.stringify({ query, variables }),
  });
  // ...
}
```

### 4. Update Main Handler Function
**BEFORE:**
```javascript
export default async function processTaskName(task, { supabase, updateTaskStatus, logError }) {
  try {
    await updateTaskStatus(task.id, 'processing');
    
    const payload = task.payload;
    const { someField } = payload;
    
    // ... do work ...
    
    await updateTaskStatus(task.id, 'completed', {
      message: 'Task completed',
      someField
    });
  } catch (error) {
    await updateTaskStatus(task.id, 'error', {
      message: error.message
    });
  }
}
```

**AFTER:**
```javascript
export default async function processTaskName(task, { supabase, updateTaskStatus, logError }) {
  try {
    await updateTaskStatus(task.id, 'processing');
    
    const payload = task.payload;
    const { someField, shopify_store_id } = payload;
    const storeId = shopify_store_id || 1; // Default to DZ Discs for backward compatibility
    const { storeName } = getShopifyCredentials(storeId);
    
    console.log(`[processTaskName] Processing for ${storeName} (store ${storeId})`);
    
    // ... do work, passing storeId to all Shopify functions ...
    
    await updateTaskStatus(task.id, 'completed', {
      message: `Task completed on ${storeName}`,
      someField,
      store: storeName,
      store_id: storeId
    });
  } catch (error) {
    const storeId = task.payload?.shopify_store_id || 1;
    const { storeName } = getShopifyCredentials(storeId);
    
    await updateTaskStatus(task.id, 'error', {
      message: `Error on ${storeName}: ${error.message}`,
      store: storeName,
      store_id: storeId
    });
  }
}
```

## Files to Update

### ✅ Completed
- `processUpdateShopifyProductTitleTask.js`
- `processUpdateShopifyProductDescriptionTask.js`
- `processDeleteSkuFromShopifyTask.js`

### 🔄 Remaining
- `processDeleteVariantFromShopifyTask.js`
- `processUpdateDiscVariantPriceOnShopifyTask.js`
- `processUpdateDiscVariantMsrpOnShopifyTask.js`
- `processSyncProductVariantToShopifyTask.js`
- `processShopifySaleStartTask.js`
- `processShopifySaleEndTask.js`
- `processDiscSoldOrUnsoldUpdateShopifyDirectlyTask.js`
- `processDiscUpdatedDeleteFromShopifyTask.js`
- `processFixOslShopifyProductWo3OptionsTask.js`
- `processUpdateShopifyDiscTitleTask.js`
- `processClearShopifyCountForSoldDiscTask.js`

## Testing

After updating each handler:
1. Verify no syntax errors
2. Test with a task that has NO `shopify_store_id` (should default to store 1)
3. Test with a task that has `shopify_store_id: 2` (should use Tippmann Parts)
4. Verify completion/error messages include store information

