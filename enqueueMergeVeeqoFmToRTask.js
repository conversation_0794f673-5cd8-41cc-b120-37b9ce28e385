// enqueueMergeVeeqoFmToRTask.js - Enqueue a task to merge Veeqo FM SKU to R SKU
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function enqueueMergeTask(oldSku, newSku) {
  console.log('📋 ENQUEUE MERGE VEEQO FM TO R TASK');
  console.log('='.repeat(70));
  console.log(`   Old SKU: ${oldSku}`);
  console.log(`   New SKU: ${newSku}`);
  console.log('');

  try {
    const now = new Date();
    
    // Create the payload
    const payload = {
      old_sku: oldSku,
      new_sku: newSku
    };

    console.log(`📦 Payload:`, JSON.stringify(payload, null, 2));

    // Insert the task into the queue
    const { data, error } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'merge_veeqo_fm_to_r',
        payload: payload,
        status: 'pending',
        scheduled_at: now.toISOString(),
        created_at: now.toISOString(),
        enqueued_by: `manual_merge_${oldSku}_to_${newSku}`
      })
      .select();

    if (error) {
      console.error(`❌ Error enqueueing task: ${error.message}`);
      return { success: false, error: error.message };
    }

    console.log(`✅ Task enqueued successfully!`);
    console.log(`   Task ID: ${data[0].id}`);
    console.log(`   Status: ${data[0].status}`);
    console.log(`   Scheduled at: ${data[0].scheduled_at}`);
    console.log('');
    console.log('🔄 The worker will pick up this task and process it automatically.');
    
    return { success: true, taskId: data[0].id };

  } catch (err) {
    console.error(`❌ Exception: ${err.message}`);
    return { success: false, error: err.message };
  }
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 2) {
  console.log(`
Usage: node enqueueMergeVeeqoFmToRTask.js <old-sku> <new-sku>

Arguments:
  old-sku   - The SKU of the variant to merge from (e.g., FM11937_B01633ZA48)
  new-sku   - The SKU of the variant to merge to (e.g., R11937)

Example:
  node enqueueMergeVeeqoFmToRTask.js FM11937_B01633ZA48 R11937
`);
  process.exit(1);
}

const oldSku = args[0];
const newSku = args[1];

// Run the enqueue function
enqueueMergeTask(oldSku, newSku)
  .then(result => {
    if (result.success) {
      console.log('✅ Done!');
      process.exit(0);
    } else {
      console.error('❌ Failed to enqueue task');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error(`❌ Unhandled error: ${err.message}`);
    process.exit(1);
  });

