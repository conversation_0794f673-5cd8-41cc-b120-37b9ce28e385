// enqueueRproCostPriceUpdatedTask.js
// Helper script to manually enqueue an rpro_cost_price_updated task

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function enqueueRproCostPriceUpdatedTask(ivno) {
  try {
    console.log(`\n🔍 Enqueuing rpro_cost_price_updated task for ivno: ${ivno}`);

    // Validate the ivno exists and check how many FM listings will be affected
    console.log(`\n🔍 Checking ivno "${ivno}" for FM listings uploaded to Amazon...`);

    const { data: affectedListings, error: listingsError } = await supabase
      .from('v_rpro_informed_pricing_calculations')
      .select('listing_sku, rpro_ivno')
      .eq('rpro_ivno', ivno)
      .not('listing_sku', 'is', null)
      .like('listing_sku', 'FM%');

    if (listingsError) {
      console.error('❌ Error looking up affected listings:', listingsError);
      return;
    }

    if (!affectedListings || affectedListings.length === 0) {
      console.log(`⚠️  No FM listings found for rpro_ivno ${ivno}`);
      console.log('This task will complete immediately without enqueueing any update_informed_pricing tasks.');
    } else {
      console.log(`✅ Found ${affectedListings.length} FM listing(s) for rpro_ivno ${ivno}`);

      // Check which ones have been uploaded to Amazon
      const listingSkus = affectedListings.map(l => l.listing_sku);

      const { data: uploadedListings, error: uploadedError } = await supabase
        .from('t_amaz_acc_listings')
        .select('listing_sku')
        .in('listing_sku', listingSkus)
        .not('uploaded_to_amazon_at', 'is', null);

      if (uploadedError) {
        console.error('❌ Error checking uploaded listings:', uploadedError);
        return;
      }

      if (!uploadedListings || uploadedListings.length === 0) {
        console.log(`⚠️  None of the FM listings have been uploaded to Amazon yet`);
        console.log('This task will complete immediately without enqueueing any update_informed_pricing tasks.');
      } else {
        const uploadedListingSkus = new Set(uploadedListings.map(l => l.listing_sku));
        const listingsToUpdate = affectedListings.filter(l => uploadedListingSkus.has(l.listing_sku));

        console.log(`✅ Found ${listingsToUpdate.length} FM listing(s) uploaded to Amazon (out of ${affectedListings.length} total):`);
        listingsToUpdate.forEach(listing => {
          console.log(`   - ${listing.listing_sku}`);
        });
        console.log(`\n💡 This will enqueue ${listingsToUpdate.length} update_informed_pricing task(s).`);
        console.log('   All update_informed_pricing tasks will be batched together in a single upload.');
      }
    }

    // Enqueue the task
    console.log(`\n📝 Enqueueing rpro_cost_price_updated task...`);
    
    const { data: task, error: enqueueError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'rpro_cost_price_updated',
        payload: {
          ivno: ivno
        },
        status: 'pending',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'manual'
      })
      .select()
      .single();

    if (enqueueError) {
      console.error('❌ Error enqueueing task:', enqueueError);
      return;
    }

    console.log(`\n✅ Task enqueued successfully!`);
    console.log(`   Task ID: ${task.id}`);
    console.log(`   Status: ${task.status}`);
    console.log(`   Scheduled at: ${task.scheduled_at}`);
    console.log('\n💡 The task queue worker will process this task automatically.');
    console.log('   It will enqueue update_informed_pricing tasks for FM listings uploaded to Amazon.');
    console.log('   All update_informed_pricing tasks will be batched together in a single upload.');

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Get ivno from command line arguments
const ivno = process.argv[2];

if (!ivno) {
  console.error('❌ Usage: node enqueueRproCostPriceUpdatedTask.js <ivno>');
  console.error('   Example: node enqueueRproCostPriceUpdatedTask.js R12345');
  process.exit(1);
}

enqueueRproCostPriceUpdatedTask(ivno);

