// testUpdateInformedPricing.js
// Test script to enqueue and verify update_informed_pricing task

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testUpdateInformedPricing() {
  console.log('🧪 Testing update_informed_pricing task handler');
  console.log('='.repeat(60));

  try {
    // Step 1: Check if v_rpro_size_tier_pricing view exists and has data
    console.log('\n📊 Step 1: Checking v_rpro_size_tier_pricing view...');
    
    const { data: viewData, error: viewError } = await supabase
      .from('v_rpro_size_tier_pricing')
      .select('listing_sku, sku, map_price, max_price_for_informed, cost_for_informed')
      .limit(5);

    if (viewError) {
      console.error('❌ Error querying v_rpro_size_tier_pricing:', viewError);
      return;
    }

    if (!viewData || viewData.length === 0) {
      console.error('❌ No data found in v_rpro_size_tier_pricing view');
      return;
    }

    console.log(`✅ Found ${viewData.length} records in v_rpro_size_tier_pricing`);
    console.log('Sample records:');
    viewData.forEach((record, index) => {
      console.log(`  ${index + 1}. listing_sku: ${record.listing_sku}, sku: ${record.sku}`);
      console.log(`     map_price: ${record.map_price}, max_price: ${record.max_price_for_informed}, cost: ${record.cost_for_informed}`);
    });

    // Step 2: Select a test listing_sku
    const testListingSku = viewData[0].listing_sku;
    console.log(`\n🎯 Step 2: Using test listing_sku: ${testListingSku}`);

    // Step 3: Enqueue the task
    console.log('\n📝 Step 3: Enqueueing update_informed_pricing task...');
    
    const taskData = {
      task_type: 'update_informed_pricing',
      payload: {
        listing_sku: testListingSku
      },
      status: 'pending',
      scheduled_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      enqueued_by: 'test_update_informed_pricing'
    };

    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .insert([taskData])
      .select()
      .single();

    if (taskError) {
      console.error('❌ Error enqueueing task:', taskError);
      return;
    }

    console.log(`✅ Task enqueued successfully with ID: ${task.id}`);
    console.log('Task details:', JSON.stringify(task, null, 2));

    // Step 4: Wait for task to be processed
    console.log('\n⏳ Step 4: Waiting for task to be processed...');
    console.log('(The task queue worker should pick this up within 15 seconds)');
    
    let attempts = 0;
    const maxAttempts = 20; // Wait up to 20 seconds
    let taskStatus = 'pending';

    while (attempts < maxAttempts && taskStatus === 'pending') {
      await new Promise(resolve => setTimeout(resolve, 1000));
      attempts++;

      const { data: updatedTask, error: statusError } = await supabase
        .from('t_task_queue')
        .select('*')
        .eq('id', task.id)
        .single();

      if (statusError) {
        console.error('❌ Error checking task status:', statusError);
        break;
      }

      taskStatus = updatedTask.status;
      
      if (taskStatus !== 'pending') {
        console.log(`\n✅ Task completed with status: ${taskStatus}`);
        console.log('Task result:', JSON.stringify(updatedTask.result, null, 2));
        
        if (taskStatus === 'completed') {
          console.log('\n🎉 SUCCESS! The update_informed_pricing task handler is working correctly!');
        } else if (taskStatus === 'error') {
          console.log('\n⚠️  Task completed with error status');
          console.log('Error details:', updatedTask.result);
        }
        break;
      }

      if (attempts % 5 === 0) {
        console.log(`  Still waiting... (${attempts}s elapsed)`);
      }
    }

    if (taskStatus === 'pending') {
      console.log('\n⏰ Task is still pending after 20 seconds');
      console.log('The task queue worker may not be running, or it may be processing other tasks');
      console.log('You can check the task status manually in the t_task_queue table');
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the test
testUpdateInformedPricing();

