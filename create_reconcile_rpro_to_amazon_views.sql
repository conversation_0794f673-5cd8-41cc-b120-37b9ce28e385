-- Create views to reconcile RPRO quantities with Amazon Active Listings Report
-- This compares quantities between imported_table_rpro and it_amaz_active_listings_report

-- Main reconciliation view showing all discrepancies
CREATE OR REPLACE VIEW public.v_reconcile_rpro_counts_to_amazon AS
WITH rpro_data AS (
  -- Get RPRO data with standardized SKU format
  SELECT
    r.id as rpro_id,
    r.ivno,
    r.ivqtylaw as rpro_qty,
    'R' || LPAD(r.ivno::TEXT, 5, '0') as rpro_sku
  FROM imported_table_rpro r
  WHERE r.ivqtylaw IS NOT NULL
),
amazon_data AS (
  -- Get Amazon data
  SELECT
    a.id as amazon_id,
    a.seller_sku,
    a.quantity as amazon_qty,
    a.asin1,
    a.item_name,
    a.report_date
  FROM it_amaz_active_listings_report a
  WHERE a.quantity IS NOT NULL
),
matched_records AS (
  -- Records that exist in both RPRO and Amazon
  SELECT
    r.rpro_id,
    r.ivno,
    r.rpro_qty,
    r.rpro_sku,
    a.amazon_id,
    a.seller_sku,
    a.amazon_qty,
    a.asin1,
    a.item_name,
    a.report_date,
    CASE
      WHEN r.rpro_qty = a.amazon_qty THEN 'Match'
      WHEN r.rpro_qty > a.amazon_qty THEN 'Qty mismatch'
      ELSE 'Qty mismatch'
    END as issue_type,
    r.rpro_qty - a.amazon_qty as qty_difference
  FROM rpro_data r
  JOIN amazon_data a ON r.rpro_sku = a.seller_sku
),
rpro_only AS (
  -- RPRO records not in Amazon
  SELECT
    r.rpro_id,
    r.ivno,
    r.rpro_qty,
    r.rpro_sku,
    NULL::BIGINT as amazon_id,
    NULL::TEXT as seller_sku,
    NULL::NUMERIC as amazon_qty,
    NULL::TEXT as asin1,
    NULL::TEXT as item_name,
    NULL::DATE as report_date,
    'In RPRO not in Amazon' as issue_type,
    r.rpro_qty as qty_difference
  FROM rpro_data r
  WHERE NOT EXISTS (
    SELECT 1 FROM amazon_data a WHERE r.rpro_sku = a.seller_sku
  )
),
amazon_only AS (
  -- Amazon records not in RPRO
  SELECT
    NULL::BIGINT as rpro_id,
    NULL::INTEGER as ivno,
    NULL::NUMERIC as rpro_qty,
    NULL::TEXT as rpro_sku,
    a.amazon_id,
    a.seller_sku,
    a.amazon_qty,
    a.asin1,
    a.item_name,
    a.report_date,
    'In Amazon not in RPRO' as issue_type,
    a.amazon_qty as qty_difference
  FROM amazon_data a
  WHERE NOT EXISTS (
    SELECT 1 FROM rpro_data r WHERE r.rpro_sku = a.seller_sku
  )
)
SELECT * FROM matched_records
UNION ALL
SELECT * FROM rpro_only
UNION ALL
SELECT * FROM amazon_only
ORDER BY issue_type, ivno NULLS LAST;

-- Summary view showing counts by issue type
CREATE OR REPLACE VIEW public.v_reconcile_rpro_counts_to_amazon_summary AS
SELECT
  issue_type,
  COUNT(*) as count,
  COUNT(DISTINCT COALESCE(ivno, seller_sku)) as unique_items,
  SUM(CASE WHEN qty_difference IS NOT NULL THEN ABS(qty_difference) ELSE 0 END) as total_qty_difference
FROM v_reconcile_rpro_counts_to_amazon
GROUP BY issue_type
ORDER BY issue_type;

