import 'dotenv/config';
import { validateAllReports } from '../informedDirectImporter.js';

(async () => {
  try {
    const results = await validateAllReports();
    console.log(JSON.stringify(results, null, 2));
    const summary = results.map(r => ({
      report: r.report,
      success: r.success,
      csvRows: r.csvRowCount,
      csvCols: r.csvColumnCount,
      dbCols: r.dbColumnCount,
      error: r.error,
      csvOnly: r.csvOnlyColumns?.slice(0, 5),
      dbOnly: r.dbOnlyColumns?.slice(0, 5)
    }));
    console.log('\nSummary:', summary);
  } catch (e) {
    console.error('Validation run failed:', e?.message || e);
    process.exit(1);
  }
})();

