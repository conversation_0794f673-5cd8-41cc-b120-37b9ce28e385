import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import readline from 'readline';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing required environment variables: SUPABASE_URL or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function cleanupTasks() {
  try {
    console.log('\n🔍 Finding update_informed_pricing tasks with error status...\n');

    // Get all error tasks for update_informed_pricing
    const { data: errorTasks, error: tasksError } = await supabase
      .from('t_task_queue')
      .select('id, payload, status, result, created_at')
      .eq('task_type', 'update_informed_pricing')
      .eq('status', 'error')
      .order('id', { ascending: true });

    if (tasksError) {
      console.error('❌ Error fetching tasks:', tasksError);
      return;
    }

    if (!errorTasks || errorTasks.length === 0) {
      console.log('✅ No error tasks found for update_informed_pricing');
      return;
    }

    console.log(`📋 Found ${errorTasks.length} error tasks for update_informed_pricing`);

    // Extract listing_skus from payloads
    const listingSkus = errorTasks
      .map(task => task.payload?.listing_sku)
      .filter(sku => sku);

    if (listingSkus.length === 0) {
      console.log('⚠️  No valid listing_skus found in error tasks');
      return;
    }

    console.log(`🔍 Checking ${listingSkus.length} unique listing_skus against t_amaz_acc_listings...\n`);

    // Check which listings have been uploaded to Amazon
    const { data: uploadedListings, error: uploadedError } = await supabase
      .from('t_amaz_acc_listings')
      .select('listing_sku, uploaded_to_amazon_at')
      .in('listing_sku', listingSkus);

    if (uploadedError) {
      console.error('❌ Error checking uploaded listings:', uploadedError);
      return;
    }

    // Create a map of listing_sku -> uploaded_to_amazon_at
    const uploadedMap = new Map();
    if (uploadedListings) {
      uploadedListings.forEach(listing => {
        uploadedMap.set(listing.listing_sku, listing.uploaded_to_amazon_at);
      });
    }

    // Find tasks for listings that are NOT uploaded to Amazon
    const tasksToCleanup = [];
    const tasksToKeep = [];

    for (const task of errorTasks) {
      const listingSku = task.payload?.listing_sku;
      if (!listingSku) {
        console.log(`⚠️  Task ${task.id} has no listing_sku in payload, skipping`);
        continue;
      }

      const uploadedAt = uploadedMap.get(listingSku);
      
      if (!uploadedAt) {
        // Not uploaded to Amazon - mark for cleanup
        tasksToCleanup.push({
          id: task.id,
          listing_sku: listingSku,
          created_at: task.created_at
        });
      } else {
        // Uploaded to Amazon - keep the error status
        tasksToKeep.push({
          id: task.id,
          listing_sku: listingSku,
          uploaded_at: uploadedAt
        });
      }
    }

    console.log(`\n📊 Analysis Results:`);
    console.log(`   ✅ Tasks to keep (listing uploaded to Amazon): ${tasksToKeep.length}`);
    console.log(`   🧹 Tasks to cleanup (listing NOT uploaded to Amazon): ${tasksToCleanup.length}\n`);

    if (tasksToCleanup.length === 0) {
      console.log('✅ No tasks need cleanup - all error tasks are for listings uploaded to Amazon');
      return;
    }

    // Show sample of tasks to cleanup
    console.log(`📋 Sample of tasks to cleanup (first 10):`);
    tasksToCleanup.slice(0, 10).forEach(task => {
      console.log(`   - Task ${task.id}: ${task.listing_sku} (created: ${task.created_at})`);
    });

    if (tasksToCleanup.length > 10) {
      console.log(`   ... and ${tasksToCleanup.length - 10} more`);
    }

    console.log(`\n⚠️  About to update ${tasksToCleanup.length} tasks from 'error' to 'completed' (skipped)`);
    console.log(`   These tasks will be marked as completed with a note that the listing wasn't uploaded to Amazon.\n`);

    // Prompt for confirmation
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question('Do you want to proceed? (yes/no): ', async (answer) => {
      rl.close();

      if (answer.toLowerCase() !== 'yes') {
        console.log('\n❌ Cleanup cancelled by user');
        return;
      }

      console.log('\n🧹 Starting cleanup...\n');

      let successCount = 0;
      let errorCount = 0;

      // Update tasks in batches
      for (const task of tasksToCleanup) {
        try {
          const { error: updateError } = await supabase
            .from('t_task_queue')
            .update({
              status: 'completed',
              result: {
                message: `Skipped ${task.listing_sku} - not uploaded to Amazon (cleaned up from error status)`,
                listing_sku: task.listing_sku,
                skipped: true,
                reason: 'Not uploaded to Amazon',
                cleaned_up_at: new Date().toISOString(),
                previous_status: 'error'
              }
            })
            .eq('id', task.id);

          if (updateError) {
            console.error(`❌ Error updating task ${task.id}:`, updateError.message);
            errorCount++;
          } else {
            console.log(`✅ Updated task ${task.id} (${task.listing_sku})`);
            successCount++;
          }
        } catch (err) {
          console.error(`❌ Exception updating task ${task.id}:`, err.message);
          errorCount++;
        }
      }

      console.log(`\n✅ Cleanup complete!`);
      console.log(`   Successfully updated: ${successCount} tasks`);
      console.log(`   Errors: ${errorCount} tasks`);
      console.log(`   Total processed: ${tasksToCleanup.length} tasks\n`);

      if (tasksToKeep.length > 0) {
        console.log(`ℹ️  ${tasksToKeep.length} error tasks were kept (listings are uploaded to Amazon)`);
        console.log(`   These may represent genuine errors that need investigation.\n`);
      }
    });

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the cleanup
cleanupTasks();

