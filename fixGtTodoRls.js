import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixGtTodoRls() {
  try {
    console.log('Fixing RLS policies on gt_todo table...');

    const sqlQuery = `
      -- Fix RLS policies on gt_todo table to allow authenticated users full access

      -- First, check if RLS is enabled
      ALTER TABLE public.gt_todo ENABLE ROW LEVEL SECURITY;

      -- Drop existing policies if they exist
      DROP POLICY IF EXISTS "Allow authenticated users to select gt_todo" ON public.gt_todo;
      DROP POLICY IF EXISTS "Allow authenticated users to insert gt_todo" ON public.gt_todo;
      DROP POLICY IF EXISTS "Allow authenticated users to update gt_todo" ON public.gt_todo;
      DROP POLICY IF EXISTS "Allow authenticated users to delete gt_todo" ON public.gt_todo;
      DROP POLICY IF EXISTS "gt_todo_select_policy" ON public.gt_todo;
      DROP POLICY IF EXISTS "gt_todo_insert_policy" ON public.gt_todo;
      DROP POLICY IF EXISTS "gt_todo_update_policy" ON public.gt_todo;
      DROP POLICY IF EXISTS "gt_todo_delete_policy" ON public.gt_todo;

      -- Create new permissive policies that allow all authenticated users full access
      CREATE POLICY "Allow authenticated users to select gt_todo" 
      ON public.gt_todo FOR SELECT 
      TO authenticated 
      USING (true);

      CREATE POLICY "Allow authenticated users to insert gt_todo" 
      ON public.gt_todo FOR INSERT 
      TO authenticated 
      WITH CHECK (true);

      CREATE POLICY "Allow authenticated users to update gt_todo" 
      ON public.gt_todo FOR UPDATE 
      TO authenticated 
      USING (true) 
      WITH CHECK (true);

      CREATE POLICY "Allow authenticated users to delete gt_todo" 
      ON public.gt_todo FOR DELETE 
      TO authenticated 
      USING (true);

      -- Grant necessary permissions to authenticated role
      GRANT SELECT, INSERT, UPDATE, DELETE ON public.gt_todo TO authenticated;
    `;

    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sqlQuery });

    if (error) {
      console.error('Error fixing RLS policies:', error);
      process.exit(1);
    }

    console.log('✅ Successfully fixed RLS policies on gt_todo table');
    console.log('Authenticated users can now select, insert, update, and delete gt_todo records');
  } catch (err) {
    console.error('Error:', err);
    process.exit(1);
  }
}

fixGtTodoRls();

