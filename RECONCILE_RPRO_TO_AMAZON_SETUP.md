# Reconcile RPRO Qty to Amazon - Setup Guide

## Overview

A new card has been added to the **Reconcile** tab in `admin.html` called **"Reconcile RPRO Qty to Amazon"**. This card provides instructions for comparing RPRO quantities with Amazon Active Listings Report data to identify discrepancies using live SQL views.

## Files Added/Modified

### 1. **admin.html** (MODIFIED)
- Added new card in the Reconcile tab (after "Refresh RPRO Counts to Veeqo View" card)
- Includes instructions and links to the two views
- No button - views are live and always current

### 2. **create_reconcile_rpro_to_amazon_views.sql** (NEW)
SQL file that creates two views:

#### `v_reconcile_rpro_counts_to_amazon`
Main reconciliation view showing all discrepancies with columns:
- `rpro_id` - RPRO record ID
- `ivno` - RPRO item number
- `rpro_qty` - RPRO quantity (ivqtylaw)
- `rpro_sku` - RPRO SKU (R + 5-digit padded ivno)
- `amazon_id` - Amazon listing ID
- `seller_sku` - Amazon seller SKU
- `amazon_qty` - Amazon quantity
- `asin1` - Amazon ASIN
- `item_name` - Amazon item name
- `report_date` - Amazon report date
- `issue_type` - Type of discrepancy:
  - "Match" - Quantities match
  - "Qty mismatch" - Quantities differ
  - "In RPRO not in Amazon" - Item exists in RPRO but not in Amazon
  - "In Amazon not in RPRO" - Item exists in Amazon but not in RPRO
- `qty_difference` - Difference in quantities

#### `v_reconcile_rpro_counts_to_amazon_summary`
Summary view with counts by issue type:
- `issue_type` - Type of discrepancy
- `count` - Number of records with this issue
- `unique_items` - Number of unique items
- `total_qty_difference` - Total quantity difference

## Setup Instructions

### Step 1: Create the Views in Supabase

Run the SQL file in your Supabase SQL editor:

```
1. Go to Supabase Dashboard
2. Click "SQL Editor"
3. Create a new query
4. Copy and paste contents of create_reconcile_rpro_to_amazon_views.sql
5. Click "Run"
```

### Step 2: Use the Card in Admin Interface

1. Open `admin.html` in your browser
2. Navigate to the **Reconcile** tab
3. Find the **"Reconcile RPRO Qty to Amazon"** card
4. Follow the instructions:
   - Make sure an RPRO import was done today
   - Make sure to download and import 'Amazon Active Listings Report' (see card on the Amazon FBA tab)
5. Go to Supabase SQL Editor and query the views to see the discrepancies

## Viewing Results

After the views are created, you can query them in Supabase SQL Editor:

### Quantity Mismatches (Start Here)
```sql
SELECT * FROM v_reconcile_rpro_counts_to_amazon
WHERE issue_type = 'Qty mismatch'
ORDER BY ABS(qty_difference) DESC;
```

### Summary Statistics
```sql
SELECT * FROM v_reconcile_rpro_counts_to_amazon_summary;
```

### Items in RPRO but Not in Amazon
```sql
SELECT * FROM v_reconcile_rpro_counts_to_amazon
WHERE issue_type = 'In RPRO not in Amazon'
ORDER BY rpro_qty DESC;
```

### Items in Amazon but Not in RPRO
```sql
SELECT * FROM v_reconcile_rpro_counts_to_amazon
WHERE issue_type = 'In Amazon not in RPRO'
ORDER BY amazon_qty DESC;
```

### All Matching Items
```sql
SELECT * FROM v_reconcile_rpro_counts_to_amazon
WHERE issue_type = 'Match';
```

## Card Location

The card is positioned in the **Reconcile** tab, immediately after the **"Refresh RPRO Counts to Veeqo View"** card.

## How It Works

- The views are **live** - they query the current data from `imported_table_rpro` and `it_amaz_active_listings_report` every time you run a query
- No refresh button is needed - the data is always current
- Simply run the SQL queries in Supabase SQL Editor to see the latest discrepancies

## Troubleshooting

### Views not found
- Run the SQL file to create the views
- Verify the SQL executed without errors
- Check Supabase logs for any issues

### No data in views
- Ensure RPRO data has been imported (check `imported_table_rpro`)
- Ensure Amazon Active Listings Report has been imported (check `it_amaz_active_listings_report`)
- Both tables must have data for the reconciliation to work

## Related Documentation

- See "Amazon FBA" tab for importing Amazon Active Listings Report
- See "RPRO" tab for importing RPRO data
- See "Reconcile" tab for other reconciliation processes

