// Test script to verify accessory variant metafield functionality
// This script tests the metafield setting logic for publish_product_accessory task

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testAccessoryMetafields() {
  console.log('🧪 Testing accessory variant metafield functionality...\n');

  try {
    // 1. Find an accessory product with variants that has color_id set
    console.log('1️⃣ Finding test accessory product...');
    const { data: products, error: prodError } = await supabase
      .from('t_products')
      .select(`
        id, name, category_id,
        t_product_variants!inner(
          id, color_id, op1_name, op1_value, op2_name, op2_value, op3_name, op3_value,
          uploaded_to_shopify_at
        )
      `)
      .is('t_product_variants.uploaded_to_shopify_at', null)
      .limit(1);

    if (prodError) {
      console.error('❌ Error fetching products:', prodError.message);
      return;
    }

    if (!products || products.length === 0) {
      console.log('⚠️ No test products found. Creating test data would be needed.');
      return;
    }

    const product = products[0];
    const variant = product.t_product_variants[0];
    
    console.log(`✅ Found test product: ${product.name} (ID: ${product.id})`);
    console.log(`   - Variant ID: ${variant.id}`);
    console.log(`   - Color ID: ${variant.color_id}`);
    console.log(`   - Options: ${variant.op1_name}=${variant.op1_value}, ${variant.op2_name}=${variant.op2_value}, ${variant.op3_name}=${variant.op3_value}`);

    // 2. Test color family lookup
    if (variant.color_id) {
      console.log('\n2️⃣ Testing color family lookup...');
      const { data: color, error: colorError } = await supabase
        .from('t_colors')
        .select('color')
        .eq('id', variant.color_id)
        .maybeSingle();

      if (colorError) {
        console.error('❌ Error fetching color:', colorError.message);
      } else if (color) {
        console.log(`✅ Color family would be: ${color.color}`);
      } else {
        console.log('⚠️ No color found for color_id');
      }
    }

    // 3. Test size detection
    console.log('\n3️⃣ Testing size detection...');
    let sizeValue = null;
    if (variant.op1_name === 'Size' && variant.op1_value) {
      sizeValue = variant.op1_value;
    } else if (variant.op2_name === 'Size' && variant.op2_value) {
      sizeValue = variant.op2_value;
    } else if (variant.op3_name === 'Size' && variant.op3_value) {
      sizeValue = variant.op3_value;
    }

    if (sizeValue) {
      console.log(`✅ Size would be: ${sizeValue}`);
    } else {
      console.log('ℹ️ No size option found');
    }

    // 4. Test variant attributes lookup
    console.log('\n4️⃣ Testing variant attributes lookup...');
    const { data: variantAttributes, error: attrError } = await supabase
      .from('t_variant_attributes')
      .select(`
        attribute_value,
        t_category_attributes!inner(
          variant_metafield_on_shopify_name,
          variant_metafield_on_shopify_created_at
        )
      `)
      .eq('variant_id', variant.id);

    if (attrError) {
      console.error('❌ Error fetching variant attributes:', attrError.message);
    } else if (variantAttributes && variantAttributes.length > 0) {
      console.log(`✅ Found ${variantAttributes.length} variant attributes:`);
      for (const attr of variantAttributes) {
        const categoryAttr = attr.t_category_attributes;
        console.log(`   - Value: ${attr.attribute_value}`);
        console.log(`   - Metafield name: ${categoryAttr.variant_metafield_on_shopify_name}`);
        console.log(`   - Created at: ${categoryAttr.variant_metafield_on_shopify_created_at}`);
        
        if (categoryAttr.variant_metafield_on_shopify_name && 
            categoryAttr.variant_metafield_on_shopify_created_at) {
          console.log(`   ✅ Would set metafield: ${categoryAttr.variant_metafield_on_shopify_name} = ${attr.attribute_value}`);
        } else {
          console.log(`   ⚠️ Metafield not ready (missing name or created_at)`);
        }
      }
    } else {
      console.log('ℹ️ No variant attributes found');
    }

    console.log('\n✅ Test completed successfully!');
    console.log('\n📝 Summary of metafields that would be set:');
    console.log(`   - dz_disc_variant.color_family: ${variant.color_id ? 'Yes' : 'No'}`);
    console.log(`   - dz_disc_variant.size: ${sizeValue ? 'Yes' : 'No'}`);
    console.log(`   - Additional attributes: ${variantAttributes?.length || 0}`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testAccessoryMetafields();
