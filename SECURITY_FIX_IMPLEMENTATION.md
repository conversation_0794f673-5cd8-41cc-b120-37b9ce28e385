# Security Fix Implementation Guide

## Fix #1: Add Authentication Middleware to adminServer.js

### Step 1: Create Authentication Middleware

Create a new file `authMiddleware.js`:

```javascript
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function authMiddleware(req, res, next) {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Missing or invalid authorization header' });
    }

    const token = authHeader.substring(7);

    // Verify token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    // Check if user is admin
    const { data: roles, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .single();

    if (roleError || !roles || roles.role !== 'admin') {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    // Attach user to request
    req.user = user;
    next();
  } catch (err) {
    console.error('[authMiddleware] Error:', err);
    res.status(500).json({ error: 'Authentication error' });
  }
}
```

### Step 2: Apply Middleware in adminServer.js

Add after line 32 in adminServer.js:

```javascript
import { authMiddleware } from './authMiddleware.js';

// Apply authentication to all API routes
app.use('/api/', authMiddleware);
```

---

## Fix #2: Update Worker to Use Service Role Key

### In taskQueueWorker.js (line 71):

**Change from:**
```javascript
const supabaseKey = process.env.SUPABASE_KEY;
```

**Change to:**
```javascript
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
```

### In taskQueueWorkerDaemon.js (line 11):

**Change from:**
```javascript
const supabaseKey = process.env.SUPABASE_KEY;
```

**Change to:**
```javascript
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
```

### In enqueueWorkerStatusTask.js (line 9):

**Change from:**
```javascript
const supabaseKey = process.env.SUPABASE_KEY;
```

**Change to:**
```javascript
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
```

---

## Fix #3: Remove Hardcoded Credentials

### Files to Clean:

1. **update_task_queue_worker.js** - Delete lines 5-6, use env vars
2. **enqueue_match_task.js** - Delete lines 5-6, use env vars
3. **update_task_locking.js** - Delete lines 4-5, use env vars
4. **informedReportDownloader.cjs** - Delete lines 46-47, use env vars
5. **.env-tp.txt** - DELETE ENTIRE FILE (it's in version control!)

### Template for replacement:

```javascript
// BEFORE (BAD):
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';

// AFTER (GOOD):
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!supabaseKey) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY not set in environment');
}
```

---

## Fix #4: Add Authorization Headers to admin.html

### Update all fetch calls to include auth token

**Before (line 4187):**
```javascript
fetch('/api/enqueue-task', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    }
})
```

**After:**
```javascript
fetch('/api/enqueue-task', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAuthToken()}`
    }
})
```

### Add helper function to admin.html (in script section):

```javascript
function getAuthToken() {
  // Get token from Supabase session
  const session = JSON.parse(localStorage.getItem('supabase.auth.token'));
  return session?.access_token || '';
}
```

---

## Fix #5: Add Input Validation

### Example for adminServer.js (line 370):

**Before:**
```javascript
app.post('/api/delete-wrong-veeqo-records', (req, res) => {
  const limit = req.body.limit;
```

**After:**
```javascript
app.post('/api/delete-wrong-veeqo-records', (req, res) => {
  const limit = req.body.limit;
  
  // Validate input
  if (limit !== undefined && (typeof limit !== 'number' || limit < 0 || limit > 10000)) {
    return res.status(400).json({ error: 'Invalid limit parameter' });
  }
```

---

## Testing Checklist

- [ ] Test worker with service role key
- [ ] Test admin endpoints with auth middleware
- [ ] Test admin.html with auth headers
- [ ] Verify RLS policies still work
- [ ] Test task processing still works
- [ ] Verify no hardcoded credentials remain
- [ ] Test error handling for auth failures

---

## Environment Variables Required

Ensure your `.env` file has:

```
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

**NEVER commit `.env` file to version control!**

