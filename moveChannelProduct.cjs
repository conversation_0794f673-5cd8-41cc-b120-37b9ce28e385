const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔗 VEEQO CHANNEL PRODUCT MOVER');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    console.log(`   Request: ${method} ${endpoint}`);
    if (data) {
      console.log(`   Body: ${JSON.stringify(data, null, 2)}`);
    }
    
    const response = await fetch(endpoint, options);
    
    console.log(`   Response Status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`   Response Body: ${errorText}`);
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    if (method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    console.log(`   Response: Success`);
    return { success: true, data: result };
  } catch (error) {
    console.log(`   Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Function to get product details
async function getProductDetails(productId) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!result.success) {
    console.error(`❌ Error fetching product: ${result.error}`);
    return null;
  }
  
  return result.data;
}

// Function to find sellable by SKU
async function findSellableBySku(sku) {
  console.log(`\n🔍 Searching for SKU: ${sku}`);
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products?query=${encodeURIComponent(sku)}`);
  
  if (!result.success) {
    console.error(`   ❌ Error searching: ${result.error}`);
    return null;
  }
  
  for (const product of result.data) {
    if (product.sellables) {
      const sellable = product.sellables.find(s => s.sku_code === sku);
      if (sellable) {
        console.log(`   ✅ Found: Product ID ${product.id}, Sellable ID ${sellable.id}`);
        return { product, sellable };
      }
    }
  }
  
  console.log(`   ❌ Not found`);
  return null;
}

// Function to move channel product
async function moveChannelProduct(oldProductId, channelProductId, newProductId) {
  console.log(`\n🔗 Moving channel product...`);
  
  // Try different endpoints and methods
  const attempts = [
    {
      endpoint: `https://api.veeqo.com/products/${oldProductId}/channel_products/${channelProductId}`,
      method: 'PUT',
      data: { channel_product: { product_id: newProductId } }
    },
    {
      endpoint: `https://api.veeqo.com/products/${oldProductId}/channel_products/${channelProductId}`,
      method: 'PATCH',
      data: { channel_product: { product_id: newProductId } }
    },
    {
      endpoint: `https://api.veeqo.com/products/${newProductId}/channel_products/${channelProductId}`,
      method: 'PUT',
      data: { channel_product: { product_id: newProductId } }
    },
    {
      endpoint: `https://api.veeqo.com/channel_products/${channelProductId}`,
      method: 'PUT',
      data: { channel_product: { product_id: newProductId } }
    },
    {
      endpoint: `https://api.veeqo.com/channel_products/${channelProductId}`,
      method: 'PATCH',
      data: { channel_product: { product_id: newProductId } }
    }
  ];
  
  for (const attempt of attempts) {
    console.log(`\n📡 Attempt: ${attempt.method} ${attempt.endpoint}`);
    const result = await makeVeeqoRequest(attempt.endpoint, attempt.method, attempt.data);
    
    if (result.success) {
      console.log(`   ✅ Success!`);
      return { success: true, data: result.data };
    } else {
      console.log(`   ❌ Failed: ${result.error}`);
    }
  }
  
  console.log(`\n❌ All attempts failed`);
  return { success: false, error: 'All attempts failed' };
}

// Main function
async function relinkChannelProduct(oldProductId, channelProductId, newSku) {
  console.log(`\n🔗 Relinking channel product ${channelProductId} from product ${oldProductId} to SKU ${newSku}`);
  console.log('─'.repeat(50));
  
  // Get old product details
  console.log(`\n📦 Fetching old product ${oldProductId}...`);
  const oldProduct = await getProductDetails(oldProductId);
  
  if (!oldProduct) {
    return false;
  }
  
  console.log(`   Title: ${oldProduct.title}`);
  
  // Find the channel product
  const channelProduct = oldProduct.channel_products?.find(cp => cp.id === parseInt(channelProductId));
  
  if (!channelProduct) {
    console.error(`❌ Channel product ${channelProductId} not found in product ${oldProductId}`);
    return false;
  }
  
  console.log(`\n📺 Channel Product:`);
  console.log(`   ID: ${channelProduct.id}`);
  console.log(`   Channel: ${channelProduct.channel?.name || 'Unknown'}`);
  console.log(`   Status: ${channelProduct.status}`);
  console.log(`   Remote Title: ${channelProduct.remote_title}`);
  
  // Find new sellable
  const newTarget = await findSellableBySku(newSku);
  
  if (!newTarget) {
    console.error(`❌ Could not find target SKU: ${newSku}`);
    return false;
  }
  
  console.log(`\n🎯 Target:`);
  console.log(`   Product ID: ${newTarget.product.id}`);
  console.log(`   Sellable ID: ${newTarget.sellable.id}`);
  console.log(`   Title: ${newTarget.product.title}`);
  console.log(`   SKU: ${newTarget.sellable.sku_code}`);
  
  // Try to move the channel product
  const result = await moveChannelProduct(oldProductId, channelProductId, newTarget.product.id);
  
  if (result.success) {
    console.log(`\n✅ Successfully moved channel product!`);
    return true;
  } else {
    console.log(`\n❌ Failed to move channel product`);
    console.log(`\n💡 The Veeqo API may not support moving channel products between products.`);
    console.log(`   You may need to manually relink this in the Veeqo UI.`);
    return false;
  }
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 3) {
  console.log(`
Usage: node moveChannelProduct.cjs <old-product-id> <channel-product-id> <new-sku>

Example:
  node moveChannelProduct.cjs 116629736 229745894 R13756

This will move channel product 229745894 from product 116629736 to the product for R13756.
`);
  process.exit(1);
}

const oldProductId = args[0];
const channelProductId = args[1];
const newSku = args[2];

relinkChannelProduct(oldProductId, channelProductId, newSku)
  .then(success => {
    if (success) {
      console.log(`\n✅ All done!`);
      process.exit(0);
    } else {
      console.log(`\n❌ Operation failed`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

