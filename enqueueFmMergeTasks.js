// enqueueFmMergeTasks.js - Find FM SKUs and enqueue merge tasks
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function findAndEnqueueFmMerges(limit = 10) {
  console.log('🔍 FIND AND ENQUEUE FM MERGE TASKS');
  console.log('='.repeat(70));
  console.log(`   Looking for up to ${limit} FM SKUs to merge...`);
  console.log('');

  try {
    // Find FM SKUs in the form FM##### (5 digits)
    // Exclude SKUs with 'X' in them
    // Exclude SKUs that already have 'merged' in the product_title
    const { data: fmRecords, error } = await supabase
      .from('imported_table_veeqo_sellables_export')
      .select('sku_code, product_title')
      .like('sku_code', 'FM%')
      .not('sku_code', 'like', '%X%')
      .not('product_title', 'like', 'merged%')
      .limit(1000); // Get more records to filter

    if (error) {
      console.error(`❌ Error querying database: ${error.message}`);
      return { success: false, error: error.message };
    }

    console.log(`📊 Found ${fmRecords.length} total FM records (before filtering)`);

    // Filter for exactly 5 digits after FM (FM12345 format)
    const validFmRecords = fmRecords.filter(record => {
      const sku = record.sku_code;
      // Match FM followed by exactly 5 digits and nothing else
      const match = sku.match(/^FM(\d{5})$/);
      return match !== null;
    });

    console.log(`✅ Found ${validFmRecords.length} valid FM##### SKUs (5 digits, no X)`);
    console.log('');

    if (validFmRecords.length === 0) {
      console.log('ℹ️  No FM SKUs found to merge');
      return { success: true, tasksEnqueued: 0 };
    }

    // Take only the requested limit
    const recordsToProcess = validFmRecords.slice(0, limit);
    console.log(`📋 Processing ${recordsToProcess.length} records:`);
    console.log('');

    const now = new Date();
    const tasksEnqueued = [];
    const updates = [];

    for (const record of recordsToProcess) {
      const oldSku = record.sku_code;
      const newSku = oldSku.replace(/^FM/, 'R');
      
      console.log(`   ${oldSku} → ${newSku}`);
      console.log(`      Title: ${record.product_title}`);

      // Prepare task payload
      const payload = {
        old_sku: oldSku,
        new_sku: newSku
      };

      // Insert the task into the queue
      const { data: taskData, error: taskError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'merge_veeqo_fm_to_r',
          payload: payload,
          status: 'pending',
          scheduled_at: now.toISOString(),
          created_at: now.toISOString(),
          enqueued_by: `batch_fm_merge_${oldSku}_to_${newSku}`
        })
        .select();

      if (taskError) {
        console.error(`      ❌ Error enqueueing task: ${taskError.message}`);
        continue;
      }

      console.log(`      ✅ Task enqueued (ID: ${taskData[0].id})`);
      tasksEnqueued.push({
        taskId: taskData[0].id,
        oldSku,
        newSku
      });

      // Prepare update to prepend 'merged' to product_title
      updates.push({
        sku_code: record.sku_code,
        oldTitle: record.product_title,
        newTitle: `merged ${record.product_title}`
      });

      console.log('');
    }

    // Update all product_titles to prepend 'merged'
    console.log('📝 Updating product_titles to prepend "merged"...');
    console.log('');

    for (const update of updates) {
      const { error: updateError } = await supabase
        .from('imported_table_veeqo_sellables_export')
        .update({ product_title: update.newTitle })
        .eq('sku_code', update.sku_code);

      if (updateError) {
        console.error(`   ❌ Error updating record ${update.sku_code}: ${updateError.message}`);
      } else {
        console.log(`   ✅ Updated: "${update.oldTitle}" → "${update.newTitle}"`);
      }
    }

    console.log('');
    console.log('='.repeat(70));
    console.log(`✅ Summary:`);
    console.log(`   Tasks enqueued: ${tasksEnqueued.length}`);
    console.log(`   Records updated: ${updates.length}`);
    console.log('');
    console.log('🔄 The worker will pick up these tasks and process them automatically.');

    return { 
      success: true, 
      tasksEnqueued: tasksEnqueued.length,
      tasks: tasksEnqueued,
      updates: updates.length
    };

  } catch (err) {
    console.error(`❌ Exception: ${err.message}`);
    return { success: false, error: err.message };
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const limit = args[0] ? parseInt(args[0]) : 10;

if (isNaN(limit) || limit < 1) {
  console.log(`
Usage: node enqueueFmMergeTasks.js [limit]

Arguments:
  limit   - Number of FM SKUs to process (default: 10)

Example:
  node enqueueFmMergeTasks.js 10
  node enqueueFmMergeTasks.js 50
`);
  process.exit(1);
}

// Run the function
findAndEnqueueFmMerges(limit)
  .then(result => {
    if (result.success) {
      console.log('✅ Done!');
      process.exit(0);
    } else {
      console.error('❌ Failed to enqueue tasks');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error(`❌ Unhandled error: ${err.message}`);
    process.exit(1);
  });

