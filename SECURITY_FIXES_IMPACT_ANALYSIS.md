# Security Fixes: Impact Analysis & Troubleshooting

## What Will Break After Fixes

### 1. Admin Interface (admin.html)
**Status: WILL BREAK** ⚠️

**Why:** Without Authorization headers, all fetch requests will fail with 401 errors.

**Symptoms:**
- "Missing or invalid authorization header" errors in console
- All buttons stop working
- No data loads in dashboard

**Fix:** Add auth token to all fetch calls (see SECURITY_FIX_IMPLEMENTATION.md)

**Workaround (Temporary):**
```javascript
// Add to admin.html before other scripts
window.getAuthToken = () => {
  const session = JSON.parse(localStorage.getItem('supabase.auth.token'));
  return session?.access_token || '';
};
```

---

### 2. Task Queue Worker
**Status: MIGHT BREAK** ⚠️

**Why:** If RLS policies are strict, service role key might be required.

**Symptoms:**
- Tasks fail with "permission denied" errors
- Worker logs show RLS policy violations
- Tasks stuck in "processing" status

**Fix:** Ensure service role key is used (see Fix #2)

**Verification:**
```bash
# Check which key is being used
grep "SUPABASE_KEY" taskQueueWorker.js
# Should show: SUPABASE_SERVICE_ROLE_KEY
```

---

### 3. Manual Scripts
**Status: WILL BREAK** ⚠️

**Why:** Hardcoded credentials will be removed.

**Affected Scripts:**
- `update_task_queue_worker.js`
- `enqueue_match_task.js`
- `update_task_locking.js`
- `informedReportDownloader.cjs`

**Symptoms:**
- "SUPABASE_KEY not set" errors
- Scripts fail to connect to database

**Fix:** Use environment variables instead

**Verification:**
```bash
# Before running any script, ensure .env is loaded
node -e "require('dotenv').config(); console.log(process.env.SUPABASE_SERVICE_ROLE_KEY ? 'OK' : 'MISSING')"
```

---

### 4. Daemon Processes
**Status: MIGHT BREAK** ⚠️

**Why:** If service role key is different from anon key, permissions change.

**Affected Processes:**
- `taskQueueWorkerDaemon.js`
- `runTaskQueueWorker.js`
- `discraftScheduler.js`

**Symptoms:**
- Daemon starts but tasks fail
- "Permission denied" in logs
- Email notifications stop

**Fix:** Restart daemons after credential changes

**Verification:**
```bash
# Check daemon status
pm2 status

# View daemon logs
pm2 logs taskQueueWorkerDaemon
```

---

## Rollback Plan

If fixes cause critical issues:

### Step 1: Revert Code Changes
```bash
git revert <commit-hash>
```

### Step 2: Restart Services
```bash
pm2 restart all
```

### Step 3: Verify Functionality
```bash
# Test admin interface
curl http://localhost:3001/admin.html

# Test worker
pm2 logs taskQueueWorkerDaemon
```

---

## Testing Strategy

### Phase 1: Development Testing
1. Apply fixes to development environment
2. Test each component individually
3. Verify no hardcoded credentials remain
4. Check all error messages are clear

### Phase 2: Staging Testing
1. Deploy to staging environment
2. Run full integration tests
3. Test admin interface with real users
4. Monitor worker for 24 hours

### Phase 3: Production Deployment
1. Schedule during low-traffic period
2. Have rollback plan ready
3. Monitor logs closely
4. Be prepared to revert quickly

---

## Monitoring After Fixes

### Key Metrics to Watch

1. **Admin Interface**
   - Check browser console for auth errors
   - Verify all buttons work
   - Test task enqueueing

2. **Worker Process**
   - Monitor task success rate
   - Check for permission errors
   - Verify task completion times

3. **Database**
   - Monitor RLS policy violations
   - Check for permission denied errors
   - Verify audit logs

### Log Locations

```
# Admin server logs
pm2 logs adminServer

# Worker logs
pm2 logs taskQueueWorkerDaemon

# System logs
tail -f logs/*.log
```

---

## Common Issues & Solutions

### Issue: "Missing or invalid authorization header"
**Cause:** Admin.html not sending auth token
**Solution:** Add Authorization header to fetch calls

### Issue: "Insufficient permissions"
**Cause:** User not in admin role
**Solution:** Check user_roles table, add admin role if needed

### Issue: "SUPABASE_SERVICE_ROLE_KEY not set"
**Cause:** Environment variable not loaded
**Solution:** Ensure .env file exists and is loaded

### Issue: Worker tasks fail with RLS errors
**Cause:** Service role key not being used
**Solution:** Verify taskQueueWorker.js uses SUPABASE_SERVICE_ROLE_KEY

### Issue: Admin interface loads but no data
**Cause:** API endpoints returning 401
**Solution:** Check auth token is valid and user is admin

---

## Verification Checklist

After implementing fixes:

- [ ] No hardcoded credentials in code
- [ ] All fetch calls have Authorization headers
- [ ] Worker uses service role key
- [ ] Admin endpoints require authentication
- [ ] Error messages are clear and helpful
- [ ] Logs show successful auth checks
- [ ] Tasks process successfully
- [ ] Admin interface fully functional
- [ ] No permission denied errors
- [ ] Credentials rotated in Supabase

---

## Support Resources

- Supabase Auth Docs: https://supabase.com/docs/guides/auth
- RLS Policies: https://supabase.com/docs/guides/auth/row-level-security
- JWT Tokens: https://supabase.com/docs/guides/auth/jwts
- Environment Variables: https://nodejs.org/en/docs/guides/nodejs-env-variable-configuration/

