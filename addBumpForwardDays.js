// Add bump_forward_days column to gt_todo table
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function addBumpForwardDays() {
  try {
    console.log('Adding bump_forward_days column to gt_todo table...');

    const sqlQuery = `
      DO $$
      BEGIN
          IF NOT EXISTS (
              SELECT 1 
              FROM information_schema.columns 
              WHERE table_schema = 'public' 
              AND table_name = 'gt_todo' 
              AND column_name = 'bump_forward_days'
          ) THEN
              ALTER TABLE public.gt_todo 
              ADD COLUMN bump_forward_days NUMERIC(10,3);
              
              -- Add a comment to the column
              COMMENT ON COLUMN public.gt_todo.bump_forward_days IS 'Number of days to bump scheduled_at forward when task is past due. Default is 0.042 (1 hour) if null.';
              
              RAISE NOTICE 'Added bump_forward_days column to gt_todo table';
          ELSE
              RAISE NOTICE 'bump_forward_days column already exists in gt_todo table';
          END IF;
      END $$;
    `;

    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sqlQuery });

    if (error) {
      console.error('Error adding column:', error);
      process.exit(1);
    }

    console.log('✅ Successfully added bump_forward_days column to gt_todo table');
    console.log('Default bump is 0.042 days (1 hour) when null');
  } catch (err) {
    console.error('Error:', err);
    process.exit(1);
  }
}

addBumpForwardDays();

