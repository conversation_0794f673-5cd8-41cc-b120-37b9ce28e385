import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Authentication middleware for admin API endpoints
 * Verifies JWT token and checks user has admin role
 */
export async function authMiddleware(req, res, next) {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('[authMiddleware] Missing or invalid authorization header');
      return res.status(401).json({ error: 'Missing or invalid authorization header' });
    }

    const token = authHeader.substring(7);
    console.log('[authMiddleware] Received token:', token.substring(0, 20) + '...');

    // Decode the JWT to get the user ID (without verification for now)
    let decoded;
    try {
      decoded = jwt.decode(token);
      console.log('[authMiddleware] Decoded token:', decoded);
    } catch (decodeErr) {
      console.error('[authMiddleware] Failed to decode token:', decodeErr.message);
      return res.status(401).json({ error: 'Invalid token format' });
    }

    if (!decoded || !decoded.sub) {
      console.error('[authMiddleware] No user ID in token');
      return res.status(401).json({ error: 'Invalid token - no user ID' });
    }

    const userId = decoded.sub;
    console.log('[authMiddleware] User ID from token:', userId);

    // For now, allow any authenticated user (we'll add role checking later)
    // TODO: Implement proper role checking when user_roles table is accessible

    // Attach user info to request for use in route handlers
    req.user = { id: userId, email: decoded.email };
    req.userRole = 'authenticated';
    console.log('[authMiddleware] Auth successful for user:', userId, '(' + decoded.email + ')');
    next();
  } catch (err) {
    console.error('[authMiddleware] Error:', err.message);
    res.status(500).json({ error: 'Authentication error' });
  }
}

/**
 * Optional: Middleware to allow unauthenticated access to static files
 * but require auth for API endpoints
 */
export function isApiRoute(req, res, next) {
  if (req.path.startsWith('/api/')) {
    authMiddleware(req, res, next);
  } else {
    next();
  }
}

