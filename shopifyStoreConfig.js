// shopifyStoreConfig.js
// Utility to get Shopify credentials based on store ID from t_shopify_stores table

import dotenv from 'dotenv';

dotenv.config();

/**
 * Get Shopify credentials for a specific store
 * @param {number} storeId - The store ID from t_shopify_stores (1 = dzdiscs, 2 = tippmannparts)
 * @returns {Object} Object containing endpoint and accessToken
 * @throws {Error} If store ID is invalid or credentials are missing
 */
export function getShopifyCredentials(storeId) {
  if (!storeId) {
    throw new Error('Store ID is required');
  }

  let endpoint, accessToken, storeName;

  switch (storeId) {
    case 1:
      // DZ Discs (default store)
      endpoint = process.env.SHOPIFY_ENDPOINT;
      accessToken = process.env.SHOPIFY_ACCESS_TOKEN;
      storeName = 'DZ Discs';
      break;

    case 2:
      // Tippmann Parts
      endpoint = process.env.SHOPIFY_ENDPOINT_2;
      accessToken = process.env.SHOPIFY_ACCESS_TOKEN_2;
      storeName = 'Tippmann Parts';
      break;

    default:
      throw new Error(`Unknown store ID: ${storeId}. Valid store IDs are 1 (DZ Discs) or 2 (Tippmann Parts)`);
  }

  if (!endpoint || !accessToken) {
    throw new Error(`Missing Shopify credentials for store ID ${storeId} (${storeName}). Check environment variables.`);
  }

  return {
    endpoint,
    accessToken,
    storeName,
    storeId
  };
}

/**
 * Get the products base URL from a GraphQL endpoint
 * @param {string} endpoint - The GraphQL endpoint URL
 * @returns {string} The base URL for REST API product operations
 */
export function getProductsBaseUrl(endpoint) {
  const productsEndpoint = endpoint.replace('graphql.json', 'products.json');
  return productsEndpoint.split('/products.json')[0];
}

/**
 * Make a GraphQL request to Shopify
 * @param {string} query - The GraphQL query
 * @param {Object} variables - Variables for the query
 * @param {number} storeId - The store ID
 * @returns {Promise<Object>} The query result
 */
export async function shopifyGraphQLRequest(query, variables, storeId) {
  const { endpoint, accessToken } = getShopifyCredentials(storeId);

  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': accessToken,
    },
    body: JSON.stringify({ query, variables }),
  });

  const result = await response.json();

  if (result.errors) {
    throw new Error(`Shopify GraphQL error: ${JSON.stringify(result.errors)}`);
  }

  return result.data;
}

/**
 * Make a REST API request to Shopify
 * @param {string} url - The full URL for the REST API call
 * @param {string} method - HTTP method (GET, POST, PUT, DELETE)
 * @param {Object} payload - Request payload for POST/PUT
 * @param {number} storeId - The store ID
 * @returns {Promise<Object>} The response data
 */
export async function shopifyRestRequest(url, method, payload, storeId) {
  const { accessToken } = getShopifyCredentials(storeId);

  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': accessToken,
    },
  };

  if (payload && (method === 'POST' || method === 'PUT')) {
    options.body = JSON.stringify(payload);
  }

  const response = await fetch(url, options);
  const result = await response.json();

  if (!response.ok) {
    throw new Error(`Shopify REST API error: ${response.status} ${response.statusText} - ${JSON.stringify(result)}`);
  }

  return result;
}

export default {
  getShopifyCredentials,
  getProductsBaseUrl,
  shopifyGraphQLRequest,
  shopifyRestRequest
};

