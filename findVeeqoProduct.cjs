const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔍 VEEQO PRODUCT FINDER');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to search for a product by SKU
async function findProductBySku(sku) {
  console.log(`\n🔍 Searching for product with SKU: ${sku}`);
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products?query=${encodeURIComponent(sku)}`);
  
  if (!result.success) {
    console.error(`   ❌ Error searching: ${result.error}`);
    return null;
  }
  
  if (!result.data || result.data.length === 0) {
    console.log(`   ⚠️  No products found for SKU: ${sku}`);
    return null;
  }
  
  console.log(`\n📊 Found ${result.data.length} product(s):\n`);
  
  result.data.forEach((product, index) => {
    console.log(`${index + 1}. Product ID: ${product.id}`);
    console.log(`   Title: ${product.title}`);
    console.log(`   Sellables:`);
    
    if (product.sellables && product.sellables.length > 0) {
      product.sellables.forEach((sellable, sIndex) => {
        const isMatch = sellable.sku_code === sku ? ' ⭐ EXACT MATCH' : '';
        console.log(`     ${sIndex + 1}. Sellable ID: ${sellable.id} - SKU: ${sellable.sku_code}${isMatch}`);
        
        // Get stock info
        if (sellable.stock_entries && sellable.stock_entries.length > 0) {
          const totalStock = sellable.stock_entries.reduce((sum, entry) => sum + (entry.physical_stock_level || 0), 0);
          console.log(`        Stock: ${totalStock}`);
        }
      });
    }
    
    // Check for channel products
    if (product.channel_products && product.channel_products.length > 0) {
      console.log(`   Channel Products: ${product.channel_products.length}`);
      product.channel_products.forEach((cp, cpIndex) => {
        console.log(`     ${cpIndex + 1}. Channel: ${cp.channel?.name || 'Unknown'} - Status: ${cp.status}`);
      });
    } else {
      console.log(`   Channel Products: None`);
    }
    
    console.log('');
  });
  
  return result.data;
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length === 0) {
  console.log(`
Usage: node findVeeqoProduct.cjs <sku>

Example:
  node findVeeqoProduct.cjs FM13756
`);
  process.exit(1);
}

const sku = args[0];

findProductBySku(sku)
  .then((products) => {
    if (products && products.length > 0) {
      console.log(`✅ Search complete!`);
      process.exit(0);
    } else {
      console.log(`❌ No products found`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

