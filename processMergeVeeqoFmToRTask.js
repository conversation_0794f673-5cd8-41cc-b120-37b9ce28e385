// processMergeVeeqoFmToRTask.js - Process merge_veeqo_fm_to_r task
import dotenv from 'dotenv';
dotenv.config();

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Process a merge_veeqo_fm_to_r task by calling the robustMergeVeeqoVariants.cjs script
 * @param {Object} task - The task object from t_task_queue
 * @param {Object} context - Context object with supabase, updateTaskStatus, logError
 */
export default async function processMergeVeeqoFmToRTask(task, context) {
  const { supabase, updateTaskStatus, logError } = context;
  
  console.log(`[processMergeVeeqoFmToRTask.js] Processing task ${task.id}`);
  
  try {
    // Update task status to processing
    await updateTaskStatus(task.id, 'processing');
    
    // Parse the payload
    const payload = typeof task.payload === 'string' ? JSON.parse(task.payload) : task.payload;
    const oldSku = payload.old_sku;
    const newSku = payload.new_sku;
    
    if (!oldSku || !newSku) {
      throw new Error('Missing old_sku or new_sku in payload');
    }
    
    console.log(`[processMergeVeeqoFmToRTask.js] Merging ${oldSku} → ${newSku}`);
    
    // Call the robustMergeVeeqoVariants.cjs script
    const scriptPath = path.join(__dirname, 'robustMergeVeeqoVariants.cjs');
    
    return new Promise((resolve, reject) => {
      const mergeProcess = spawn('node', [scriptPath, oldSku, newSku, '--delete'], {
        cwd: __dirname,
        env: process.env
      });
      
      let stdout = '';
      let stderr = '';
      
      mergeProcess.stdout.on('data', (data) => {
        const output = data.toString();
        stdout += output;
        console.log(`[robustMergeVeeqoVariants.cjs] ${output.trim()}`);
      });
      
      mergeProcess.stderr.on('data', (data) => {
        const output = data.toString();
        stderr += output;
        console.error(`[robustMergeVeeqoVariants.cjs] ERROR: ${output.trim()}`);
      });
      
      mergeProcess.on('close', async (code) => {
        if (code === 0) {
          console.log(`[processMergeVeeqoFmToRTask.js] Merge completed successfully`);
          
          // Extract summary from stdout
          const result = {
            success: true,
            oldSku,
            newSku,
            message: `Successfully merged ${oldSku} to ${newSku}`,
            output: stdout
          };
          
          await updateTaskStatus(task.id, 'completed', result);
          resolve(result);
        } else {
          const errorMessage = `Merge script exited with code ${code}`;
          console.error(`[processMergeVeeqoFmToRTask.js] ${errorMessage}`);
          console.error(`[processMergeVeeqoFmToRTask.js] stderr: ${stderr}`);
          
          await logError(errorMessage, {
            task_id: task.id,
            task_type: task.task_type,
            oldSku,
            newSku,
            exit_code: code,
            stderr
          });
          
          await updateTaskStatus(task.id, 'error', {
            error: errorMessage,
            stderr,
            exit_code: code
          });
          
          reject(new Error(errorMessage));
        }
      });
      
      mergeProcess.on('error', async (err) => {
        const errorMessage = `Failed to spawn merge process: ${err.message}`;
        console.error(`[processMergeVeeqoFmToRTask.js] ${errorMessage}`);
        
        await logError(errorMessage, {
          task_id: task.id,
          task_type: task.task_type,
          oldSku,
          newSku,
          error: err.message
        });
        
        await updateTaskStatus(task.id, 'error', {
          error: errorMessage
        });
        
        reject(err);
      });
    });
    
  } catch (error) {
    console.error(`[processMergeVeeqoFmToRTask.js] Error processing task: ${error.message}`);
    
    await logError(`Error processing merge_veeqo_fm_to_r task: ${error.message}`, {
      task_id: task.id,
      task_type: task.task_type,
      payload: task.payload,
      error: error.message,
      stack: error.stack
    });
    
    await updateTaskStatus(task.id, 'error', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

