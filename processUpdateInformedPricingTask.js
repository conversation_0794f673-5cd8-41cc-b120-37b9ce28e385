// processUpdateInformedPricingTask.js
// Task handler for update_informed_pricing task type
// Batches all pending update_informed_pricing tasks and sends them as a single CSV to Informed

import dotenv from 'dotenv';
dotenv.config();

const INFORMED_API_KEY = process.env.INFORMED_API_KEY || 'Us79BiL2il9qXJTWPWhfbAgXn5GfYMFFaFiFtiCH';
const INFORMED_BASE_URL = 'https://api.informed.co';
const INFORMED_FEED_URL = `${INFORMED_BASE_URL}/v1/feed`;

/**
 * Process update_informed_pricing tasks in batch
 * This function collects all pending update_informed_pricing tasks and sends them as a single CSV
 * RATE LIMITING: Only sends to Informed API once every 15 minutes to avoid rate limits
 * @param {Object} task - The task object from t_task_queue (the first task that triggered this)
 * @param {Object} context - Context object with supabase, updateTaskStatus, logError
 */
async function processUpdateInformedPricingTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processUpdateInformedPricingTask] Processing batch of update_informed_pricing tasks, triggered by task ${task.id}`);

  try {
    const now = new Date();

    // UNLOCK STALE LOCKED TASKS: If a task has been locked for more than 30 minutes, unlock it
    // This handles cases where the worker crashed while processing
    console.log(`[processUpdateInformedPricingTask] Checking for stale locked tasks...`);
    const lockTimeout = 30 * 60 * 1000; // 30 minutes
    const staleTime = new Date(now.getTime() - lockTimeout).toISOString();

    const { data: staledTasks, error: staleError } = await supabase
      .from('t_task_queue')
      .update({
        status: 'pending',
        locked_at: null,
        locked_by: null
      })
      .eq('task_type', 'update_informed_pricing')
      .eq('status', 'processing')
      .lt('locked_at', staleTime)
      .select('id');

    if (!staleError && staledTasks && staledTasks.length > 0) {
      console.log(`[processUpdateInformedPricingTask] ✅ Unlocked ${staledTasks.length} stale locked tasks (locked for >30 min)`);
    }

    // RATE LIMITING STEP 1: Check if there are pending tasks scheduled in the future (from 429 reschedules)
    console.log(`[processUpdateInformedPricingTask] Checking for tasks rescheduled due to rate limits...`);

    const { data: futureScheduledTasks, error: futureError } = await supabase
      .from('t_task_queue')
      .select('id, scheduled_at')
      .eq('task_type', 'update_informed_pricing')
      .eq('status', 'pending')
      .gt('scheduled_at', now.toISOString())
      .order('scheduled_at', { ascending: true })
      .limit(1);

    if (!futureError && futureScheduledTasks && futureScheduledTasks.length > 0) {
      const earliestFutureTask = futureScheduledTasks[0];
      const scheduledTime = new Date(earliestFutureTask.scheduled_at);
      const minutesUntil = Math.ceil((scheduledTime - now) / 1000 / 60);

      console.log(`[processUpdateInformedPricingTask] ⏸️  Found tasks scheduled for future: ${earliestFutureTask.scheduled_at} (in ${minutesUntil} minutes)`);
      console.log(`[processUpdateInformedPricingTask] ⏸️  This indicates a previous 429 rate limit response`);
      console.log(`[processUpdateInformedPricingTask] ⏸️  Rescheduling current tasks to match...`);

      // Reschedule all ready tasks to match the future scheduled time
      const { data: tasksToReschedule, error: fetchError } = await supabase
        .from('t_task_queue')
        .select('id')
        .eq('task_type', 'update_informed_pricing')
        .in('status', ['pending', 'processing'])
        .lte('scheduled_at', now.toISOString());

      if (!fetchError && tasksToReschedule && tasksToReschedule.length > 0) {
        const taskIds = tasksToReschedule.map(t => t.id);

        const resultData = {
          message: 'Rate limit detected - task rescheduled to respect 429 backoff',
          rescheduled_to: scheduledTime.toISOString()
        };

        const { error: rescheduleError } = await supabase
          .from('t_task_queue')
          .update({
            status: 'pending',
            scheduled_at: scheduledTime.toISOString(),
            locked_at: null,
            locked_by: null,
            result: JSON.stringify(resultData),
            processed_at: new Date().toISOString()
          })
          .in('id', taskIds);

        if (!rescheduleError) {
          console.log(`[processUpdateInformedPricingTask] ✅ Rescheduled ${taskIds.length} tasks to ${scheduledTime.toISOString()}`);
        }
      }

      return; // Exit early - respect the 429 reschedule time
    }

    // RATE LIMITING STEP 2: Check if we sent to Informed API in the last 15 minutes
    const fifteenMinutesAgo = new Date(now.getTime() - 15 * 60 * 1000).toISOString();

    console.log(`[processUpdateInformedPricingTask] Checking for recent Informed API calls (since ${fifteenMinutesAgo})...`);

    const { data: recentCompletedTasks, error: recentError } = await supabase
      .from('t_task_queue')
      .select('id, processed_at, result')
      .eq('task_type', 'update_informed_pricing')
      .eq('status', 'completed')
      .gte('processed_at', fifteenMinutesAgo)
      .order('processed_at', { ascending: false })
      .limit(1);

    if (recentError) {
      console.error(`[processUpdateInformedPricingTask] Error checking recent tasks:`, recentError);
      // Continue anyway - don't fail just because we can't check
    }

    // Check if any recent task actually sent data to Informed (has informed_response in result)
    let lastInformedApiCall = null;
    if (recentCompletedTasks && recentCompletedTasks.length > 0) {
      for (const recentTask of recentCompletedTasks) {
        const result = typeof recentTask.result === 'string' ? JSON.parse(recentTask.result) : recentTask.result;
        if (result && result.informed_response) {
          lastInformedApiCall = recentTask.processed_at;
          console.log(`[processUpdateInformedPricingTask] Found recent Informed API call at ${lastInformedApiCall} (task ${recentTask.id})`);
          break;
        }
      }
    }

    // If we found a recent API call, reschedule all pending tasks for 15 minutes after that call
    if (lastInformedApiCall) {
      const lastCallTime = new Date(lastInformedApiCall);
      const nextAllowedTime = new Date(lastCallTime.getTime() + 15 * 60 * 1000);
      const now = new Date();

      if (now < nextAllowedTime) {
        const waitMinutes = Math.ceil((nextAllowedTime - now) / 1000 / 60);
        console.log(`[processUpdateInformedPricingTask] ⏸️  Rate limit: Last Informed API call was at ${lastInformedApiCall}`);
        console.log(`[processUpdateInformedPricingTask] ⏸️  Next allowed time: ${nextAllowedTime.toISOString()} (in ${waitMinutes} minutes)`);
        console.log(`[processUpdateInformedPricingTask] ⏸️  Rescheduling all pending tasks...`);

        // Find all pending/processing tasks and reschedule them
        const { data: tasksToReschedule, error: fetchError } = await supabase
          .from('t_task_queue')
          .select('id')
          .eq('task_type', 'update_informed_pricing')
          .in('status', ['pending', 'processing'])
          .lte('scheduled_at', now.toISOString());

        if (fetchError) {
          console.error(`[processUpdateInformedPricingTask] Error fetching tasks to reschedule:`, fetchError);
        } else if (tasksToReschedule && tasksToReschedule.length > 0) {
          const taskIds = tasksToReschedule.map(t => t.id);

          const { error: rescheduleError } = await supabase
            .from('t_task_queue')
            .update({
              status: 'pending',
              scheduled_at: nextAllowedTime.toISOString(),
              locked_at: null,
              locked_by: null
            })
            .in('id', taskIds);

          if (rescheduleError) {
            console.error(`[processUpdateInformedPricingTask] Error rescheduling tasks:`, rescheduleError);
          } else {
            console.log(`[processUpdateInformedPricingTask] ✅ Rescheduled ${taskIds.length} tasks to ${nextAllowedTime.toISOString()}`);
          }
        }

        return; // Exit early - tasks have been rescheduled
      }
    }

    console.log(`[processUpdateInformedPricingTask] ✅ Rate limit check passed - proceeding with batch processing`);

    // Find all update_informed_pricing tasks that are ready to process
    // This includes tasks that are 'pending' OR already 'processing' (locked by the worker)
    console.log(`[processUpdateInformedPricingTask] Looking for all update_informed_pricing tasks ready to process...`);

    const { data: pendingTasks, error: tasksError } = await supabase
      .from('t_task_queue')
      .select('id, payload, status, created_at')
      .eq('task_type', 'update_informed_pricing')
      .in('status', ['pending', 'processing'])
      .lte('scheduled_at', new Date().toISOString())
      .order('id', { ascending: true });

    if (tasksError) {
      console.error(`[processUpdateInformedPricingTask] Error fetching tasks:`, tasksError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to fetch tasks: ${tasksError.message}`,
        error: tasksError.message
      });
      return;
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log(`[processUpdateInformedPricingTask] No tasks found to process - checking if already processed recently...`);

      // Check if this task's listing was already processed by another task in the batch
      // Look for recently completed update_informed_pricing tasks for the same listing_sku
      let listingSkuFromPayload;
      try {
        const payload = typeof task.payload === 'string' ? JSON.parse(task.payload) : task.payload;
        listingSkuFromPayload = payload?.listing_sku;
      } catch (e) {
        console.error(`[processUpdateInformedPricingTask] Error parsing payload:`, e);
      }

      if (listingSkuFromPayload) {
        // Look for tasks completed in the last 2 minutes for the same listing_sku
        const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000).toISOString();

        const { data: recentTasks, error: recentError } = await supabase
          .from('t_task_queue')
          .select('id, processed_at, result')
          .eq('task_type', 'update_informed_pricing')
          .eq('status', 'completed')
          .gte('processed_at', twoMinutesAgo)
          .neq('id', task.id)
          .order('processed_at', { ascending: false })
          .limit(50);

        if (!recentError && recentTasks && recentTasks.length > 0) {
          // Check if any of these tasks processed our listing_sku
          for (const recentTask of recentTasks) {
            try {
              const result = typeof recentTask.result === 'string'
                ? JSON.parse(recentTask.result)
                : recentTask.result;

              // Check if this task successfully processed our listing (direct match)
              if (result && result.listing_sku === listingSkuFromPayload && result.message && result.message.includes('Successfully updated')) {
                console.log(`[processUpdateInformedPricingTask] Found that listing ${listingSkuFromPayload} was already processed by task ${recentTask.id}`);
                await updateTaskStatus(task.id, 'completed', {
                  message: `Already processed by task ${recentTask.id} in batch`,
                  listing_sku: listingSkuFromPayload,
                  processed_by_task_id: recentTask.id,
                  processed_at: recentTask.processed_at,
                  note: 'This listing was included in a batch processed by another task'
                });
                return;
              }

              // Check if this task processed our listing as part of a batch (check all_processed_skus array)
              if (result && result.all_processed_skus && Array.isArray(result.all_processed_skus)) {
                if (result.all_processed_skus.includes(listingSkuFromPayload)) {
                  console.log(`[processUpdateInformedPricingTask] Found that listing ${listingSkuFromPayload} was processed in batch by task ${recentTask.id}`);
                  await updateTaskStatus(task.id, 'completed', {
                    message: `Already processed by task ${recentTask.id} in batch of ${result.batch_size} listings`,
                    listing_sku: listingSkuFromPayload,
                    processed_by_task_id: recentTask.id,
                    processed_at: recentTask.processed_at,
                    batch_size: result.batch_size,
                    note: 'This listing was included in a batch processed by another task'
                  });
                  return;
                }
              }

              // Also check if the listing was skipped (not uploaded to Amazon)
              if (result && result.listing_sku === listingSkuFromPayload && result.skipped === true) {
                console.log(`[processUpdateInformedPricingTask] Found that listing ${listingSkuFromPayload} was skipped by task ${recentTask.id}`);
                await updateTaskStatus(task.id, 'completed', {
                  message: `Already processed (skipped) by task ${recentTask.id} - not uploaded to Amazon`,
                  listing_sku: listingSkuFromPayload,
                  skipped: true,
                  processed_by_task_id: recentTask.id,
                  processed_at: recentTask.processed_at,
                  reason: result.reason || 'Not uploaded to Amazon'
                });
                return;
              }
            } catch (e) {
              // Skip tasks with unparseable results
              continue;
            }
          }
        }
      }

      // If we didn't find a recent task that processed this listing, return generic message
      await updateTaskStatus(task.id, 'completed', {
        message: 'No tasks to process - likely already processed by another worker instance',
        listing_sku: listingSkuFromPayload || 'unknown',
        tasks_processed: 0,
        note: 'This can happen when multiple workers are running and one processes the batch before the other'
      });
      return;
    }

    console.log(`[processUpdateInformedPricingTask] Found ${pendingTasks.length} update_informed_pricing tasks to process`);

    // Mark all tasks as processing (if not already)
    const taskIds = pendingTasks.map(t => t.id);
    console.log(`[processUpdateInformedPricingTask] Ensuring ${taskIds.length} tasks are marked as processing...`);

    const { error: updateError } = await supabase
      .from('t_task_queue')
      .update({
        status: 'processing',
        locked_at: new Date().toISOString(),
        locked_by: 'batch_update_informed_pricing'
      })
      .in('id', taskIds)
      .eq('status', 'pending'); // Only update if still pending

    if (updateError) {
      console.error(`[processUpdateInformedPricingTask] Error marking tasks as processing:`, updateError);
      // Don't fail here - tasks may already be processing
    }

    // Extract listing_skus from all tasks
    const listingSkus = [];
    const taskPayloadMap = new Map(); // Map listing_sku to task_id for later updates
    const taskCreatedAtMap = new Map(); // Map listing_sku to created_at for MV staleness check

    for (const t of pendingTasks) {
      try {
        let payload;
        if (typeof t.payload === 'object' && t.payload !== null) {
          payload = t.payload;
        } else if (typeof t.payload === 'string') {
          payload = JSON.parse(t.payload);
        } else {
          console.error(`[processUpdateInformedPricingTask] Task ${t.id} has invalid payload type`);
          continue;
        }

        const { listing_sku } = payload;
        if (listing_sku) {
          listingSkus.push(listing_sku);
          taskPayloadMap.set(listing_sku, t.id);
          taskCreatedAtMap.set(listing_sku, t.created_at);
        } else {
          console.error(`[processUpdateInformedPricingTask] Task ${t.id} missing listing_sku in payload`);
          // Mark this task as error
          await updateTaskStatus(t.id, 'error', {
            message: 'Missing listing_sku in payload',
            error: 'listing_sku is required'
          });
        }
      } catch (parseError) {
        console.error(`[processUpdateInformedPricingTask] Error parsing payload for task ${t.id}:`, parseError);
        await updateTaskStatus(t.id, 'error', {
          message: `Failed to parse payload: ${parseError.message}`,
          error: parseError.message
        });
      }
    }

    if (listingSkus.length === 0) {
      console.log(`[processUpdateInformedPricingTask] No valid listing_skus found in pending tasks`);
      await updateTaskStatus(task.id, 'completed', {
        message: 'No valid listing_skus to process',
        tasks_processed: 0
      });
      return;
    }

    console.log(`[processUpdateInformedPricingTask] Processing ${listingSkus.length} listing_skus:`, listingSkus);

    // Separate listings by type: FM/FN (RPRO discs) vs DGACC (accessories)
    // DGACC listings have format: FBMDGA_*, FBADGA_*, or DGACC*
    const rproSkus = listingSkus.filter(sku => sku.startsWith('FM') || sku.startsWith('FN'));
    const dgaccSkus = listingSkus.filter(sku =>
      sku.startsWith('FBMDGA_') ||
      sku.startsWith('FBADGA_') ||
      sku.startsWith('DGACC')
    );

    console.log(`[processUpdateInformedPricingTask] Looking up pricing data for ${listingSkus.length} listings (${rproSkus.length} RPRO, ${dgaccSkus.length} DGACC)...`);

    // Look up RPRO pricing (FM/FN listings)
    // Chunk the query to avoid hitting Supabase .in() limits (typically 100-1000 items)
    let rproPricingData = [];
    if (rproSkus.length > 0) {
      const chunkSize = 100;
      for (let i = 0; i < rproSkus.length; i += chunkSize) {
        const chunk = rproSkus.slice(i, i + chunkSize);
        const { data, error } = await supabase
          .from('v_rpro_informed_pricing_calculations')
          .select('listing_sku, map_price, max_price_for_informed_fbm, cost_for_informed_fbm, max_price_for_informed_fba, cost_for_informed_fba')
          .in('listing_sku', chunk);

        if (error) {
          console.error(`[processUpdateInformedPricingTask] Error looking up RPRO pricing data (chunk ${i / chunkSize + 1}):`, error);
          // Mark RPRO tasks as error
          for (const sku of chunk) {
            const taskId = taskPayloadMap.get(sku);
            if (taskId) {
              await updateTaskStatus(taskId, 'error', {
                message: `Failed to look up RPRO pricing data: ${error.message}`,
                error: error.message
              });
            }
          }
        } else {
          rproPricingData = rproPricingData.concat(data || []);
          console.log(`[processUpdateInformedPricingTask] Found RPRO pricing data for ${(data || []).length} listings in chunk ${i / chunkSize + 1}`);
        }
      }
      console.log(`[processUpdateInformedPricingTask] Total RPRO pricing data: ${rproPricingData.length} listings`);
    }

    // Look up DGACC pricing (DGACC listings) - uses materialized view for performance
    let dgaccPricingData = [];
    if (dgaccSkus.length > 0) {
      // Find the earliest created_at among DGACC tasks - MV must be refreshed after this time
      const dgaccCreatedAts = dgaccSkus.map(sku => taskCreatedAtMap.get(sku)).filter(Boolean);
      const earliestDgaccTaskCreatedAt = dgaccCreatedAts.length > 0
        ? new Date(Math.min(...dgaccCreatedAts.map(d => new Date(d).getTime())))
        : new Date();

      console.log(`[processUpdateInformedPricingTask] Checking MV freshness for DGACC tasks (earliest task created: ${earliestDgaccTaskCreatedAt.toISOString()})...`);

      // Check if MV needs refresh - call the ensure function
      const { data: refreshResult, error: refreshError } = await supabase
        .rpc('ensure_mv_dgacc_pricing_fresh', { min_refresh_after: earliestDgaccTaskCreatedAt.toISOString() });

      if (refreshError) {
        console.error(`[processUpdateInformedPricingTask] Error checking MV freshness:`, refreshError);
        // If we can't check freshness, reschedule DGACC tasks to be safe
        const rescheduleTime = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes from now
        console.log(`[processUpdateInformedPricingTask] Rescheduling ${dgaccSkus.length} DGACC tasks to ${rescheduleTime.toISOString()}`);

        for (const sku of dgaccSkus) {
          const taskId = taskPayloadMap.get(sku);
          if (taskId) {
            const resultData = {
              message: 'MV freshness check failed, task rescheduled',
              error: refreshError.message,
              rescheduled_to: rescheduleTime.toISOString()
            };
            console.log(`[processUpdateInformedPricingTask] Updating task ${taskId} (type: ${typeof taskId}) with result:`, resultData);

            const updatePayload = {
              status: 'pending',
              scheduled_at: rescheduleTime.toISOString(),
              locked_at: null,
              locked_by: null,
              result: JSON.stringify(resultData),
              processed_at: new Date().toISOString()
            };
            console.log(`[processUpdateInformedPricingTask] Update payload:`, updatePayload);

            const { error: updateError, data: updateData, status: updateStatus } = await supabase
              .from('t_task_queue')
              .update(updatePayload)
              .eq('id', parseInt(taskId));

            if (updateError) {
              console.error(`[processUpdateInformedPricingTask] Error rescheduling task ${taskId}:`, updateError);
            } else {
              console.log(`[processUpdateInformedPricingTask] Successfully rescheduled task ${taskId} for ${sku} to ${rescheduleTime.toISOString()}. Status: ${updateStatus}, rows: ${updateData?.length || 0}`);
            }
          }
        }
        // Continue with RPRO tasks only
        dgaccSkus.length = 0;
      } else {
        // refreshResult is now a string: 'fresh' | 'refreshed' | 'refresh_in_progress'
        const mvStatus = refreshResult;
        console.log(`[processUpdateInformedPricingTask] MV freshness check result: ${mvStatus}`);

        // If refresh is in progress, reschedule DGACC tasks
        if (mvStatus === 'refresh_in_progress') {
          const rescheduleTime = new Date(Date.now() + 2 * 60 * 1000); // 2 minutes from now
          console.log(`[processUpdateInformedPricingTask] MV refresh in progress, rescheduling ${dgaccSkus.length} DGACC tasks to ${rescheduleTime.toISOString()}`);

          for (const sku of dgaccSkus) {
            const taskId = taskPayloadMap.get(sku);
            if (taskId) {
              const resultData = {
                message: 'MV refresh in progress, task rescheduled',
                rescheduled_to: rescheduleTime.toISOString()
              };
              console.log(`[processUpdateInformedPricingTask] Updating task ${taskId} (type: ${typeof taskId}) with result:`, resultData);

              const updatePayload = {
                status: 'pending',
                scheduled_at: rescheduleTime.toISOString(),
                locked_at: null,
                locked_by: null,
                result: JSON.stringify(resultData),
                processed_at: new Date().toISOString()
              };
              console.log(`[processUpdateInformedPricingTask] Update payload:`, updatePayload);

              const { error: updateError, data: updateData, status: updateStatus } = await supabase
                .from('t_task_queue')
                .update(updatePayload)
                .eq('id', parseInt(taskId));

              if (updateError) {
                console.error(`[processUpdateInformedPricingTask] Error rescheduling task ${taskId}:`, updateError);
              } else {
                console.log(`[processUpdateInformedPricingTask] Successfully rescheduled task ${taskId} for ${sku} to ${rescheduleTime.toISOString()}. Status: ${updateStatus}, rows: ${updateData?.length || 0}`);
              }
            }
          }
          // Continue with RPRO tasks only
          dgaccSkus.length = 0;
        } else {
          // MV is fresh or was just refreshed - query it
          // Chunk the query to avoid hitting Supabase .in() limits (typically 100-1000 items)
          const chunkSize = 100;
          for (let i = 0; i < dgaccSkus.length; i += chunkSize) {
            const chunk = dgaccSkus.slice(i, i + chunkSize);
            const { data, error } = await supabase
              .from('mv_dgacc_informed_pricing_calculations')
              .select('listing_sku, map_price, max_price_for_informed_fbm, cost_for_informed_fbm, max_price_for_informed_fba, cost_for_informed_fba')
              .in('listing_sku', chunk);

            if (error) {
              console.error(`[processUpdateInformedPricingTask] Error looking up DGACC pricing data (chunk ${i / chunkSize + 1}):`, error);
              // Mark DGACC tasks as error
              for (const sku of chunk) {
                const taskId = taskPayloadMap.get(sku);
                if (taskId) {
                  await updateTaskStatus(taskId, 'error', {
                    message: `Failed to look up DGACC pricing data: ${error.message}`,
                    error: error.message
                  });
                }
              }
            } else {
              dgaccPricingData = dgaccPricingData.concat(data || []);
              console.log(`[processUpdateInformedPricingTask] Found DGACC pricing data for ${(data || []).length} listings in chunk ${i / chunkSize + 1} (MV was ${mvStatus})`);
            }
          }
          console.log(`[processUpdateInformedPricingTask] Total DGACC pricing data: ${dgaccPricingData.length} listings`);
        }
      }
    }

    // Combine pricing data from both sources
    const pricingData = [...rproPricingData, ...dgaccPricingData];

    if (pricingData.length === 0) {
      console.error(`[processUpdateInformedPricingTask] No pricing data found for any listings`);
      return; // All tasks already marked as error above
    }

    console.log(`[processUpdateInformedPricingTask] Found total pricing data for ${pricingData.length} listings`);

    // Filter out listings that haven't been uploaded to Amazon yet
    // We only update Informed pricing for listings that are actually on Amazon
    // Check both t_amaz_acc_listings (RPRO) and t_amaz_dgacc_listings (DGACC)
    console.log(`[processUpdateInformedPricingTask] Checking which listings have been uploaded to Amazon...`);

    // Check RPRO listings (FM/FN) - chunk to avoid .in() limits
    let rproUploadedListings = [];
    if (rproSkus.length > 0) {
      const chunkSize = 100;
      for (let i = 0; i < rproSkus.length; i += chunkSize) {
        const chunk = rproSkus.slice(i, i + chunkSize);
        const { data, error } = await supabase
          .from('t_amaz_acc_listings')
          .select('listing_sku')
          .in('listing_sku', chunk)
          .not('uploaded_to_amazon_at', 'is', null);

        if (error) {
          console.error(`[processUpdateInformedPricingTask] Error checking RPRO uploaded listings (chunk ${i / chunkSize + 1}):`, error);
        } else {
          rproUploadedListings = rproUploadedListings.concat(data || []);
        }
      }
      console.log(`[processUpdateInformedPricingTask] Found ${rproUploadedListings.length} RPRO listings uploaded to Amazon`);
    }

    // Check DGACC listings - chunk to avoid .in() limits
    let dgaccUploadedListings = [];
    if (dgaccSkus.length > 0) {
      const chunkSize = 100;
      for (let i = 0; i < dgaccSkus.length; i += chunkSize) {
        const chunk = dgaccSkus.slice(i, i + chunkSize);
        const { data, error } = await supabase
          .from('t_amaz_dgacc_listings')
          .select('listing_sku')
          .in('listing_sku', chunk)
          .not('uploaded_to_amazon_at', 'is', null);

        if (error) {
          console.error(`[processUpdateInformedPricingTask] Error checking DGACC uploaded listings (chunk ${i / chunkSize + 1}):`, error);
        } else {
          dgaccUploadedListings = dgaccUploadedListings.concat(data || []);
        }
      }
      console.log(`[processUpdateInformedPricingTask] Found ${dgaccUploadedListings.length} DGACC listings uploaded to Amazon`);
    }

    // Combine uploaded listings from both sources
    const uploadedListingSkus = new Set([
      ...rproUploadedListings.map(l => l.listing_sku),
      ...dgaccUploadedListings.map(l => l.listing_sku)
    ]);
    console.log(`[processUpdateInformedPricingTask] Found total ${uploadedListingSkus.size} listings uploaded to Amazon (out of ${listingSkus.length} total)`);

    // Build CSV content for Informed API
    // Format: sku,marketplace_id,cost,currency,max_price,map_price
    // FM listings use FBM pricing, FN listings use FBA pricing, DGACC listings use FBA pricing
    const csvRows = [];
    const successfulListings = [];
    const failedListings = [];
    const skippedListings = [];

    for (const pricing of pricingData) {
      const { listing_sku, map_price, max_price_for_informed_fbm, cost_for_informed_fbm, max_price_for_informed_fba, cost_for_informed_fba } = pricing;

      // Skip listings that haven't been uploaded to Amazon
      if (!uploadedListingSkus.has(listing_sku)) {
        console.log(`[processUpdateInformedPricingTask] Skipping ${listing_sku} - not uploaded to Amazon yet`);
        skippedListings.push(listing_sku);

        // Mark task as completed (not an error, just not needed yet)
        const taskId = taskPayloadMap.get(listing_sku);
        if (taskId) {
          await updateTaskStatus(taskId, 'completed', {
            message: `Skipped ${listing_sku} - not uploaded to Amazon yet`,
            listing_sku,
            skipped: true,
            reason: 'Not uploaded to Amazon'
          });
        }
        continue;
      }

      // Determine which pricing to use based on listing_sku prefix
      let max_price, cost_for_informed, fulfillment_type;

      if (listing_sku.startsWith('FM')) {
        // FM = Fulfilled by Merchant (FBM) - RPRO discs
        max_price = max_price_for_informed_fbm;
        cost_for_informed = cost_for_informed_fbm;
        fulfillment_type = 'FBM';
      } else if (listing_sku.startsWith('FN')) {
        // FN = Fulfilled by Amazon (FBA) - RPRO discs
        max_price = max_price_for_informed_fba;
        cost_for_informed = cost_for_informed_fba;
        fulfillment_type = 'FBA';
      } else if (listing_sku.startsWith('FBMDGA_')) {
        // FBMDGA = Disc Golf Accessories - FBM
        max_price = max_price_for_informed_fbm;
        cost_for_informed = cost_for_informed_fbm;
        fulfillment_type = 'FBM';
      } else if (listing_sku.startsWith('FBADGA_') || listing_sku.startsWith('DGACC')) {
        // FBADGA/DGACC = Disc Golf Accessories - FBA
        max_price = max_price_for_informed_fba;
        cost_for_informed = cost_for_informed_fba;
        fulfillment_type = 'FBA';
      } else {
        console.error(`[processUpdateInformedPricingTask] Unknown listing type for ${listing_sku} (expected FM, FN, FBMDGA_, FBADGA_, or DGACC prefix)`);
        failedListings.push({
          listing_sku,
          reason: 'Unknown listing type (expected FM, FN, FBMDGA_, FBADGA_, or DGACC prefix)'
        });

        // Mark task as error
        const taskId = taskPayloadMap.get(listing_sku);
        if (taskId) {
          await updateTaskStatus(taskId, 'error', {
            message: `Unknown listing type for ${listing_sku}`,
            error: 'Expected FM, FN, FBMDGA_, FBADGA_, or DGACC prefix'
          });
        }
        continue;
      }

      // Default map_price to 0 if null (consistent with RPRO view behavior)
      const effective_map_price = map_price ?? 0;

      // Validate pricing values (map_price can be 0, but max_price and cost must have values)
      const nullFields = [];
      if (max_price === null || max_price === undefined) nullFields.push(`max_price_for_informed_${fulfillment_type.toLowerCase()}`);
      if (cost_for_informed === null || cost_for_informed === undefined) nullFields.push(`cost_for_informed_${fulfillment_type.toLowerCase()}`);

      if (nullFields.length > 0) {
        console.error(`[processUpdateInformedPricingTask] Invalid ${fulfillment_type} pricing data for ${listing_sku}: null fields: ${nullFields.join(', ')}`);
        failedListings.push({
          listing_sku,
          reason: `Invalid ${fulfillment_type} pricing data (null: ${nullFields.join(', ')})`
        });

        // Mark task as error
        const taskId = taskPayloadMap.get(listing_sku);
        if (taskId) {
          await updateTaskStatus(taskId, 'error', {
            message: `Invalid ${fulfillment_type} pricing data for ${listing_sku}`,
            error: `Null fields: ${nullFields.join(', ')}`
          });
        }
        continue;
      }

      // Add to CSV
      csvRows.push(`${listing_sku},9432,${cost_for_informed},USD,${max_price},${effective_map_price}`);
      successfulListings.push({ listing_sku, fulfillment_type });
    }

    // Check for listings that weren't found in pricing data
    for (const listing_sku of listingSkus) {
      if (!pricingData.find(p => p.listing_sku === listing_sku) && !skippedListings.includes(listing_sku)) {
        console.error(`[processUpdateInformedPricingTask] No pricing data found for ${listing_sku}`);

        // Determine which view based on SKU prefix
        const isDgacc = listing_sku.startsWith('FBMDGA_') ||
                        listing_sku.startsWith('FBADGA_') ||
                        listing_sku.startsWith('DGACC');
        const viewName = isDgacc
          ? 'mv_dgacc_informed_pricing_calculations'
          : 'v_rpro_informed_pricing_calculations';

        failedListings.push({
          listing_sku,
          reason: `Not found in ${viewName}`
        });

        // Mark task as error
        const taskId = taskPayloadMap.get(listing_sku);
        if (taskId) {
          await updateTaskStatus(taskId, 'error', {
            message: `No pricing data found for ${listing_sku}`,
            error: `Listing not found in ${viewName}`
          });
        }
      }
    }

    if (csvRows.length === 0) {
      console.log(`[processUpdateInformedPricingTask] No valid listings to upload to Informed`);
      console.log(`[processUpdateInformedPricingTask] Skipped: ${skippedListings.length}, Failed: ${failedListings.length}`);
      await updateTaskStatus(task.id, 'completed', {
        message: 'No valid listings to upload',
        tasks_processed: 0,
        skipped_listings: skippedListings,
        failed_listings: failedListings
      });
      return;
    }

    const fbmCount = successfulListings.filter(l => l.fulfillment_type === 'FBM').length;
    const fbaCount = successfulListings.filter(l => l.fulfillment_type === 'FBA').length;

    const csvHeader = 'sku,marketplace_id,cost,currency,max_price,map_price';
    const csvContent = `${csvHeader}\n${csvRows.join('\n')}`;

    console.log(`[processUpdateInformedPricingTask] Uploading ${csvRows.length} listings to Informed (${fbmCount} FBM, ${fbaCount} FBA)`);
    console.log(`[processUpdateInformedPricingTask] Skipped ${skippedListings.length} listings not uploaded to Amazon`);
    console.log(`[processUpdateInformedPricingTask] CSV preview (first 5 rows):\n${csvHeader}\n${csvRows.slice(0, 5).join('\n')}`);

    // Upload to Informed API
    try {
      const response = await fetch(INFORMED_FEED_URL, {
        method: 'POST',
        headers: {
          'x-api-key': INFORMED_API_KEY,
          'Content-Type': 'text/csv',
          'Accept': 'application/json'
        },
        body: csvContent
      });

      console.log(`[processUpdateInformedPricingTask] Informed API response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[processUpdateInformedPricingTask] Informed API error: ${errorText}`);

        // Handle 429 Rate Limit - reschedule tasks based on Retry-After header
        if (response.status === 429) {
          console.log(`[processUpdateInformedPricingTask] 🔴 Rate limit hit (429) - checking Retry-After header...`);

          const retryAfter = response.headers.get('Retry-After');
          let retryTime;

          if (retryAfter) {
            // Retry-After can be either a date string or seconds
            if (isNaN(retryAfter)) {
              // It's a date string like "Tue, 28 Oct 2025 00:41:50 GMT"
              retryTime = new Date(retryAfter);
            } else {
              // It's seconds from now
              retryTime = new Date(Date.now() + parseInt(retryAfter) * 1000);
            }

            // Add 15 minutes buffer to be safe (in case there are other Informed uploads)
            retryTime = new Date(retryTime.getTime() + 15 * 60 * 1000);

            console.log(`[processUpdateInformedPricingTask] Retry-After header: ${retryAfter}`);
            console.log(`[processUpdateInformedPricingTask] Calculated retry time (with +15 min buffer): ${retryTime.toISOString()}`);
          } else {
            // No Retry-After header, default to 1 hour from now
            retryTime = new Date(Date.now() + 60 * 60 * 1000);
            console.log(`[processUpdateInformedPricingTask] No Retry-After header, defaulting to 1 hour: ${retryTime.toISOString()}`);
          }

          // Reschedule all tasks to the retry time
          const taskIds = successfulListings.map(l => taskPayloadMap.get(l.listing_sku)).filter(id => id);

          if (taskIds.length > 0) {
            const { error: rescheduleError } = await supabase
              .from('t_task_queue')
              .update({
                status: 'pending',
                scheduled_at: retryTime.toISOString(),
                locked_at: null,
                locked_by: null
              })
              .in('id', taskIds);

            if (rescheduleError) {
              console.error(`[processUpdateInformedPricingTask] Error rescheduling tasks:`, rescheduleError);
            } else {
              console.log(`[processUpdateInformedPricingTask] ✅ Rescheduled ${taskIds.length} tasks to ${retryTime.toISOString()}`);
            }
          }

          return; // Exit - tasks have been rescheduled
        }

        // For other errors (not 429), mark tasks as error
        for (const listing_sku of successfulListings) {
          const taskId = taskPayloadMap.get(listing_sku);
          if (taskId) {
            await updateTaskStatus(taskId, 'error', {
              message: `Informed API error (${response.status}): ${errorText}`,
              error: errorText,
              status_code: response.status
            });
          }
        }
        return;
      }

      const result = await response.json();
      console.log(`[processUpdateInformedPricingTask] Successfully uploaded ${successfulListings.length} listings to Informed (${fbmCount} FBM, ${fbaCount} FBA):`, result);

      // Mark all successful listings' tasks as completed
      const allProcessedSkus = successfulListings.map(l => l.listing_sku);

      for (const listingInfo of successfulListings) {
        const taskId = taskPayloadMap.get(listingInfo.listing_sku);
        if (taskId) {
          await updateTaskStatus(taskId, 'completed', {
            message: `Successfully updated ${listingInfo.fulfillment_type} pricing for ${listingInfo.listing_sku} (batch upload)`,
            listing_sku: listingInfo.listing_sku,
            fulfillment_type: listingInfo.fulfillment_type,
            batch_size: successfulListings.length,
            fbm_count: fbmCount,
            fba_count: fbaCount,
            all_processed_skus: allProcessedSkus,
            informed_response: result
          });
        }
      }

      console.log(`[processUpdateInformedPricingTask] Batch complete: ${successfulListings.length} successful (${fbmCount} FBM, ${fbaCount} FBA), ${skippedListings.length} skipped, ${failedListings.length} failed`);

    } catch (apiError) {
      console.error(`[processUpdateInformedPricingTask] Error calling Informed API:`, apiError);

      // Mark all successful listings' tasks as error
      for (const listingInfo of successfulListings) {
        const taskId = taskPayloadMap.get(listingInfo.listing_sku);
        if (taskId) {
          await updateTaskStatus(taskId, 'error', {
            message: `Error calling Informed API: ${apiError.message}`,
            error: apiError.message,
            fulfillment_type: listingInfo.fulfillment_type
          });
        }
      }
      return;
    }

  } catch (error) {
    console.error(`[processUpdateInformedPricingTask] Unexpected error processing task ${task.id}:`, error);
    await logError(task.id, error.message);
    await updateTaskStatus(task.id, 'error', {
      message: `Unexpected error: ${error.message}`,
      error: error.message
    });
  }
}

export default processUpdateInformedPricingTask;

