import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function testQuery() {
  try {
    console.log('Testing t_images query for variant 4878...\n');

    const variantId = 4878;

    // Test 1: Query with service role key
    console.log('Test 1: Query with service role key');
    const { data: result1, error: err1 } = await supabase
      .from('t_images')
      .select('id, image_verified')
      .eq('table_name', 't_product_variants')
      .eq('record_id', variantId)
      .maybeSingle();

    if (err1) {
      console.error('❌ Error:', err1);
    } else {
      console.log('✅ Result:', JSON.stringify(result1, null, 2));
    }
    console.log();

    // Test 2: Query all columns
    console.log('Test 2: Query all columns');
    const { data: result2, error: err2 } = await supabase
      .from('t_images')
      .select('*')
      .eq('table_name', 't_product_variants')
      .eq('record_id', variantId)
      .maybeSingle();

    if (err2) {
      console.error('❌ Error:', err2);
    } else {
      console.log('✅ Result:', JSON.stringify(result2, null, 2));
    }
    console.log();

    // Test 3: Query without filters
    console.log('Test 3: Query all t_images for t_product_variants');
    const { data: result3, error: err3 } = await supabase
      .from('t_images')
      .select('id, table_name, record_id, image_verified')
      .eq('table_name', 't_product_variants')
      .limit(5);

    if (err3) {
      console.error('❌ Error:', err3);
    } else {
      console.log('✅ Result:', JSON.stringify(result3, null, 2));
    }

  } catch (err) {
    console.error('❌ Exception:', err.message);
  }
}

testQuery();

