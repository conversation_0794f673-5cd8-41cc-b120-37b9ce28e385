const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔗 VEEQO CHANNEL SELLABLE RELINKER');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    console.log(`   Request: ${method} ${endpoint}`);
    if (data) {
      console.log(`   Body: ${JSON.stringify(data, null, 2)}`);
    }
    
    const response = await fetch(endpoint, options);

    console.log(`   Response Status: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.log(`   Response Body: ${errorText}`);
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }

    // 204 No Content is a success response with no body
    if (response.status === 204 || method === 'DELETE') {
      console.log(`   Response: Success (No Content)`);
      return { success: true, data: null };
    }

    const result = await response.json();
    console.log(`   Response: Success`);
    return { success: true, data: result };
  } catch (error) {
    console.log(`   Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Function to get product details
async function getProductDetails(productId) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!result.success) {
    console.error(`❌ Error fetching product: ${result.error}`);
    return null;
  }
  
  return result.data;
}

// Function to find sellable by SKU
async function findSellableBySku(sku) {
  console.log(`\n🔍 Searching for SKU: ${sku}`);
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products?query=${encodeURIComponent(sku)}`);
  
  if (!result.success) {
    console.error(`   ❌ Error searching: ${result.error}`);
    return null;
  }
  
  for (const product of result.data) {
    if (product.sellables) {
      const sellable = product.sellables.find(s => s.sku_code === sku);
      if (sellable) {
        console.log(`   ✅ Found: Product ID ${product.id}, Sellable ID ${sellable.id}`);
        return { product, sellable };
      }
    }
  }
  
  console.log(`   ❌ Not found`);
  return null;
}

// Function to update channel sellable
async function updateChannelSellable(channelSellableId, newSellableId) {
  console.log(`\n🔗 Updating channel sellable ${channelSellableId} to point to sellable ${newSellableId}...`);
  
  // Try different endpoints
  const attempts = [
    {
      endpoint: `https://api.veeqo.com/channel_sellables/${channelSellableId}`,
      method: 'PUT',
      data: { channel_sellable: { sellable_id: newSellableId } }
    },
    {
      endpoint: `https://api.veeqo.com/channel_sellables/${channelSellableId}`,
      method: 'PATCH',
      data: { channel_sellable: { sellable_id: newSellableId } }
    },
    {
      endpoint: `https://api.veeqo.com/channel_sellables/${channelSellableId}`,
      method: 'PUT',
      data: { sellable_id: newSellableId }
    },
    {
      endpoint: `https://api.veeqo.com/channel_sellables/${channelSellableId}`,
      method: 'PATCH',
      data: { sellable_id: newSellableId }
    }
  ];
  
  for (const attempt of attempts) {
    console.log(`\n📡 Attempt: ${attempt.method}`);
    const result = await makeVeeqoRequest(attempt.endpoint, attempt.method, attempt.data);
    
    if (result.success) {
      console.log(`   ✅ Success!`);
      return { success: true, data: result.data };
    } else {
      console.log(`   ❌ Failed: ${result.error}`);
    }
  }
  
  console.log(`\n❌ All attempts failed`);
  return { success: false, error: 'All attempts failed' };
}

// Main function
async function relinkChannelSellable(currentProductId, channelProductId, channelSellableId, newSku) {
  console.log(`\n🔗 Relinking channel sellable ${channelSellableId} to SKU ${newSku}`);
  console.log('─'.repeat(50));
  
  // Get current product details
  console.log(`\n📦 Fetching current product ${currentProductId}...`);
  const currentProduct = await getProductDetails(currentProductId);
  
  if (!currentProduct) {
    return false;
  }
  
  console.log(`   Title: ${currentProduct.title}`);
  
  // Find the channel product
  const channelProduct = currentProduct.channel_products?.find(cp => cp.id === parseInt(channelProductId));
  
  if (!channelProduct) {
    console.error(`❌ Channel product ${channelProductId} not found`);
    return false;
  }
  
  console.log(`\n📺 Channel Product:`);
  console.log(`   ID: ${channelProduct.id}`);
  console.log(`   Channel: ${channelProduct.channel?.name || 'Unknown'}`);
  console.log(`   Status: ${channelProduct.status}`);
  console.log(`   Remote Title: ${channelProduct.remote_title}`);
  
  // Find the channel sellable
  const channelSellable = channelProduct.channel_sellables?.find(cs => cs.id === parseInt(channelSellableId));
  
  if (!channelSellable) {
    console.error(`❌ Channel sellable ${channelSellableId} not found`);
    return false;
  }
  
  console.log(`\n📋 Channel Sellable:`);
  console.log(`   ID: ${channelSellable.id}`);
  console.log(`   Remote SKU: ${channelSellable.remote_sku}`);
  console.log(`   Current Sellable ID: ${channelSellable.sellable_id}`);
  
  // Find new sellable
  const newTarget = await findSellableBySku(newSku);
  
  if (!newTarget) {
    console.error(`❌ Could not find target SKU: ${newSku}`);
    return false;
  }
  
  console.log(`\n🎯 Target Sellable:`);
  console.log(`   Product ID: ${newTarget.product.id}`);
  console.log(`   Sellable ID: ${newTarget.sellable.id}`);
  console.log(`   Title: ${newTarget.product.title}`);
  console.log(`   SKU: ${newTarget.sellable.sku_code}`);
  
  // Update the channel sellable
  const result = await updateChannelSellable(channelSellableId, newTarget.sellable.id);
  
  if (result.success) {
    console.log(`\n✅ Successfully relinked channel sellable!`);
    return true;
  } else {
    console.log(`\n❌ Failed to relink channel sellable`);
    return false;
  }
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 4) {
  console.log(`
Usage: node relinkChannelSellable.cjs <current-product-id> <channel-product-id> <channel-sellable-id> <new-sku>

Example:
  node relinkChannelSellable.cjs 116629736 229745894 512790446 R13756

This will relink channel sellable 512790446 (in channel product 229745894) to the sellable for R13756.
`);
  process.exit(1);
}

const currentProductId = args[0];
const channelProductId = args[1];
const channelSellableId = args[2];
const newSku = args[3];

relinkChannelSellable(currentProductId, channelProductId, channelSellableId, newSku)
  .then(success => {
    if (success) {
      console.log(`\n✅ All done!`);
      process.exit(0);
    } else {
      console.log(`\n❌ Operation failed`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

