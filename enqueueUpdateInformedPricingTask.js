// enqueueUpdateInformedPricingTask.js
// Helper script to manually enqueue an update_informed_pricing task

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function enqueueUpdateInformedPricingTask(listingSku) {
  console.log('📋 Enqueueing update_informed_pricing task...');
  console.log('='.repeat(60));

  try {
    // Validate the listing_sku exists in the view
    console.log(`\n🔍 Checking if listing_sku "${listingSku}" exists in v_rpro_size_tier_pricing...`);
    
    const { data: pricingData, error: pricingError } = await supabase
      .from('v_rpro_size_tier_pricing')
      .select('listing_sku, sku, map_price, max_price_for_informed, cost_for_informed')
      .eq('listing_sku', listingSku)
      .single();

    if (pricingError) {
      console.error('❌ Error looking up listing_sku:', pricingError);
      return;
    }

    if (!pricingData) {
      console.error(`❌ listing_sku "${listingSku}" not found in v_rpro_size_tier_pricing`);
      return;
    }

    console.log('✅ Found pricing data:');
    console.log(`   SKU: ${pricingData.sku}`);
    console.log(`   Listing SKU: ${pricingData.listing_sku}`);
    console.log(`   MAP Price: ${pricingData.map_price}`);
    console.log(`   Max Price: ${pricingData.max_price_for_informed}`);
    console.log(`   Cost: ${pricingData.cost_for_informed}`);

    // Enqueue the task
    console.log('\n📝 Enqueueing task...');
    
    const taskData = {
      task_type: 'update_informed_pricing',
      payload: {
        listing_sku: listingSku
      },
      status: 'pending',
      scheduled_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      enqueued_by: 'manual_enqueue_update_informed_pricing'
    };

    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .insert([taskData])
      .select()
      .single();

    if (taskError) {
      console.error('❌ Error enqueueing task:', taskError);
      return;
    }

    console.log(`\n✅ Task enqueued successfully!`);
    console.log(`   Task ID: ${task.id}`);
    console.log(`   Status: ${task.status}`);
    console.log(`   Scheduled at: ${task.scheduled_at}`);
    console.log('\n💡 The task queue worker will process this task automatically.');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Get listing_sku from command line argument
const listingSku = process.argv[2];

if (!listingSku) {
  console.error('❌ Usage: node enqueueUpdateInformedPricingTask.js <listing_sku>');
  console.error('   Example: node enqueueUpdateInformedPricingTask.js R12345');
  process.exit(1);
}

enqueueUpdateInformedPricingTask(listingSku);

