-- Create table for Amazon FBA Fee Preview import
-- This table stores fee estimates for FBA products

CREATE TABLE IF NOT EXISTS it_amaz_fba_fee_preview (
    id SERIAL PRIMARY KEY,

    -- Product identification
    sku TEXT UNIQUE NOT NULL,
    fnsku TEXT,
    asin TEXT,
    amazon_store TEXT,
    product_name TEXT,
    product_group TEXT,
    brand TEXT,
    fulfilled_by TEXT,
    
    -- Pricing
    your_price NUMERIC(10, 2),
    sales_price NUMERIC(10, 2),
    
    -- Dimensions
    longest_side NUMERIC(10, 2),
    median_side NUMERIC(10, 2),
    shortest_side NUMERIC(10, 2),
    length_and_girth NUMERIC(10, 2),
    unit_of_dimension TEXT,
    
    -- Weight
    item_package_weight NUMERIC(10, 2),
    unit_of_weight TEXT,
    
    -- Size tier and currency
    product_size_tier TEXT,
    currency TEXT,
    
    -- Current fees
    estimated_fee_total NUMERIC(10, 2),
    estimated_referral_fee_per_unit NUMERIC(10, 2),
    estimated_variable_closing_fee NUMERIC(10, 2),
    estimated_fixed_closing_fee NUMERIC(10, 2),
    estimated_order_handling_fee_per_order NUMERIC(10, 2),
    estimated_pick_pack_fee_per_unit NUMERIC(10, 2),
    estimated_weight_handling_fee_per_unit NUMERIC(10, 2),
    expected_fulfillment_fee_per_unit NUMERIC(10, 2),
    
    -- Future fees
    estimated_future_fee NUMERIC(10, 2),
    estimated_future_order_handling_fee_per_order NUMERIC(10, 2),
    estimated_future_pick_pack_fee_per_unit NUMERIC(10, 2),
    estimated_future_weight_handling_fee_per_unit NUMERIC(10, 2),
    expected_future_fulfillment_fee_per_unit NUMERIC(10, 2),
    
    -- Audit fields
    created_by TEXT DEFAULT 'system',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_by TEXT DEFAULT 'system',
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index on SKU for faster lookups
CREATE INDEX IF NOT EXISTS idx_it_amaz_fba_fee_preview_sku ON it_amaz_fba_fee_preview(sku);

-- Create index on ASIN for faster lookups
CREATE INDEX IF NOT EXISTS idx_it_amaz_fba_fee_preview_asin ON it_amaz_fba_fee_preview(asin);

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_it_amaz_fba_fee_preview_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_it_amaz_fba_fee_preview_updated_at
    BEFORE UPDATE ON it_amaz_fba_fee_preview
    FOR EACH ROW
    EXECUTE FUNCTION update_it_amaz_fba_fee_preview_updated_at();

-- Add comment to table
COMMENT ON TABLE it_amaz_fba_fee_preview IS 'Amazon FBA Fee Preview report data - contains estimated fees for FBA products';

