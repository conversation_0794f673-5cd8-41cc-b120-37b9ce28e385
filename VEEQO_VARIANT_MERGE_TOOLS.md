# Veeqo Variant Merge Tools

## Overview

This document describes the tools available for merging Veeqo variants (SKUs) and managing channel sellables (listings).

## Main Tool: robustMergeVeeqoVariants.cjs

**Purpose**: Complete end-to-end variant merger with comprehensive error handling and verification.

**Usage**:
```bash
node robustMergeVeeqoVariants.cjs <old-sku> <new-sku> [--delete]
```

**Example**:
```bash
node robustMergeVeeqoVariants.cjs FM13953 R13953 --delete
```

**What it does**:

1. **Step 1**: Finds both variants (old and new)
   - Verifies both SKUs exist
   - Shows product IDs, sellable IDs, and stock levels

2. **Step 2**: Gets channel sellables from old variant's product
   - Retrieves all channel sellables with the old SKU from the old product
   - This happens BEFORE deletion to prevent data loss

3. **Step 3**: Finds other products with old SKU channel sellables
   - Searches through all products in Veeqo
   - Identifies any other products that have listings with the old SKU
   - This handles cases where listings were incorrectly linked to other products

4. **Step 4**: Updates all channel sellables to point to new variant
   - Moves all found channel sellables to the new variant
   - Updates the sellable_id to point to the new variant's sellable
   - Shows detailed progress for each update

5. **Step 5**: Verifies and fixes mismatches in new variant's product
   - Checks all channel sellables in the new product
   - Identifies orphaned sellables (pointing to deleted variants)
   - Identifies SKU mismatches (remote_sku doesn't match actual sellable SKU)
   - Automatically fixes by looking up correct sellables and updating links

6. **Step 6**: Deletes old variant (if --delete flag used)
   - Sets stock to 0
   - Deletes the old product

**Key Features**:
- ✅ Prevents data loss by getting channel sellables before deletion
- ✅ Searches ALL products for misplaced listings
- ✅ Automatically fixes orphaned and mismatched channel sellables
- ✅ Comprehensive error handling and reporting
- ✅ Detailed progress logging

## Supporting Tools

### 1. mergeVeeqoVariants.cjs (Original)

**Purpose**: Original variant merger (less robust, kept for reference)

**Usage**:
```bash
node mergeVeeqoVariants.cjs <old-sku> <new-sku> --delete
```

**Note**: Use `robustMergeVeeqoVariants.cjs` instead for better reliability.

### 2. fixMismatchedChannelSellables.cjs

**Purpose**: Find and fix channel sellables that point to wrong variants

**Usage**:
```bash
node fixMismatchedChannelSellables.cjs <product-id>
```

**Example**:
```bash
node fixMismatchedChannelSellables.cjs 86512012
```

**What it does**:
- Identifies channel sellables where remote_sku doesn't match the actual sellable's SKU
- Identifies orphaned channel sellables (pointing to deleted sellables)
- Looks up correct sellable for each remote_sku
- Updates channel sellables to point to correct sellables

### 3. updateOrphanedToCorrectSellable.cjs

**Purpose**: Update all channel sellables with a specific remote_sku to point to a specific sellable

**Usage**:
```bash
node updateOrphanedToCorrectSellable.cjs <product-id> <remote-sku> <correct-sellable-id>
```

**Example**:
```bash
node updateOrphanedToCorrectSellable.cjs 86512012 FM13953 191469738
```

**Use case**: When you know exactly which sellable a remote_sku should point to.

### 4. relinkChannelSellable.cjs

**Purpose**: Manually relink a single channel sellable

**Usage**:
```bash
node relinkChannelSellable.cjs <product-id> <channel-product-id> <channel-sellable-id> <new-sku>
```

**Use case**: For precise manual fixes of individual channel sellables.

### 5. findVeeqoProduct.cjs

**Purpose**: Search for products by SKU

**Usage**:
```bash
node findVeeqoProduct.cjs <sku>
```

**Example**:
```bash
node findVeeqoProduct.cjs R13953
```

### 6. getVeeqoProductDetails.cjs

**Purpose**: Get detailed product information including all channel products and sellables

**Usage**:
```bash
node getVeeqoProductDetails.cjs <product-id>
```

**Example**:
```bash
node getVeeqoProductDetails.cjs 86512012
```

**Use case**: Verify product state, check channel sellables, debug issues.

### 7. mergeVeeqoSkus.cjs

**Purpose**: Merge entire products (not just variants)

**Usage**:
```bash
node mergeVeeqoSkus.cjs <old-sku> <primary-sku> --delete
```

**Use case**: When you need to merge products that don't share the same parent product.

## Common Workflows

### Workflow 1: Standard Variant Merge

```bash
# Merge FM13953 into R13953 and delete the old variant
node robustMergeVeeqoVariants.cjs FM13953 R13953 --delete
```

This single command handles everything:
- Moves all channel sellables
- Fixes any mismatches
- Deletes the old variant

### Workflow 2: Merge Without Deletion

```bash
# Merge but keep the old variant
node robustMergeVeeqoVariants.cjs FM13953 R13953
```

Use this when you want to verify everything is correct before deleting.

### Workflow 3: Fix Mismatches After Manual Changes

```bash
# Check and fix a specific product
node fixMismatchedChannelSellables.cjs 86512012
```

Use this after manual changes or if you suspect there are orphaned channel sellables.

### Workflow 4: Verify Results

```bash
# Check the new product
node getVeeqoProductDetails.cjs 86512012

# Verify old product is gone
node findVeeqoProduct.cjs FM13953
```

## Veeqo API Concepts

### Products
Main product entities in Veeqo (e.g., "JT Spectra Thermal Lens")

### Sellables (Variants)
SKU-level entities within products (e.g., R13953, R13952)

### Channel Products
Listings on sales channels (Amazon, eBay, Shopify, etc.)

### Channel Sellables
The link between channel products and sellables
- Contains `remote_sku` (the SKU on the sales channel)
- Contains `sellable_id` (points to the actual Veeqo sellable)

### Key Insight
When a channel sellable's `sellable_id` is updated, the channel product automatically moves to the new product in Veeqo's data structure.

## Troubleshooting

### Issue: Channel sellables still show old sellable IDs

**Cause**: Veeqo API caching

**Solution**: The updates were successful (204 responses), but GET requests return cached data. Wait a few minutes or check in the Veeqo UI.

### Issue: "Could not find sellable for SKU"

**Cause**: The SKU was already deleted/merged

**Solution**: Use `updateOrphanedToCorrectSellable.cjs` to manually specify the correct sellable ID.

### Issue: Listings linked to wrong variant

**Cause**: Previous merge operations or manual errors

**Solution**: Run `fixMismatchedChannelSellables.cjs` on the affected product.

## Best Practices

1. **Always use robustMergeVeeqoVariants.cjs** for merges - it's the most comprehensive
2. **Verify before deleting** - run without --delete first to check results
3. **Check for mismatches** - run fixMismatchedChannelSellables.cjs after complex operations
4. **Use getVeeqoProductDetails.cjs** to verify final state
5. **Keep old SKUs in remote_sku** - don't update remote_sku unless necessary, as it helps track the original listing

## Environment Setup

Required environment variable in `.env`:
```
VEEQO_API_KEY=your_api_key_here
```

All scripts use CommonJS (`.cjs` extension) to work in ES module projects.

