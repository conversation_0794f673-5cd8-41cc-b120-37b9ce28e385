// createAmazonFbaFeePreviewTable.js - Create the it_amaz_fba_fee_preview table

import fs from 'fs';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('[createAmazonFbaFeePreviewTable] Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createTable() {
  try {
    console.log('[createAmazonFbaFeePreviewTable] Reading SQL file...');
    
    // Read the SQL file
    const sqlContent = fs.readFileSync('./sql/create_it_amaz_fba_fee_preview.sql', 'utf-8');
    
    console.log('[createAmazonFbaFeePreviewTable] Executing SQL to create table...');
    
    // Execute the SQL using the exec_sql RPC function
    const { data, error } = await supabase.rpc('exec_sql', {
      sql_query: sqlContent
    });

    if (error) {
      throw new Error(`Failed to create table: ${error.message}`);
    }

    console.log('[createAmazonFbaFeePreviewTable] Table it_amaz_fba_fee_preview created successfully!');
    console.log('[createAmazonFbaFeePreviewTable] Indexes and triggers have been set up.');
    
    return { success: true };

  } catch (error) {
    console.error(`[createAmazonFbaFeePreviewTable] Error: ${error.message}`);
    throw error;
  }
}

// Run the function
createTable()
  .then(() => {
    console.log('[createAmazonFbaFeePreviewTable] Done!');
    process.exit(0);
  })
  .catch(error => {
    console.error('[createAmazonFbaFeePreviewTable] Failed:', error.message);
    process.exit(1);
  });

