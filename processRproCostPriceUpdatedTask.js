// processRproCostPriceUpdatedTask.js
// Task handler for rpro_cost_price_updated task type
// When RPRO cost price is updated, enqueue update_informed_pricing tasks for all affected listings

import dotenv from 'dotenv';
dotenv.config();

/**
 * Process an rpro_cost_price_updated task
 * @param {Object} task - The task object from t_task_queue
 * @param {Object} context - Context object with supabase, updateTaskStatus, logError
 */
async function processRproCostPriceUpdatedTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processRproCostPriceUpdatedTask] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      if (typeof task.payload === 'object' && task.payload !== null) {
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload type: ${typeof task.payload}`);
      }
    } catch (parseError) {
      console.error(`[processRproCostPriceUpdatedTask] Error parsing payload for task ${task.id}:`, parseError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to parse payload: ${parseError.message}`,
        error: parseError.message
      });
      return;
    }

    // Extract ivno from payload
    const { ivno } = payload;

    if (!ivno) {
      console.error(`[processRproCostPriceUpdatedTask] Task ${task.id} missing ivno in payload`);
      await updateTaskStatus(task.id, 'error', {
        message: 'Missing ivno in payload',
        error: 'ivno is required'
      });
      return;
    }

    console.log(`[processRproCostPriceUpdatedTask] Task ${task.id} processing ivno: ${ivno}`);

    // Update task status to processing
    await updateTaskStatus(task.id, 'processing');

    // Find all FM listings that have been uploaded to Amazon
    // We only care about listings that:
    // 1. Have listing_sku starting with "FM"
    // 2. Have been uploaded to Amazon (uploaded_to_amazon_at is not null in t_amaz_acc_listings)
    console.log(`[processRproCostPriceUpdatedTask] Looking up FM listings with rpro_ivno = ${ivno} that are uploaded to Amazon`);

    const { data: affectedListings, error: listingsError } = await supabase
      .from('v_rpro_informed_pricing_calculations')
      .select('listing_sku, rpro_ivno')
      .eq('rpro_ivno', ivno)
      .not('listing_sku', 'is', null)
      .like('listing_sku', 'FM%');

    if (listingsError) {
      console.error(`[processRproCostPriceUpdatedTask] Error looking up affected listings:`, listingsError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to look up affected listings: ${listingsError.message}`,
        error: listingsError.message
      });
      return;
    }

    if (!affectedListings || affectedListings.length === 0) {
      console.log(`[processRproCostPriceUpdatedTask] No FM listings found for rpro_ivno ${ivno}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `No FM listings found for rpro_ivno ${ivno}`,
        ivno,
        listings_found: 0,
        tasks_enqueued: 0
      });
      return;
    }

    console.log(`[processRproCostPriceUpdatedTask] Found ${affectedListings.length} FM listings for rpro_ivno ${ivno}`);

    // Now filter by checking which ones have been uploaded to Amazon
    console.log(`[processRproCostPriceUpdatedTask] Checking which listings have been uploaded to Amazon...`);

    const listingSkus = affectedListings.map(l => l.listing_sku);

    const { data: uploadedListings, error: uploadedError } = await supabase
      .from('t_amaz_acc_listings')
      .select('listing_sku')
      .in('listing_sku', listingSkus)
      .not('uploaded_to_amazon_at', 'is', null);

    if (uploadedError) {
      console.error(`[processRproCostPriceUpdatedTask] Error checking uploaded listings:`, uploadedError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to check uploaded listings: ${uploadedError.message}`,
        error: uploadedError.message
      });
      return;
    }

    if (!uploadedListings || uploadedListings.length === 0) {
      console.log(`[processRproCostPriceUpdatedTask] No FM listings have been uploaded to Amazon yet for rpro_ivno ${ivno}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `No FM listings uploaded to Amazon yet for rpro_ivno ${ivno}`,
        ivno,
        fm_listings_found: affectedListings.length,
        uploaded_listings_found: 0,
        tasks_enqueued: 0
      });
      return;
    }

    // Filter to only include listings that have been uploaded
    const uploadedListingSkus = new Set(uploadedListings.map(l => l.listing_sku));
    const listingsToUpdate = affectedListings.filter(l => uploadedListingSkus.has(l.listing_sku));

    console.log(`[processRproCostPriceUpdatedTask] Found ${listingsToUpdate.length} FM listings uploaded to Amazon (out of ${affectedListings.length} total FM listings)`);

    // Enqueue update_informed_pricing task for each uploaded listing
    const tasksToEnqueue = [];
    const now = new Date();
    // Schedule immediately - rate limiting is handled by the processUpdateInformedPricingTask handler

    for (const listing of listingsToUpdate) {
      console.log(`[processRproCostPriceUpdatedTask] Enqueueing update_informed_pricing for listing_sku: ${listing.listing_sku}`);

      tasksToEnqueue.push({
        task_type: 'update_informed_pricing',
        payload: {
          listing_sku: listing.listing_sku
        },
        status: 'pending',
        scheduled_at: now.toISOString(),
        created_at: now.toISOString(),
        enqueued_by: `rpro_cost_price_updated:${task.id}`
      });
    }

    // Batch insert all tasks
    console.log(`[processRproCostPriceUpdatedTask] Inserting ${tasksToEnqueue.length} tasks into t_task_queue...`);
    
    const { data: enqueuedTasks, error: enqueueError } = await supabase
      .from('t_task_queue')
      .insert(tasksToEnqueue)
      .select('id');

    if (enqueueError) {
      console.error(`[processRproCostPriceUpdatedTask] Error enqueueing tasks:`, enqueueError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to enqueue tasks: ${enqueueError.message}`,
        error: enqueueError.message
      });
      return;
    }

    console.log(`[processRproCostPriceUpdatedTask] Successfully enqueued ${enqueuedTasks.length} tasks`);

    // Mark task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully enqueued ${enqueuedTasks.length} update_informed_pricing tasks for rpro_ivno ${ivno} (FM listings uploaded to Amazon)`,
      ivno,
      fm_listings_found: affectedListings.length,
      uploaded_listings_found: listingsToUpdate.length,
      tasks_enqueued: enqueuedTasks.length,
      note: 'Rate limiting (15 min between API calls) is handled by processUpdateInformedPricingTask',
      enqueued_task_ids: enqueuedTasks.map(t => t.id)
    });

  } catch (error) {
    console.error(`[processRproCostPriceUpdatedTask] Unexpected error processing task ${task.id}:`, error);
    await logError(task.id, error.message);
    await updateTaskStatus(task.id, 'error', {
      message: `Unexpected error: ${error.message}`,
      error: error.message
    });
  }
}

export default processRproCostPriceUpdatedTask;

