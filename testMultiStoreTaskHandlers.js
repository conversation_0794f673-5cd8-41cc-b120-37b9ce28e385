// testMultiStoreTaskHandlers.js
// Test that the task handlers can process tasks for both stores

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testMultiStoreHandlers() {
  console.log('🧪 Testing Multi-Store Task Handlers\n');
  console.log('='.repeat(60));

  try {
    // Check if there are any products with shopify_store_id = 2
    console.log('\n📊 Checking for Tippmann Parts products...');
    const { data: tippmannProducts, error: tippmannError } = await supabase
      .from('t_products')
      .select('id, name, shopify_handle, shopify_store_id, shopify_uploaded_at')
      .eq('shopify_store_id', 2)
      .not('shopify_uploaded_at', 'is', null)
      .limit(5);

    if (tippmannError) {
      console.error('❌ Error fetching Tippmann products:', tippmannError);
    } else if (!tippmannProducts || tippmannProducts.length === 0) {
      console.log('⚠️  No Tippmann Parts products found with shopify_uploaded_at set');
      console.log('   You need products with shopify_store_id=2 and shopify_uploaded_at not null to test');
    } else {
      console.log(`✅ Found ${tippmannProducts.length} Tippmann Parts products uploaded to Shopify`);
      console.log('\nSample products:');
      tippmannProducts.forEach(p => {
        console.log(`  - ID: ${p.id}, Handle: ${p.shopify_handle}, Name: ${p.name}`);
      });
    }

    // Check if there are any products with shopify_store_id = 1
    console.log('\n📊 Checking for DZ Discs products...');
    const { data: dzProducts, error: dzError } = await supabase
      .from('t_products')
      .select('id, name, shopify_handle, shopify_store_id, shopify_uploaded_at')
      .eq('shopify_store_id', 1)
      .not('shopify_uploaded_at', 'is', null)
      .limit(5);

    if (dzError) {
      console.error('❌ Error fetching DZ products:', dzError);
    } else if (!dzProducts || dzProducts.length === 0) {
      console.log('⚠️  No DZ Discs products found with shopify_uploaded_at set');
    } else {
      console.log(`✅ Found ${dzProducts.length} DZ Discs products uploaded to Shopify`);
      console.log('\nSample products:');
      dzProducts.forEach(p => {
        console.log(`  - ID: ${p.id}, Handle: ${p.shopify_handle}, Name: ${p.name}`);
      });
    }

    // Check recent tasks in the queue
    console.log('\n📋 Checking recent Shopify update tasks...');
    const { data: recentTasks, error: tasksError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, payload, status, created_at')
      .in('task_type', ['update_shopify_product_title', 'update_shopify_product_description'])
      .order('created_at', { ascending: false })
      .limit(10);

    if (tasksError) {
      console.error('❌ Error fetching tasks:', tasksError);
    } else if (!recentTasks || recentTasks.length === 0) {
      console.log('ℹ️  No recent Shopify update tasks found');
    } else {
      console.log(`✅ Found ${recentTasks.length} recent Shopify update tasks`);
      console.log('\nRecent tasks:');
      recentTasks.forEach(t => {
        const payload = typeof t.payload === 'string' ? JSON.parse(t.payload) : t.payload;
        const storeId = payload.shopify_store_id || 'MISSING';
        const storeName = storeId === 1 ? 'DZ Discs' : storeId === 2 ? 'Tippmann Parts' : 'Unknown';
        console.log(`  - Task ${t.id}: ${t.task_type}`);
        console.log(`    Store: ${storeName} (ID: ${storeId})`);
        console.log(`    Handle: ${payload.shopify_handle}`);
        console.log(`    Status: ${t.status}`);
        console.log(`    Created: ${t.created_at}`);
        console.log('');
      });
    }

    console.log('='.repeat(60));
    console.log('\n✨ Test complete!');
    console.log('\n📝 To test the full workflow:');
    console.log('1. Update a product name or description in t_products');
    console.log('2. Make sure the product has shopify_uploaded_at set');
    console.log('3. Check that a task is enqueued with the correct shopify_store_id');
    console.log('4. Watch the worker process the task and update the correct store');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error(error);
  }
}

testMultiStoreHandlers();

