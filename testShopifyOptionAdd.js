import fetch from 'node-fetch';

const shopifyGraphqlEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/graphql.json';
const shopifyAccessToken = '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';
const productsEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/products/';
const variantsEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/variants/';

async function shopifyGraphQLRequest(query, variables = {}) {
  const response = await fetch(shopifyGraphqlEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify({ query, variables })
  });
  if (!response.ok) {
    const t = await response.text();
    throw new Error(`Shopify GraphQL ${response.status} ${response.statusText}: ${t}`);
  }
  const result = await response.json();
  if (result.errors) {
    throw new Error(`Shopify GraphQL errors: ${JSON.stringify(result.errors)}`);
  }
  return result.data;
}

async function getProduct(productId) {
  const resp = await fetch(`${productsEndpoint}${productId}.json`, {
    headers: { 'X-Shopify-Access-Token': shopifyAccessToken }
  });
  if (!resp.ok) {
    const t = await resp.text();
    throw new Error(`GET product ${productId} failed: ${resp.status} ${resp.statusText} - ${t}`);
  }
  const j = await resp.json();
  return j.product;
}

async function getProductVariants(productId) {
  const resp = await fetch(`${productsEndpoint}${productId}/variants.json`, {
    headers: { 'X-Shopify-Access-Token': shopifyAccessToken }
  });
  if (!resp.ok) {
    const t = await resp.text();
    throw new Error(`GET product variants ${productId} failed: ${resp.status} ${resp.statusText} - ${t}`);
  }
  const j = await resp.json();
  return j.variants || [];
}

async function updateProduct(productId, productPayload) {
  const payload = { product: { id: productId, ...productPayload } };
  console.log('Sending product update:', JSON.stringify(payload, null, 2));
  const resp = await fetch(`${productsEndpoint}${productId}.json`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify(payload)
  });
  if (!resp.ok) {
    const t = await resp.text();
    throw new Error(`PUT product ${productId} failed: ${resp.status} ${resp.statusText} - ${t}`);
  }
  const j = await resp.json();
  return j.product;
}

async function updateVariant(variantId, variantPayload) {
  const payload = { variant: { id: variantId, ...variantPayload } };
  console.log(`Sending variant ${variantId} update:`, JSON.stringify(payload, null, 2));
  const resp = await fetch(`${variantsEndpoint}${variantId}.json`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify(payload)
  });
  if (!resp.ok) {
    const t = await resp.text();
    throw new Error(`PUT variant ${variantId} failed: ${resp.status} ${resp.statusText} - ${t}`);
  }
  const j = await resp.json();
  return j.variant;
}

async function test() {
  try {
    const productId = '7549999841468';
    const variantId = '41846650306748';
    const newOptionName = 'Blend';
    const newOptionValue = 'Original';

    console.log('\n=== Step 1: Get current product ===');
    const product = await getProduct(productId);
    console.log('Current options:', product.options);
    console.log('Current variants count:', product.variants.length);

    console.log('\n=== Step 2: Get all variants ===');
    const allVariants = await getProductVariants(productId);
    console.log(`Found ${allVariants.length} variants`);
    allVariants.forEach(v => {
      console.log(`  Variant ${v.id}: option1=${v.option1}, option2=${v.option2}, option3=${v.option3}`);
    });

    console.log('\n=== Step 3: Try adding option with variant values in product update ===');
    const newOptions = product.options.map(opt => ({ id: opt.id, name: opt.name }));
    newOptions.push({ name: newOptionName });
    
    // Build variant updates with the new option value
    const variantUpdatesForProduct = allVariants.map(v => {
      const update = { id: v.id };
      if (v.id === variantId) {
        update.option3 = newOptionValue;
      } else {
        update.option3 = 'Default';
      }
      return update;
    });

    console.log('Variant updates to include:', JSON.stringify(variantUpdatesForProduct, null, 2));

    const productUpdate = {
      options: newOptions,
      variants: variantUpdatesForProduct
    };

    const updatedProduct = await updateProduct(productId, productUpdate);
    console.log('Product updated successfully!');
    console.log('New options:', updatedProduct.options);

  } catch (err) {
    console.error('Error:', err.message);
  }
}

test();

