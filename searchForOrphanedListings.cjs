const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔍 SEARCH FOR ORPHANED LISTINGS');
console.log('='.repeat(70));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // 204 No Content is a success response with no body
    if (response.status === 204 || method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to search for orphaned listings with a specific remote_sku
async function searchForOrphanedListings(remoteSku) {
  console.log(`\n🔍 Searching for channel sellables with remote_sku: ${remoteSku}`);
  console.log('─'.repeat(70));

  const foundListings = [];
  let page = 1;
  const pageSize = 200; // Increased page size
  let totalChannelProducts = 0;

  // Search through channel_products endpoint directly
  console.log(`\n   Using channel_products endpoint for efficient search...`);

  while (true) {
    process.stdout.write(`\r   Scanning page ${page}... (${totalChannelProducts} channel products scanned)`);

    const result = await makeVeeqoRequest(`https://api.veeqo.com/channel_products?page=${page}&page_size=${pageSize}`);

    if (!result.success) {
      console.log(`\n   ⚠️  Error fetching page ${page}: ${result.error}`);
      break;
    }

    const channelProducts = result.data;

    if (!channelProducts || channelProducts.length === 0) {
      break;
    }

    totalChannelProducts += channelProducts.length;

    // Check each channel product for channel sellables with the remote_sku
    for (const channelProduct of channelProducts) {
      if (channelProduct.channel_sellables) {
        for (const channelSellable of channelProduct.channel_sellables) {
          if (channelSellable.remote_sku === remoteSku) {
            // Get product details to check if sellable exists
            let productId = channelProduct.product_id;
            let productTitle = 'Unknown';
            let sellableExists = false;
            let sellableSku = null;

            if (productId) {
              const productResult = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
              if (productResult.success) {
                const product = productResult.data;
                productTitle = product.title;
                const sellableInProduct = product.sellables?.find(s => s.id === channelSellable.sellable_id);
                sellableExists = !!sellableInProduct;
                sellableSku = sellableInProduct ? sellableInProduct.sku_code : null;
              }
            }

            foundListings.push({
              productId: productId,
              productTitle: productTitle,
              channelProductId: channelProduct.id,
              channelProductTitle: channelProduct.remote_title,
              channelName: channelProduct.channel?.name || 'Unknown',
              channelStatus: channelProduct.status || 'Unknown',
              channelSellableId: channelSellable.id,
              remoteSku: channelSellable.remote_sku,
              sellableId: channelSellable.sellable_id,
              sellableExists: sellableExists,
              sellableSku: sellableSku,
              isOrphaned: !sellableExists,
              isMismatched: sellableExists && sellableSku !== remoteSku,
              isUnlinked: !productId
            });
          }
        }
      }
    }

    if (channelProducts.length < pageSize) {
      break;
    }

    page++;
  }

  console.log(`\n   ✅ Scan complete: ${totalChannelProducts} channel products scanned`);
  console.log(`\n${'='.repeat(70)}`);
  console.log(`📊 RESULTS: Found ${foundListings.length} channel sellable(s) with remote_sku=${remoteSku}`);
  console.log(`${'='.repeat(70)}`);
  
  if (foundListings.length === 0) {
    console.log(`\n   No channel sellables found with remote_sku=${remoteSku}`);
    return foundListings;
  }
  
  // Group by status
  const unlinked = foundListings.filter(l => l.isUnlinked);
  const orphaned = foundListings.filter(l => !l.isUnlinked && l.isOrphaned);
  const mismatched = foundListings.filter(l => !l.isUnlinked && !l.isOrphaned && l.isMismatched);
  const correct = foundListings.filter(l => !l.isUnlinked && !l.isOrphaned && !l.isMismatched);

  console.log(`\n📈 Summary:`);
  console.log(`   Total: ${foundListings.length}`);
  console.log(`   Unlinked (no product): ${unlinked.length}`);
  console.log(`   Orphaned (sellable doesn't exist): ${orphaned.length}`);
  console.log(`   Mismatched (SKU doesn't match): ${mismatched.length}`);
  console.log(`   Correct: ${correct.length}`);
  
  // Display unlinked listings
  if (unlinked.length > 0) {
    console.log(`\n🔵 UNLINKED LISTINGS (no product):`);
    console.log('─'.repeat(70));

    for (const listing of unlinked) {
      console.log(`\n   Channel Product ID: ${listing.channelProductId}`);
      console.log(`   Channel Product Title: ${listing.channelProductTitle}`);
      console.log(`   Channel: ${listing.channelName}`);
      console.log(`   Status: ${listing.channelStatus}`);
      console.log(`   Channel Sellable ID: ${listing.channelSellableId}`);
      console.log(`   Remote SKU: ${listing.remoteSku}`);
      console.log(`   Sellable ID: ${listing.sellableId}`);
      console.log(`   ⚠️  UNLINKED - No product associated`);
    }
  }

  // Display orphaned listings
  if (orphaned.length > 0) {
    console.log(`\n🔴 ORPHANED LISTINGS (sellable doesn't exist):`);
    console.log('─'.repeat(70));

    for (const listing of orphaned) {
      console.log(`\n   Product ID: ${listing.productId}`);
      console.log(`   Product Title: ${listing.productTitle}`);
      console.log(`   Channel Product ID: ${listing.channelProductId}`);
      console.log(`   Channel Product Title: ${listing.channelProductTitle}`);
      console.log(`   Channel: ${listing.channelName}`);
      console.log(`   Status: ${listing.channelStatus}`);
      console.log(`   Channel Sellable ID: ${listing.channelSellableId}`);
      console.log(`   Remote SKU: ${listing.remoteSku}`);
      console.log(`   Sellable ID: ${listing.sellableId} ❌ (DOESN'T EXIST)`);
    }
  }
  
  // Display mismatched listings
  if (mismatched.length > 0) {
    console.log(`\n⚠️  MISMATCHED LISTINGS (SKU doesn't match):`);
    console.log('─'.repeat(70));

    for (const listing of mismatched) {
      console.log(`\n   Product ID: ${listing.productId}`);
      console.log(`   Product Title: ${listing.productTitle}`);
      console.log(`   Channel Product ID: ${listing.channelProductId}`);
      console.log(`   Channel Product Title: ${listing.channelProductTitle}`);
      console.log(`   Channel: ${listing.channelName}`);
      console.log(`   Status: ${listing.channelStatus}`);
      console.log(`   Channel Sellable ID: ${listing.channelSellableId}`);
      console.log(`   Remote SKU: ${listing.remoteSku}`);
      console.log(`   Sellable ID: ${listing.sellableId} (SKU: ${listing.sellableSku}) ⚠️  MISMATCH`);
    }
  }
  
  // Display correct listings
  if (correct.length > 0) {
    console.log(`\n✅ CORRECT LISTINGS:`);
    console.log('─'.repeat(70));

    for (const listing of correct) {
      console.log(`\n   Product ID: ${listing.productId}`);
      console.log(`   Product Title: ${listing.productTitle}`);
      console.log(`   Channel Product ID: ${listing.channelProductId}`);
      console.log(`   Channel Product Title: ${listing.channelProductTitle}`);
      console.log(`   Channel: ${listing.channelName}`);
      console.log(`   Status: ${listing.channelStatus}`);
      console.log(`   Channel Sellable ID: ${listing.channelSellableId}`);
      console.log(`   Remote SKU: ${listing.remoteSku}`);
      console.log(`   Sellable ID: ${listing.sellableId} (SKU: ${listing.sellableSku}) ✅`);
    }
  }
  
  return foundListings;
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 1) {
  console.log(`
Usage: node searchForOrphanedListings.cjs <remote-sku>

Arguments:
  remote-sku - The remote SKU to search for (e.g., FM6872)

Example:
  node searchForOrphanedListings.cjs FM6872

This will search through ALL products in Veeqo and find any channel sellables
with the specified remote_sku. It will identify:
  - Orphaned listings (sellable doesn't exist)
  - Mismatched listings (remote_sku doesn't match actual sellable SKU)
  - Correct listings
`);
  process.exit(1);
}

const remoteSku = args[0];

searchForOrphanedListings(remoteSku)
  .then(foundListings => {
    console.log(`\n✅ Search complete!`);
    process.exit(0);
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

