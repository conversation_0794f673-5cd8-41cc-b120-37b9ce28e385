const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔍 FIND EXACT CHANNEL SELLABLE BY REMOTE SKU');
console.log('='.repeat(70));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // 204 No Content is a success response with no body
    if (response.status === 204 || method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to search for exact remote_sku match
async function findExactChannelSellable(remoteSku) {
  console.log(`\n🔍 Searching for exact remote_sku: ${remoteSku}`);
  console.log('─'.repeat(70));
  
  const foundMatches = [];
  let page = 1;
  const pageSize = 200;
  let totalScanned = 0;
  
  while (page <= 200) { // Limit to reasonable number
    process.stdout.write(`\r   Scanning page ${page}... (${totalScanned} channel products scanned, ${foundMatches.length} matches found)`);
    
    const result = await makeVeeqoRequest(`https://api.veeqo.com/channel_products?page=${page}&page_size=${pageSize}`);
    
    if (!result.success) {
      if (result.status === 504) {
        console.log(`\n   ⚠️  Timeout on page ${page}, continuing...`);
        page++;
        continue;
      }
      console.log(`\n   ⚠️  Error fetching page ${page}: ${result.error}`);
      break;
    }
    
    let channelProducts = result.data;
    
    // Handle pagination response format
    if (!Array.isArray(channelProducts)) {
      if (channelProducts && channelProducts.items) {
        channelProducts = channelProducts.items;
      } else {
        break;
      }
    }
    
    if (!channelProducts || channelProducts.length === 0) {
      break;
    }
    
    totalScanned += channelProducts.length;
    
    // Check each channel product for exact remote_sku match
    for (const cp of channelProducts) {
      if (cp.channel_sellables) {
        for (const cs of cp.channel_sellables) {
          if (cs.remote_sku === remoteSku) {
            foundMatches.push({
              channelProduct: cp,
              channelSellable: cs
            });
          }
        }
      }
    }
    
    if (channelProducts.length < pageSize) {
      break;
    }
    
    page++;
    
    // Add small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`\n   ✅ Scan complete: ${totalScanned} channel products scanned`);
  console.log(`\n${'='.repeat(70)}`);
  console.log(`📊 RESULTS: Found ${foundMatches.length} exact match(es) for remote_sku=${remoteSku}`);
  console.log(`${'='.repeat(70)}`);
  
  if (foundMatches.length === 0) {
    console.log(`\n   No exact matches found for remote_sku=${remoteSku}`);
    return [];
  }
  
  // Display each match
  for (const match of foundMatches) {
    const cp = match.channelProduct;
    const cs = match.channelSellable;
    
    console.log(`\n${'─'.repeat(70)}`);
    console.log(`📺 Channel Product ID: ${cp.id}`);
    console.log(`   Title: ${cp.remote_title || 'N/A'}`);
    console.log(`   Remote ID: ${cp.remote_id || 'N/A'}`);
    console.log(`   Channel: ${cp.channel?.name || 'Unknown'}`);
    console.log(`   Status: ${cp.status || 'Unknown'}`);
    console.log(`   Product ID: ${cp.product_id || 'UNLINKED ⚠️'}`);
    
    console.log(`\n   📋 Channel Sellable ID: ${cs.id}`);
    console.log(`      Remote SKU: ${cs.remote_sku}`);
    console.log(`      Sellable ID: ${cs.sellable_id || 'N/A'}`);
    
    // If there's a product, check if the sellable exists
    if (cp.product_id) {
      console.log(`\n   🔍 Checking product ${cp.product_id}...`);
      const productResult = await makeVeeqoRequest(`https://api.veeqo.com/products/${cp.product_id}`);
      if (productResult.success) {
        const product = productResult.data;
        console.log(`      Product Title: ${product.title}`);
        
        if (cs.sellable_id) {
          const sellable = product.sellables?.find(s => s.id === cs.sellable_id);
          if (sellable) {
            console.log(`      Sellable SKU: ${sellable.sku_code}`);
            if (sellable.sku_code === cs.remote_sku) {
              console.log(`      Status: ✅ CORRECT MATCH`);
            } else {
              console.log(`      Status: ⚠️  MISMATCH (remote_sku=${cs.remote_sku}, sellable_sku=${sellable.sku_code})`);
            }
          } else {
            console.log(`      Status: ❌ ORPHANED (sellable ${cs.sellable_id} doesn't exist in product)`);
          }
        } else {
          console.log(`      Status: ⚠️  No sellable_id`);
        }
      } else {
        console.log(`      ⚠️  Could not fetch product: ${productResult.error}`);
      }
    } else {
      console.log(`\n   Status: 🔵 UNLINKED (no product associated)`);
    }
  }
  
  return foundMatches;
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 1) {
  console.log(`
Usage: node findExactChannelSellable.cjs <remote-sku>

Arguments:
  remote-sku - The exact remote SKU to search for (e.g., FM6872)

Example:
  node findExactChannelSellable.cjs FM6872

This will scan through all channel products and find exact matches for the remote_sku.
`);
  process.exit(1);
}

const remoteSku = args[0];

findExactChannelSellable(remoteSku)
  .then(matches => {
    console.log(`\n${'='.repeat(70)}`);
    console.log(`✅ Search complete! Found ${matches.length} exact match(es)`);
    console.log(`${'='.repeat(70)}`);
    process.exit(0);
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

