-- Add bump_forward_days field to gt_todo table
-- This field controls how many days to bump a past-due task forward when refreshing

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'gt_todo' 
        AND column_name = 'bump_forward_days'
    ) THEN
        ALTER TABLE public.gt_todo 
        ADD COLUMN bump_forward_days NUMERIC(10,3);
        
        -- Add a comment to the column
        COMMENT ON COLUMN public.gt_todo.bump_forward_days IS 'Number of days to bump scheduled_at forward when task is past due. Default is 0.042 (1 hour) if null.';
        
        RAISE NOTICE 'Added bump_forward_days column to gt_todo table';
    ELSE
        RAISE NOTICE 'bump_forward_days column already exists in gt_todo table';
    END IF;
END $$;

