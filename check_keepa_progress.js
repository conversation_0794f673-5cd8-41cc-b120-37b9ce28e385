import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkProgress() {
  console.log('Checking Keepa update progress...\n');

  // First, check the most recent update regardless of time
  const { data: mostRecent, error: recentError } = await supabase
    .from('t_sdasins')
    .select('id, asin, so_rank_30day_avg, so_rank_30day_avg_date')
    .not('so_rank_30day_avg_date', 'is', null)
    .order('so_rank_30day_avg_date', { ascending: false })
    .limit(5);

  if (recentError) {
    console.error('Error querying database:', recentError);
    return;
  }

  console.log('Most recent rank updates (any time):');
  if (mostRecent && mostRecent.length > 0) {
    mostRecent.forEach((record, index) => {
      const updateDate = new Date(record.so_rank_30day_avg_date);
      const minutesAgo = Math.floor((Date.now() - updateDate.getTime()) / 60000);
      console.log(`${index + 1}. ASIN: ${record.asin}, Rank: ${record.so_rank_30day_avg}, Updated: ${minutesAgo} minutes ago (${record.so_rank_30day_avg_date})`);
    });
  } else {
    console.log('No records with rank updates found.');
  }

  // Check how many records were updated in the last 15 minutes
  const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000).toISOString();

  const { data: recentUpdates, error } = await supabase
    .from('t_sdasins')
    .select('id, asin, so_rank_30day_avg, so_rank_30day_avg_date')
    .gte('so_rank_30day_avg_date', fifteenMinutesAgo)
    .order('so_rank_30day_avg_date', { ascending: false })
    .limit(10);

  if (error) {
    console.error('Error querying database:', error);
    return;
  }

  console.log(`\nRecords updated in the last 15 minutes: ${recentUpdates?.length || 0}`);

  if (recentUpdates && recentUpdates.length > 0) {
    console.log('\nRecent updates:');
    recentUpdates.forEach((record, index) => {
      console.log(`${index + 1}. ASIN: ${record.asin}, Rank: ${record.so_rank_30day_avg}, Updated: ${record.so_rank_30day_avg_date}`);
    });

    // Get total count of records updated today
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    const { count, error: countError } = await supabase
      .from('t_sdasins')
      .select('id', { count: 'exact', head: true })
      .gte('so_rank_30day_avg_date', todayStart.toISOString());

    if (!countError) {
      console.log(`\nTotal records updated today: ${count}`);
    }

    console.log('\n✅ The Keepa update process appears to be running and making progress.');
  } else {
    console.log('\n⚠️  No recent updates found in the last 15 minutes.');
    console.log('The process may be stuck, not running, or still fetching data from Keepa API.');
  }
}

checkProgress().then(() => process.exit(0));

