const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔧 FIX ORPHANED CHANNEL SELLABLES');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // 204 No Content is a success response with no body
    if (response.status === 204 || method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to get product details
async function getProductDetails(productId) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!result.success) {
    console.error(`❌ Error fetching product: ${result.error}`);
    return null;
  }
  
  return result.data;
}

// Function to update channel sellable
async function updateChannelSellable(channelSellableId, newSellableId) {
  const data = {
    channel_sellable: {
      sellable_id: newSellableId
    }
  };
  
  const result = await makeVeeqoRequest(
    `https://api.veeqo.com/channel_sellables/${channelSellableId}`,
    'PUT',
    data
  );
  
  return result;
}

// Main function
async function fixOrphanedChannelSellables(productId, correctSellableId) {
  console.log(`\n🔧 Fixing orphaned channel sellables in product ${productId}`);
  console.log(`   Will update all to point to sellable ${correctSellableId}`);
  console.log('─'.repeat(50));
  
  // Get product details
  console.log(`\n📦 Fetching product ${productId}...`);
  const product = await getProductDetails(productId);
  
  if (!product) {
    return false;
  }
  
  console.log(`   Title: ${product.title}`);
  
  // Find all channel sellables that don't point to the correct sellable
  const channelSellablesToFix = [];
  
  if (product.channel_products) {
    for (const channelProduct of product.channel_products) {
      if (channelProduct.channel_sellables) {
        for (const channelSellable of channelProduct.channel_sellables) {
          if (channelSellable.sellable_id !== correctSellableId) {
            channelSellablesToFix.push({
              channelProduct,
              channelSellable
            });
          }
        }
      }
    }
  }
  
  console.log(`\n📊 Found ${channelSellablesToFix.length} channel sellable(s) to fix`);
  
  if (channelSellablesToFix.length === 0) {
    console.log(`✅ No orphaned channel sellables found`);
    return true;
  }
  
  // Update each channel sellable
  let successCount = 0;
  for (const item of channelSellablesToFix) {
    const { channelProduct, channelSellable } = item;
    
    console.log(`\n📺 Channel Product: ${channelProduct.id} - ${channelProduct.remote_title}`);
    console.log(`   Channel Sellable: ${channelSellable.id}`);
    console.log(`   Remote SKU: ${channelSellable.remote_sku}`);
    console.log(`   Current Sellable ID: ${channelSellable.sellable_id} → ${correctSellableId}`);
    
    const result = await updateChannelSellable(
      channelSellable.id,
      correctSellableId
    );
    
    if (result.success) {
      console.log(`   ✅ Updated successfully`);
      successCount++;
    } else {
      console.log(`   ❌ Failed: ${result.error}`);
    }
  }
  
  console.log(`\n📊 Summary: ${successCount} of ${channelSellablesToFix.length} channel sellables fixed`);
  
  return successCount === channelSellablesToFix.length;
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 2) {
  console.log(`
Usage: node fixOrphanedChannelSellables.cjs <product-id> <correct-sellable-id>

Example:
  node fixOrphanedChannelSellables.cjs 86511808 191469451

This will find all channel sellables in product 86511808 that don't point to
sellable 191469451 and update them to point to it.
`);
  process.exit(1);
}

const productId = args[0];
const correctSellableId = parseInt(args[1]);

fixOrphanedChannelSellables(productId, correctSellableId)
  .then(success => {
    if (success) {
      console.log(`\n✅ All done!`);
      process.exit(0);
    } else {
      console.log(`\n⚠️  Some updates failed`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

