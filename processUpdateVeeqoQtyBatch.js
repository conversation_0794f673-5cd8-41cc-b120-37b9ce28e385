/**
 * Batch processor for update_veeqo_*_qty tasks
 * 
 * This module batches all pending update_veeqo_sdasin_qty, update_veeqo_dgacc_qty,
 * update_veeqo_osl_qty, and update_veeqo_rpro_qty tasks and processes them together
 * to reduce the number of API calls to Veeqo.
 * 
 * Instead of making one API call per task, we group updates by SKU and make
 * a single API call per unique SKU.
 */

import axios from 'axios';
import getVeeqoId from './getVeeqoId.js';

const VEEQO_API_KEY = process.env.VEEQO_API_KEY;
const WAREHOUSE_ID = 99881;

// Rate limiting configuration
// Veeqo API: bucket size 100, leak rate 5 requests/second
const CONCURRENT_LIMIT = 3; // Process 3 concurrent requests
const DELAY_BETWEEN_BATCHES = 200; // 200ms between batches (5 requests/second)

/**
 * Update a single Veeqo product with retry logic and exponential backoff
 * @param {string} veeqoId - Veeqo product ID
 * @param {number} quantity - Quantity to set
 * @param {number} maxRetries - Maximum number of retries on 429 errors
 * @returns {Promise<{success: boolean, veeqoId: string, error?: string}>}
 */
async function updateVeeqoProductWithRetry(veeqoId, quantity, maxRetries = 3) {
  const url = `https://api.veeqo.com/sellables/${veeqoId}/warehouses/${WAREHOUSE_ID}/stock_entry`;

  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    try {
      console.log(`[processUpdateVeeqoQtyBatch] Updating Veeqo product ${veeqoId} to quantity ${quantity} (attempt ${attempt}/${maxRetries + 1})`);

      const response = await axios({
        method: 'PUT',
        url: url,
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': VEEQO_API_KEY
        },
        data: {
          stock_entry: {
            physical_stock_level: quantity,
            infinite: false
          }
        },
        timeout: 5000 // 5 second timeout
      });

      console.log(`[processUpdateVeeqoQtyBatch] ✅ Successfully updated Veeqo product ${veeqoId}`);
      return { success: true, veeqoId };
    } catch (error) {
      // Handle 429 rate limit errors with exponential backoff
      if (error.response?.status === 429 && attempt <= maxRetries) {
        const waitTime = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
        console.log(`[processUpdateVeeqoQtyBatch] ⏱️ Rate limited (429), waiting ${waitTime}ms before retry ${attempt}/${maxRetries}`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        continue;
      }

      // For other errors or final retry, return failure
      const errorMsg = error.response?.data?.message || error.message || 'Unknown error';
      console.error(`[processUpdateVeeqoQtyBatch] ❌ Error updating Veeqo product ${veeqoId}: ${errorMsg}`);
      return { success: false, veeqoId, error: errorMsg };
    }
  }
}

/**
 * Process a batch of update_veeqo_*_qty tasks
 * @param {Object} task - The first task that triggered this batch (used for logging)
 * @param {Object} context - Context object with supabase, updateTaskStatus, logError
 */
async function processUpdateVeeqoQtyBatch(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processUpdateVeeqoQtyBatch] Processing batch of update_veeqo_*_qty tasks, triggered by task ${task.id}`);

  try {
    const now = new Date();

    // Fetch all pending update_veeqo_*_qty tasks
    const taskTypes = ['update_veeqo_sdasin_qty', 'update_veeqo_dgacc_qty', 'update_veeqo_osl_qty', 'update_veeqo_rpro_qty'];
    
    console.log(`[processUpdateVeeqoQtyBatch] Fetching all pending ${taskTypes.join(', ')} tasks...`);

    const { data: pendingTasks, error: tasksError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, payload, status')
      .in('task_type', taskTypes)
      .in('status', ['pending', 'processing'])
      .lte('scheduled_at', now.toISOString())
      .order('id', { ascending: true });

    if (tasksError) {
      console.error(`[processUpdateVeeqoQtyBatch] Error fetching tasks:`, tasksError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to fetch tasks: ${tasksError.message}`,
        error: tasksError.message
      });
      return;
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log(`[processUpdateVeeqoQtyBatch] No tasks to process`);
      await updateTaskStatus(task.id, 'completed', {
        message: 'No tasks to process',
        tasks_processed: 0
      });
      return;
    }

    console.log(`[processUpdateVeeqoQtyBatch] Found ${pendingTasks.length} tasks to process`);

    // Mark all tasks as processing
    const taskIds = pendingTasks.map(t => t.id);
    const { error: updateError } = await supabase
      .from('t_task_queue')
      .update({
        status: 'processing',
        locked_at: now.toISOString(),
        locked_by: 'batch_update_veeqo_qty'
      })
      .in('id', taskIds)
      .eq('status', 'pending');

    if (updateError) {
      console.error(`[processUpdateVeeqoQtyBatch] Error marking tasks as processing:`, updateError);
    }

    // Step 1: Update t_product_variants.stock_quantity for all DGACC tasks FIRST
    console.log(`[processUpdateVeeqoQtyBatch] Step 1: Updating t_product_variants.stock_quantity for DGACC tasks...`);

    const dgaccLiveQtyMap = new Map(); // Map of accessory_id -> live on_hand_qty
    const dgaccTasks = pendingTasks.filter(t => t.task_type === 'update_veeqo_dgacc_qty');
    for (const dgaccTask of dgaccTasks) {
      try {
        let payload;
        if (typeof dgaccTask.payload === 'object' && dgaccTask.payload !== null) {
          payload = dgaccTask.payload;
        } else if (typeof dgaccTask.payload === 'string') {
          payload = JSON.parse(dgaccTask.payload);
        } else {
          continue;
        }

        const accessoryId = payload.id || payload.accessory_id;
        if (!accessoryId) continue;

        // Get the current on-hand quantity from the view
        const { data: liveQty, error: qtyError } = await supabase
          .from('v_dgacc_live_qty')
          .select('on_hand_qty')
          .eq('product_variant_id', accessoryId)
          .single();

        if (!qtyError && liveQty) {
          // Store in map for later use
          dgaccLiveQtyMap.set(accessoryId, liveQty.on_hand_qty);

          const { error: updateError } = await supabase
            .from('t_product_variants')
            .update({ stock_quantity: liveQty.on_hand_qty })
            .eq('id', accessoryId);

          if (!updateError) {
            console.log(`[processUpdateVeeqoQtyBatch] ✅ Updated stock_quantity for variant ${accessoryId} to ${liveQty.on_hand_qty}`);
          } else {
            console.warn(`[processUpdateVeeqoQtyBatch] Warning: Failed to update stock_quantity for variant ${accessoryId}: ${updateError.message}`);
          }
        } else {
          console.warn(`[processUpdateVeeqoQtyBatch] Warning: Could not fetch live qty for DGACC ${accessoryId}`);
        }
      } catch (err) {
        console.warn(`[processUpdateVeeqoQtyBatch] Warning: Error updating stock_quantity: ${err.message}`);
      }
    }

    // Step 1b: Fetch live quantities and upload status for SDASIN tasks
    console.log(`[processUpdateVeeqoQtyBatch] Step 1b: Fetching live quantities and upload status for SDASIN tasks...`);

    const sdasinLiveQtyMap = new Map(); // Map of sdasin_id -> live available_quantity
    const sdasinUploadStatusMap = new Map(); // Map of sdasin_id -> fbm_uploaded_at
    const sdasinTasks = pendingTasks.filter(t => t.task_type === 'update_veeqo_sdasin_qty');

    for (const sdasinTask of sdasinTasks) {
      try {
        let payload;
        if (typeof sdasinTask.payload === 'object' && sdasinTask.payload !== null) {
          payload = sdasinTask.payload;
        } else if (typeof sdasinTask.payload === 'string') {
          payload = JSON.parse(sdasinTask.payload);
        } else {
          continue;
        }

        const sdasinId = payload.id;
        if (!sdasinId) continue;

        // Fetch live quantity and upload status from t_inv_sdasin and t_sdasins
        const { data: invRow, error: invError } = await supabase
          .from('t_inv_sdasin')
          .select('available_quantity')
          .eq('id', sdasinId)
          .maybeSingle();

        const { data: sdasinRow, error: sdasinError } = await supabase
          .from('t_sdasins')
          .select('fbm_uploaded_at')
          .eq('id', sdasinId)
          .maybeSingle();

        if (!invError && invRow) {
          sdasinLiveQtyMap.set(sdasinId, invRow.available_quantity);
          console.log(`[processUpdateVeeqoQtyBatch] ✅ Fetched live qty for SDASIN ${sdasinId}: ${invRow.available_quantity}`);
        } else {
          console.warn(`[processUpdateVeeqoQtyBatch] Warning: Could not fetch live qty for SDASIN ${sdasinId}`);
        }

        if (!sdasinError && sdasinRow) {
          sdasinUploadStatusMap.set(sdasinId, sdasinRow.fbm_uploaded_at);
          console.log(`[processUpdateVeeqoQtyBatch] ✅ Fetched upload status for SDASIN ${sdasinId}: ${sdasinRow.fbm_uploaded_at ? 'uploaded' : 'not uploaded'}`);
        } else {
          console.warn(`[processUpdateVeeqoQtyBatch] Warning: Could not fetch upload status for SDASIN ${sdasinId}`);
        }
      } catch (err) {
        console.warn(`[processUpdateVeeqoQtyBatch] Warning: Error fetching SDASIN data: ${err.message}`);
      }
    }

    // Step 2: Group tasks by SKU for Veeqo updates (only for variants uploaded to Shopify)
    console.log(`[processUpdateVeeqoQtyBatch] Step 2: Preparing Veeqo updates for uploaded variants...`);

    const skuMap = new Map(); // Map of SKU -> { quantity, taskIds, taskType, recordId, recordType, uploadedToShopify }
    const taskPayloadMap = new Map(); // Map of taskId -> payload
    const skippedTasksCount = { notUploaded: 0, invalidPayload: 0 }; // Track why tasks were skipped

    for (const t of pendingTasks) {
      try {
        let payload;
        console.log(`[processUpdateVeeqoQtyBatch] Task ${t.id} (type: ${t.task_type}) - payload type: ${typeof t.payload}, raw: ${JSON.stringify(t.payload)}`);

        if (typeof t.payload === 'object' && t.payload !== null) {
          payload = t.payload;
        } else if (typeof t.payload === 'string') {
          payload = JSON.parse(t.payload);
        } else {
          console.error(`[processUpdateVeeqoQtyBatch] Task ${t.id} has invalid payload type`);
          await updateTaskStatus(t.id, 'error', {
            message: 'Invalid payload type',
            error: 'Expected object or JSON string'
          });
          skippedTasksCount.invalidPayload++;
          continue;
        }

        console.log(`[processUpdateVeeqoQtyBatch] Task ${t.id} parsed payload:`, JSON.stringify(payload));
        taskPayloadMap.set(t.id, { ...payload, taskType: t.task_type });

        // Determine SKU and record ID based on task type
        let sku;
        let recordId;
        let quantity; // Will hold the live quantity to use for Veeqo update

        if (t.task_type === 'update_veeqo_sdasin_qty') {
          sku = `Disc_${payload.id}`;
          recordId = payload.id;
          // Use live quantity from map (fetched in Step 1b)
          quantity = sdasinLiveQtyMap.get(recordId);
          if (quantity === undefined) {
            console.warn(`[processUpdateVeeqoQtyBatch] No live qty found for SDASIN ${recordId}, using payload qty`);
            quantity = payload.available_quantity;
          }
        } else if (t.task_type === 'update_veeqo_dgacc_qty') {
          sku = `DGACC${payload.id || payload.accessory_id}`;
          recordId = payload.id || payload.accessory_id;
          // Use live quantity from map (fetched and stored in Step 1)
          quantity = dgaccLiveQtyMap.get(recordId);
          if (quantity === undefined) {
            console.warn(`[processUpdateVeeqoQtyBatch] No live qty found for DGACC ${recordId}, skipping Veeqo update`);
            continue; // Skip this task if we don't have a quantity
          }
        } else if (t.task_type === 'update_veeqo_osl_qty') {
          // For OSL tasks, construct SKU as OS + OSL id (e.g., OS9725)
          sku = `OS${payload.id}`;
          recordId = payload.id;
          quantity = payload.available_quantity;
          console.log(`[processUpdateVeeqoQtyBatch] OSL task ${t.id}: payload.id=${payload.id}, sku=${sku}`);
        } else if (t.task_type === 'update_veeqo_rpro_qty') {
          // RPRO tasks can arrive with different payload shapes:
          // - From triggers: { ivno, rpro_change_id, is_now }
          // - From reconciliation script: { id: rpro_id, sku_code, target_qty, current_veeqo_qty }
          // Normalize to ivno and proper Veeqo SKU (e.g., R01033)
          const ivno = payload.ivno || payload.id;
          sku = payload.sku_code || (ivno ? `R${String(ivno).padStart(5, '0')}` : undefined);
          recordId = ivno;

          if (!ivno || !sku) {
            console.warn(`[processUpdateVeeqoQtyBatch] Missing ivno/id in RPRO payload for task ${t.id}`);
            await updateTaskStatus(t.id, 'error', {
              message: 'Invalid RPRO payload: missing ivno/id',
              payload: payload
            });
            continue;
          }

          // Determine target quantity with RPRO rules:
          // - Negative => 0
          // - Decimals => round down (floor)
          if (payload.target_qty !== undefined && payload.target_qty !== null) {
            const raw = Number(payload.target_qty);
            quantity = Number.isNaN(raw) ? undefined : Math.max(0, Math.floor(raw));
          }

          // Fallback: use is_now from change log if provided
          if (quantity === undefined && payload.is_now !== undefined && payload.is_now !== null) {
            const rawNow = Number(payload.is_now);
            quantity = Number.isNaN(rawNow) ? undefined : Math.max(0, Math.floor(rawNow));
          }

          // Fallback: look up current ivqtylaw from imported_table_rpro
          if (quantity === undefined) {
            try {
              const { data: rproRow, error: rproError } = await supabase
                .from('imported_table_rpro')
                .select('ivqtylaw')
                .eq('ivno', ivno)
                .maybeSingle();

              if (!rproError && rproRow) {
                const raw = Number(rproRow.ivqtylaw);
                quantity = Number.isNaN(raw) ? 0 : Math.max(0, Math.floor(raw));
                console.log(`[processUpdateVeeqoQtyBatch] RPRO task ${t.id}: ivno=${ivno}, live ivqtylaw=${rproRow.ivqtylaw}, using qty=${quantity}`);
              } else {
                console.warn(`[processUpdateVeeqoQtyBatch] Could not fetch RPRO ivqtylaw for ivno=${ivno} (task ${t.id}), skipping`);
                await updateTaskStatus(t.id, 'error', {
                  message: 'Could not fetch RPRO ivqtylaw for ivno',
                  ivno: ivno
                });
                continue;
              }
            } catch (lookupErr) {
              console.warn(`[processUpdateVeeqoQtyBatch] Error looking up RPRO ivqtylaw for ivno=${ivno}: ${lookupErr.message}`);
              await updateTaskStatus(t.id, 'error', {
                message: 'Error looking up RPRO ivqtylaw',
                ivno: ivno,
                error: lookupErr.message
              });
              continue;
            }
          }
        }

        // For DGACC tasks, check if variant has been uploaded to Shopify
        let uploadedToShopify = true; // Default to true for non-DGACC/SDASIN tasks
        if (t.task_type === 'update_veeqo_dgacc_qty') {
          const { data: variant, error: varError } = await supabase
            .from('t_product_variants')
            .select('uploaded_to_shopify_at')
            .eq('id', recordId)
            .maybeSingle();

          uploadedToShopify = !varError && variant && variant.uploaded_to_shopify_at !== null;

          if (!uploadedToShopify) {
            console.log(`[processUpdateVeeqoQtyBatch] Variant ${recordId} not uploaded to Shopify, skipping Veeqo update for task ${t.id}`);
            // Mark task as completed since stock_quantity was already updated
            await updateTaskStatus(t.id, 'completed', {
              message: `Stock quantity updated locally, variant not yet uploaded to Shopify`,
              sku: sku,
              stock_quantity_updated: true,
              veeqo_update_skipped: true
            });
            skippedTasksCount.notUploaded++;
            continue;
          }
        }

        // For SDASIN tasks, check if record has been uploaded to FBM (Amazon)
        if (t.task_type === 'update_veeqo_sdasin_qty') {
          const fbmUploadedAt = sdasinUploadStatusMap.get(recordId);
          uploadedToShopify = fbmUploadedAt !== null && fbmUploadedAt !== undefined;

          if (!uploadedToShopify) {
            console.log(`[processUpdateVeeqoQtyBatch] SDASIN ${recordId} not uploaded to Amazon FBM (fbm_uploaded_at is null), skipping Veeqo update for task ${t.id}`);
            await updateTaskStatus(t.id, 'completed', {
              message: `Not yet uploaded to Amazon so Veeqo update skipped`,
              sku: sku,
              veeqo_update_skipped: true
            });
            skippedTasksCount.notUploaded++;
            continue;
          }
        }

        if (!skuMap.has(sku)) {
          skuMap.set(sku, {
            quantity: quantity, // Use live quantity (fetched for SDASIN, payload for others)
            taskIds: [t.id],
            taskType: t.task_type,
            uploadedToShopify: uploadedToShopify,
            recordId: recordId,
            recordType: t.task_type.replace('update_veeqo_', '').replace('_qty', '')
          });
        } else {
          // If same SKU appears multiple times, use the latest quantity
          const existing = skuMap.get(sku);
          existing.taskIds.push(t.id);
          existing.quantity = quantity; // Use live quantity (fetched for SDASIN, payload for others)
        }
      } catch (parseError) {
        console.error(`[processUpdateVeeqoQtyBatch] Error parsing payload for task ${t.id}:`, parseError);
        await updateTaskStatus(t.id, 'error', {
          message: `Failed to parse payload: ${parseError.message}`,
          error: parseError.message
        });
      }
    }

    console.log(`[processUpdateVeeqoQtyBatch] Grouped ${pendingTasks.length} tasks into ${skuMap.size} unique SKUs`);
    console.log(`[processUpdateVeeqoQtyBatch] Skipped tasks: ${skippedTasksCount.notUploaded} not uploaded, ${skippedTasksCount.invalidPayload} invalid payload`);

    // If all tasks were skipped, complete the batch task with appropriate message
    if (skuMap.size === 0 && skippedTasksCount.notUploaded > 0) {
      console.log(`[processUpdateVeeqoQtyBatch] All tasks skipped (not uploaded to Shopify/Amazon)`);
      await updateTaskStatus(task.id, 'completed', {
        message: `All ${skippedTasksCount.notUploaded} task(s) skipped - items not yet uploaded to Shopify/Amazon`,
        tasks_processed: 0,
        tasks_skipped: skippedTasksCount.notUploaded
      });
      return;
    }

    if (skuMap.size === 0) {
      console.log(`[processUpdateVeeqoQtyBatch] No SKUs to process after filtering`);
      await updateTaskStatus(task.id, 'completed', {
        message: 'No tasks to process after filtering',
        tasks_processed: 0,
        tasks_skipped: skippedTasksCount.notUploaded + skippedTasksCount.invalidPayload
      });
      return;
    }

    // Process each SKU
    const results = [];
    for (const [sku, skuData] of skuMap.entries()) {
      try {
        console.log(`[processUpdateVeeqoQtyBatch] Processing SKU: ${sku}, quantity: ${skuData.quantity}`);

        // Get Veeqo product IDs for this SKU
        const veeqoIds = await getVeeqoId(sku);

        if (!veeqoIds || veeqoIds.length === 0) {
          console.log(`[processUpdateVeeqoQtyBatch] No Veeqo product found for SKU ${sku}`);

          // Mark all tasks for this SKU as error (not found)
          for (const taskId of skuData.taskIds) {
            await updateTaskStatus(taskId, 'error', {
              message: `SKU ${sku} not found in Veeqo`,
              sku: sku
            });
          }
          continue;
        }

        console.log(`[processUpdateVeeqoQtyBatch] Found ${veeqoIds.length} Veeqo products for SKU ${sku}`);

        // Update all Veeqo products for this SKU with concurrent batching and rate limiting
        let successCount = 0;
        let failureCount = 0;
        const errors = [];
        const updateResults = [];

        // Process Veeqo IDs in concurrent batches with rate limiting
        for (let i = 0; i < veeqoIds.length; i += CONCURRENT_LIMIT) {
          const batch = veeqoIds.slice(i, i + CONCURRENT_LIMIT);

          console.log(`[processUpdateVeeqoQtyBatch] Processing batch of ${batch.length} products for SKU ${sku} (${i + 1}-${Math.min(i + CONCURRENT_LIMIT, veeqoIds.length)} of ${veeqoIds.length})`);

          // Process up to CONCURRENT_LIMIT requests in parallel
          const batchResults = await Promise.all(
            batch.map(veeqoId => updateVeeqoProductWithRetry(veeqoId, skuData.quantity))
          );

          // Track results
          for (const result of batchResults) {
            updateResults.push(result);
            if (result.success) {
              successCount++;
            } else {
              failureCount++;
              errors.push(`Product ID ${result.veeqoId}: ${result.error}`);
            }
          }

          // Add delay between batches to respect rate limits (except after last batch)
          if (i + CONCURRENT_LIMIT < veeqoIds.length) {
            console.log(`[processUpdateVeeqoQtyBatch] Waiting ${DELAY_BETWEEN_BATCHES}ms before next batch...`);
            await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES));
          }
        }

        // Mark all tasks for this SKU as completed
        for (const taskId of skuData.taskIds) {
          if (successCount > 0 && failureCount === 0) {
            await updateTaskStatus(taskId, 'completed', {
              message: `Successfully updated ${successCount} Veeqo products for SKU ${sku}`,
              sku: sku,
              success_count: successCount
            });
          } else if (successCount > 0 && failureCount > 0) {
            await updateTaskStatus(taskId, 'completed', {
              message: `Partially updated Veeqo products for SKU ${sku}: ${successCount} succeeded, ${failureCount} failed`,
              sku: sku,
              success_count: successCount,
              failure_count: failureCount,
              errors: errors
            });
          } else {
            await updateTaskStatus(taskId, 'error', {
              message: `Failed to update all Veeqo products for SKU ${sku}`,
              sku: sku,
              failure_count: failureCount,
              errors: errors
            });
          }
        }

        results.push({
          sku,
          successCount,
          failureCount,
          veeqoProductCount: veeqoIds.length
        });
      } catch (err) {
        console.error(`[processUpdateVeeqoQtyBatch] Error processing SKU ${sku}:`, err);
        
        // Mark all tasks for this SKU as error
        for (const taskId of skuData.taskIds) {
          await updateTaskStatus(taskId, 'error', {
            message: `Error processing SKU ${sku}: ${err.message}`,
            error: err.message
          });
        }
      }
    }

    // Mark the triggering task as completed
    const totalSuccess = results.reduce((sum, r) => sum + r.successCount, 0);
    const totalFailure = results.reduce((sum, r) => sum + r.failureCount, 0);

    await updateTaskStatus(task.id, 'completed', {
      message: `Batch processing complete: ${pendingTasks.length} tasks, ${skuMap.size} unique SKUs`,
      tasks_processed: pendingTasks.length,
      unique_skus: skuMap.size,
      total_success: totalSuccess,
      total_failure: totalFailure,
      results: results
    });

    console.log(`[processUpdateVeeqoQtyBatch] Batch complete: ${pendingTasks.length} tasks processed, ${totalSuccess} successful, ${totalFailure} failed`);

  } catch (error) {
    console.error(`[processUpdateVeeqoQtyBatch] Unexpected error:`, error);
    await logError(error.message, `Processing batch for task ${task.id}`);
    await updateTaskStatus(task.id, 'error', {
      message: `Unexpected error: ${error.message}`,
      error: error.message
    });
  }
}

export default processUpdateVeeqoQtyBatch;

