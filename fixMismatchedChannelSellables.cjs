const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔧 FIX MISMATCHED CHANNEL SELLABLES');
console.log('='.repeat(60));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // 204 No Content is a success response with no body
    if (response.status === 204 || method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to get product details
async function getProductDetails(productId) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!result.success) {
    console.error(`❌ Error fetching product: ${result.error}`);
    return null;
  }
  
  return result.data;
}

// Function to find sellable by SKU
async function findSellableBySku(sku) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products?query=${encodeURIComponent(sku)}`);
  
  if (!result.success) {
    return null;
  }
  
  for (const product of result.data) {
    if (product.sellables) {
      const sellable = product.sellables.find(s => s.sku_code === sku);
      if (sellable) {
        return { product, sellable };
      }
    }
  }
  
  return null;
}

// Function to update channel sellable
async function updateChannelSellable(channelSellableId, newSellableId) {
  const data = {
    channel_sellable: {
      sellable_id: newSellableId
    }
  };
  
  const result = await makeVeeqoRequest(
    `https://api.veeqo.com/channel_sellables/${channelSellableId}`,
    'PUT',
    data
  );
  
  return result;
}

// Main function
async function fixMismatchedChannelSellables(productId) {
  console.log(`\n🔧 Fixing mismatched channel sellables in product ${productId}`);
  console.log('─'.repeat(60));
  
  // Get product details
  console.log(`\n📦 Fetching product ${productId}...`);
  const product = await getProductDetails(productId);
  
  if (!product) {
    return false;
  }
  
  console.log(`   Title: ${product.title}`);
  console.log(`   Sellables in this product:`);
  
  if (product.sellables) {
    product.sellables.forEach(sellable => {
      console.log(`      - ${sellable.sku_code} (Sellable ID: ${sellable.id})`);
    });
  }
  
  // Find all channel sellables where remote_sku doesn't match the sellable's SKU
  const mismatchedSellables = [];
  const skuCache = new Map(); // Cache SKU lookups
  
  if (product.channel_products) {
    for (const channelProduct of product.channel_products) {
      if (channelProduct.channel_sellables) {
        for (const channelSellable of channelProduct.channel_sellables) {
          const remoteSku = channelSellable.remote_sku;
          const currentSellableId = channelSellable.sellable_id;
          
          // Check if this sellable belongs to this product
          const productSellable = product.sellables?.find(s => s.id === currentSellableId);
          
          if (productSellable) {
            // Sellable exists in this product - check if SKU matches
            if (productSellable.sku_code !== remoteSku) {
              mismatchedSellables.push({
                channelProduct,
                channelSellable,
                currentSku: productSellable.sku_code,
                remoteSku: remoteSku,
                issue: 'SKU mismatch'
              });
            }
          } else {
            // Sellable doesn't exist in this product - it's orphaned
            mismatchedSellables.push({
              channelProduct,
              channelSellable,
              currentSku: 'Unknown (orphaned)',
              remoteSku: remoteSku,
              issue: 'Orphaned sellable'
            });
          }
        }
      }
    }
  }
  
  console.log(`\n📊 Found ${mismatchedSellables.length} mismatched channel sellable(s)`);
  
  if (mismatchedSellables.length === 0) {
    console.log(`✅ No mismatched channel sellables found`);
    return true;
  }
  
  // Process each mismatched sellable
  let fixedCount = 0;
  let notFoundCount = 0;
  
  for (const item of mismatchedSellables) {
    const { channelProduct, channelSellable, currentSku, remoteSku, issue } = item;
    
    console.log(`\n📺 Channel Product: ${channelProduct.id} - ${channelProduct.remote_title}`);
    console.log(`   Channel Sellable: ${channelSellable.id}`);
    console.log(`   Remote SKU: ${remoteSku}`);
    console.log(`   Current Sellable: ${currentSku} (ID: ${channelSellable.sellable_id})`);
    console.log(`   Issue: ${issue}`);
    
    // Try to find the correct sellable for this remote SKU
    let correctSellable = skuCache.get(remoteSku);
    
    if (!correctSellable) {
      console.log(`   🔍 Looking up correct sellable for ${remoteSku}...`);
      correctSellable = await findSellableBySku(remoteSku);
      if (correctSellable) {
        skuCache.set(remoteSku, correctSellable);
      }
    }
    
    if (correctSellable) {
      console.log(`   ✅ Found correct sellable: ${correctSellable.sellable.id} in product ${correctSellable.product.id}`);
      console.log(`   🔄 Updating: ${channelSellable.sellable_id} → ${correctSellable.sellable.id}`);
      
      const result = await updateChannelSellable(
        channelSellable.id,
        correctSellable.sellable.id
      );
      
      if (result.success) {
        console.log(`   ✅ Updated successfully`);
        fixedCount++;
      } else {
        console.log(`   ❌ Failed: ${result.error}`);
      }
    } else {
      console.log(`   ⚠️  Could not find sellable for SKU ${remoteSku}`);
      notFoundCount++;
    }
  }
  
  console.log(`\n${'='.repeat(60)}`);
  console.log(`📊 Summary:`);
  console.log(`   Total mismatched: ${mismatchedSellables.length}`);
  console.log(`   Fixed: ${fixedCount}`);
  console.log(`   Not found: ${notFoundCount}`);
  console.log(`   Failed: ${mismatchedSellables.length - fixedCount - notFoundCount}`);
  console.log(`${'='.repeat(60)}`);
  
  return fixedCount === mismatchedSellables.length;
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 1) {
  console.log(`
Usage: node fixMismatchedChannelSellables.cjs <product-id>

Example:
  node fixMismatchedChannelSellables.cjs 205980478

This will:
  1. Find all channel sellables in the product
  2. Check if each channel sellable's remote_sku matches the actual sellable's SKU
  3. For mismatches, look up the correct sellable for the remote_sku
  4. Update the channel sellable to point to the correct sellable

This fixes cases where channel sellables are pointing to the wrong variant.
`);
  process.exit(1);
}

const productId = args[0];

fixMismatchedChannelSellables(productId)
  .then(success => {
    if (success) {
      console.log(`\n✅ All done!`);
      process.exit(0);
    } else {
      console.log(`\n⚠️  Some issues remain`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

