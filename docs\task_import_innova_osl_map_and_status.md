# Task: import_innova_osl_map_and_status

## Overview
This task processes Innova vendor order sheet data by calculating inventory metrics, reading vendor availability data, updating order quantities in an Excel file, and emailing the completed order form. It serves as the automated workflow for preparing Innova disc orders based on current inventory levels and sales velocity.

## Task Type
`import_innova_osl_map_and_status`

## Handler File
`processImportInnovaOslMapAndStatusTask.js`

## Execution Flow

### Step 1: Calculate Disc Counts
Calculates inventory metrics for each Innova product (identified by `internal_id`):
- **In-stock count**: Number of unsold discs currently in inventory
- **Sold count**: Number of discs sold within the lookback period (default 60 days, configurable via `innova_disc_order_look_back_days`)

**Method**: 
1. First attempts to use RPC function `calculate_innova_internal_id_disc_counts()` for efficient batch processing
2. Falls back to individual calculation if RPC fails

**Logic**:
- Joins `it_innova_order_sheet_lines` → `t_order_sheet_lines` (via `vendor_internal_id`) → `t_discs` (via `vendor_osl_id`)
- Counts discs where `sold_date IS NULL` for in-stock
- Counts discs where `sold_date >= lookback_date` for sales velocity

### Step 2: Read Availability from Excel & Update Database
Reads the Innova order form Excel file and updates the database with vendor availability data:
- Opens the Excel file from: `C:\Users\<USER>\supabase_project\data\external data\innova\innovaorderform.xlsx`
- Reads the "Order_Table" sheet
- Extracts the "Availability" column values for each product (by `internal_id`)
- Updates `it_innova_order_sheet_lines.availability` with the vendor's current stock levels

### Step 3: Update Excel File with Order Quantities
Creates a timestamped copy of the Excel file and populates order quantities:
- Reads `qty` (or falls back to `ordered`) from `it_innova_order_sheet_lines` for each `internal_id`
- Writes quantities to the "Ordered" column in the Excel file
- Clears any strikethrough formatting from cells
- Saves timestamped file: `innovaorderform_YYYY-MM-DD_HH-MM-SS.xlsx`

### Step 4: Email Order Sheet
Sends the completed order form via email to `<EMAIL>` with summary statistics:
- Total discs to order (sum of qty values)
- Count of order lines updated
- Total in-stock count
- Total sold during lookback period
- Lookback period in days

## Database Tables

### Tables Modified (WRITE)
1. **`it_innova_order_sheet_lines`**
   - Updates: `in_stock`, `sold_last_90`, `availability`, `updated_at`
   - Key: `internal_id`

### Tables Read (READ)
1. **`t_config`**
   - Reads: `innova_disc_order_look_back_days` (default: 60)

2. **`t_order_sheet_lines`**
   - Reads: `id`, `vendor_internal_id`
   - Used to map Innova internal IDs to local OSL IDs

3. **`t_discs`**
   - Reads: `vendor_osl_id`, `sold_date`
   - Used to count in-stock and sold discs

4. **`it_innova_order_sheet_lines`**
   - Reads: `internal_id`, `qty`, `ordered`
   - Used to populate Excel order quantities

## Database Functions/Procedures

### RPC Functions Called
1. **`calculate_innova_internal_id_disc_counts()`**
   - Efficiently calculates in_stock and sold_last_90 for all Innova products
   - Returns: `rows_updated`, `in_stock_sum`, `sold_last_90_sum`
   - Defined in: `create_innova_internal_counts_procedure.sql`

## File System Operations

### Files Read
- `C:\Users\<USER>\supabase_project\data\external data\innova\innovaorderform.xlsx`

### Files Written
- `C:\Users\<USER>\supabase_project\data\external data\innova\innovaorderform_YYYY-MM-DD_HH-MM-SS.xlsx`

## External Integrations

### Email (SMTP)
- **Service**: Gmail SMTP (configurable)
- **Recipient**: <EMAIL>
- **Attachment**: Timestamped Excel order form
- **Subject Format**: `Innova Order Sheet — qty:{total} / cells:{count} / sold({days}d):{sold} — {date}`

## Configuration Dependencies

### Environment Variables
- `SMTP_HOST` (default: smtp.gmail.com)
- `SMTP_PORT` (default: 587)
- `EMAIL_USER`
- `EMAIL_PASS`

### Database Config (`t_config`)
- `innova_disc_order_look_back_days` (default: 60)

## Key Business Logic

### Inventory Calculation
- **In-stock**: Counts unsold discs (`sold_date IS NULL`) linked to Innova products via OSL mapping
- **Sales velocity**: Counts discs sold within lookback period, used to determine reorder quantities

### Vendor Availability Import
- Reads vendor's current stock levels from Excel "Availability" column
- Updates database to reflect what Innova currently has available
- Used in ordering logic to cap order quantities at vendor availability

### Order Quantity Logic
- Uses `qty` field (calculated elsewhere) or falls back to `ordered` field
- Only writes non-zero quantities to Excel
- Quantities are typically calculated based on: `(sold_last_90 / 2) - in_stock + out_of_stock_bump`

## Related Workflows
- Typically scheduled to run before placing Innova orders
- May be triggered manually via admin dashboard
- Works in conjunction with OSL matching and disc inventory management

## Error Handling
- RPC failure triggers fallback to individual calculation
- Excel file not found throws error and stops execution
- Database update errors are logged but don't stop the entire process
- Email failure is logged in task result but doesn't fail the task

## Success Criteria
Task completes successfully when:
1. All disc counts are calculated and updated in database
2. Availability values are read from Excel and updated in database
3. Excel file is created with order quantities
4. Email is sent with attachment

## Typical Execution Time
- Varies based on number of products (typically 1000-3000 records)
- RPC method: 10-30 seconds
- Individual calculation fallback: 2-5 minutes

