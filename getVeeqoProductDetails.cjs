const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔍 VEEQO PRODUCT DETAILS');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to get product details
async function getProductDetails(productId) {
  console.log(`\n🔍 Fetching product ${productId}...`);
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!result.success) {
    console.error(`   ❌ Error: ${result.error}`);
    return null;
  }
  
  const product = result.data;
  
  console.log(`\n📦 Product ID: ${product.id}`);
  console.log(`   Title: ${product.title}`);
  
  console.log(`\n📋 Sellables:`);
  if (product.sellables && product.sellables.length > 0) {
    product.sellables.forEach((sellable, index) => {
      console.log(`\n   ${index + 1}. Sellable ID: ${sellable.id}`);
      console.log(`      SKU: ${sellable.sku_code}`);
      if (sellable.stock_entries && sellable.stock_entries.length > 0) {
        const totalStock = sellable.stock_entries.reduce((sum, entry) => sum + (entry.physical_stock_level || 0), 0);
        console.log(`      Stock: ${totalStock}`);
      }
    });
  }
  
  console.log(`\n📺 Channel Products:`);
  if (product.channel_products && product.channel_products.length > 0) {
    product.channel_products.forEach((cp, index) => {
      console.log(`\n   ${index + 1}. Channel Product ID: ${cp.id}`);
      console.log(`      Channel: ${cp.channel?.name || 'Unknown'}`);
      console.log(`      Status: ${cp.status}`);
      console.log(`      Remote Title: ${cp.remote_title || 'N/A'}`);
      console.log(`      Remote ID: ${cp.remote_id || 'N/A'}`);
      
      if (cp.channel_sellables && cp.channel_sellables.length > 0) {
        console.log(`      Channel Sellables:`);
        cp.channel_sellables.forEach((cs, csIndex) => {
          console.log(`        ${csIndex + 1}. Remote SKU: ${cs.remote_sku} - Sellable ID: ${cs.sellable_id || 'N/A'}`);
        });
      }
    });
  } else {
    console.log(`   None`);
  }
  
  return product;
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length === 0) {
  console.log(`
Usage: node getVeeqoProductDetails.cjs <product-id>

Example:
  node getVeeqoProductDetails.cjs 116629736
`);
  process.exit(1);
}

const productId = args[0];

getProductDetails(productId)
  .then((product) => {
    if (product) {
      console.log(`\n✅ Done!`);
      process.exit(0);
    } else {
      console.log(`\n❌ Failed to get product details`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

