const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔗 VEEQO BULK CHANNEL SELLABLE RELINKER');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // 204 No Content is a success response with no body
    if (response.status === 204 || method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to get product details
async function getProductDetails(productId) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!result.success) {
    console.error(`❌ Error fetching product: ${result.error}`);
    return null;
  }
  
  return result.data;
}

// Function to find sellable by SKU
async function findSellableBySku(sku) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products?query=${encodeURIComponent(sku)}`);
  
  if (!result.success) {
    console.error(`   ❌ Error searching: ${result.error}`);
    return null;
  }
  
  for (const product of result.data) {
    if (product.sellables) {
      const sellable = product.sellables.find(s => s.sku_code === sku);
      if (sellable) {
        return { product, sellable };
      }
    }
  }
  
  return null;
}

// Function to update channel sellable
async function updateChannelSellable(channelSellableId, newSellableId, newRemoteSku) {
  const data = {
    channel_sellable: {
      sellable_id: newSellableId,
      remote_sku: newRemoteSku
    }
  };
  
  const result = await makeVeeqoRequest(
    `https://api.veeqo.com/channel_sellables/${channelSellableId}`,
    'PUT',
    data
  );
  
  return result;
}

// Main function
async function relinkAllChannelSellables(oldProductId, oldSku, newSku) {
  console.log(`\n🔗 Relinking all channel sellables from ${oldSku} to ${newSku}`);
  console.log('─'.repeat(50));
  
  // Get old product details
  console.log(`\n📦 Fetching product ${oldProductId}...`);
  const oldProduct = await getProductDetails(oldProductId);
  
  if (!oldProduct) {
    return false;
  }
  
  console.log(`   Title: ${oldProduct.title}`);
  
  // Find new sellable
  console.log(`\n🔍 Searching for target SKU: ${newSku}`);
  const newTarget = await findSellableBySku(newSku);
  
  if (!newTarget) {
    console.error(`❌ Could not find target SKU: ${newSku}`);
    return false;
  }
  
  console.log(`   ✅ Found: Product ID ${newTarget.product.id}, Sellable ID ${newTarget.sellable.id}`);
  console.log(`   Title: ${newTarget.product.title}`);
  
  // Find all channel sellables with the old SKU
  const channelSellablesToUpdate = [];
  
  if (oldProduct.channel_products) {
    for (const channelProduct of oldProduct.channel_products) {
      if (channelProduct.channel_sellables) {
        for (const channelSellable of channelProduct.channel_sellables) {
          if (channelSellable.remote_sku === oldSku) {
            channelSellablesToUpdate.push({
              channelProduct,
              channelSellable
            });
          }
        }
      }
    }
  }
  
  console.log(`\n📊 Found ${channelSellablesToUpdate.length} channel sellable(s) to update`);
  
  if (channelSellablesToUpdate.length === 0) {
    console.log(`✅ No channel sellables found with SKU ${oldSku}`);
    return true;
  }
  
  // Update each channel sellable
  let successCount = 0;
  for (const item of channelSellablesToUpdate) {
    const { channelProduct, channelSellable } = item;
    
    console.log(`\n📺 Channel Product: ${channelProduct.id} - ${channelProduct.remote_title}`);
    console.log(`   Channel Sellable: ${channelSellable.id}`);
    console.log(`   Remote SKU: ${channelSellable.remote_sku} → ${newSku}`);
    console.log(`   Sellable ID: ${channelSellable.sellable_id} → ${newTarget.sellable.id}`);
    
    const result = await updateChannelSellable(
      channelSellable.id,
      newTarget.sellable.id,
      newSku
    );
    
    if (result.success) {
      console.log(`   ✅ Updated successfully`);
      successCount++;
    } else {
      console.log(`   ❌ Failed: ${result.error}`);
    }
  }
  
  console.log(`\n📊 Summary: ${successCount} of ${channelSellablesToUpdate.length} channel sellables updated`);
  
  return successCount === channelSellablesToUpdate.length;
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 3) {
  console.log(`
Usage: node relinkAllChannelSellables.cjs <old-product-id> <old-sku> <new-sku>

Example:
  node relinkAllChannelSellables.cjs 116629736 FM13756 R13756

This will find all channel sellables with remote_sku=FM13756 in product 116629736
and relink them to the sellable for R13756.
`);
  process.exit(1);
}

const oldProductId = args[0];
const oldSku = args[1];
const newSku = args[2];

relinkAllChannelSellables(oldProductId, oldSku, newSku)
  .then(success => {
    if (success) {
      console.log(`\n✅ All done!`);
      process.exit(0);
    } else {
      console.log(`\n⚠️  Some updates failed`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

