import fetch from 'node-fetch';
import { findVariantBySku } from './shopifyGraphQL.js';
import { getShopifyCredentials } from './shopifyStoreConfig.js';

function approxTimesEqual(a, b, toleranceMs = 2 * 60 * 1000) {
  if (!a || !b) return false;
  return new Date(a).getTime() === new Date(b).getTime();
}

function toMoneyString(value) {
  if (value === null || value === undefined) return null;
  const n = Number(value);
  if (Number.isNaN(n)) return null;
  return n.toFixed(2);
}

function moneyEquals(a, b) {
  if (a === null || a === undefined) return b === null || b === undefined;
  if (b === null || b === undefined) return false;
  return Math.abs(Number(a) - Number(b)) < 0.005; // tolerate rounding
}

export default async function processShopifySaleEndTask(task, { supabase, updateTaskStatus, logError } = {}) {
  if (!supabase) {
    console.error('[processShopifySaleEndTask] Supabase client not provided in context');
    return;
  }

  try {
    await updateTaskStatus(task.id, 'processing');

    const salePriceId = task?.payload?.sale_price_id ?? task?.payload?.salePriceId ?? task?.payload?.id;
    if (!salePriceId) throw new Error('sale_price_id is required in payload');

    const storeId = task?.payload?.shopify_store_id || 1; // Default to DZ Discs for backward compatibility
    const { endpoint, accessToken, storeName } = getShopifyCredentials(storeId);
    const variantsEndpoint = endpoint?.replace('graphql.json', 'variants/');

    console.log(`[processShopifySaleEndTask] Processing sale end for ${storeName} (store ${storeId})`);

    const { data: sale, error: saleErr } = await supabase
      .from('t_shopify_sale_prices')
      .select('*')
      .eq('id', salePriceId)
      .single();

    let saleMissing = false;
    if (saleErr) {
      const msg = saleErr.message || '';
      if (/no rows?/i.test(msg) || saleErr.code === 'PGRST116') {
        saleMissing = true;
      } else {
        throw new Error(`Failed to fetch sale price record id=${salePriceId}: ${saleErr.message}`);
      }
    }
    if (!sale && !saleMissing) {
      saleMissing = true;
    }

    const nowIso = new Date().toISOString();

    if (saleMissing) {
      // Fallback path: sale was deleted; restore by SKU in payload
      const sku = task?.payload?.shopify_sku || task?.payload?.sku;
      if (!sku) {
        await updateTaskStatus(task.id, 'completed', {
          status: 'skipped_missing_sale_record_no_sku',
          message: `Sale record missing for id=${salePriceId} and payload missing shopify_sku` ,
          sale_price_id: salePriceId
        });
        return;
      }

      // Derive local variant id (DGACC{id}) and fetch original prices
      let localVariantId = null;
      const m = /^DGACC(\d+)$/.exec(sku);
      if (m) localVariantId = parseInt(m[1], 10);

      let originalRetail = null;
      let originalCompareAt = null;

      if (localVariantId) {
        const { data: variantRow, error: varErr } = await supabase
          .from('t_product_variants')
          .select('price, msrp')
          .eq('id', localVariantId)
          .single();
        if (varErr) throw new Error(`Failed to fetch original pricing for variant ${localVariantId}: ${varErr.message}`);
        if (variantRow) {
          originalRetail = variantRow.price;
          originalCompareAt = variantRow.msrp;
        }
      }

      if (originalRetail === null && originalCompareAt === null) {
        await updateTaskStatus(task.id, 'completed', {
          status: 'skipped_no_original_prices',
          message: 'Could not determine original prices to restore from t_product_variants (no sale record)',
          sale_price_id: salePriceId,
          sku
        });
        return;
      }

      // Resolve Shopify variant
      const variantInfo = await findVariantBySku(sku, storeId);
      if (!variantInfo) {
        await updateTaskStatus(task.id, 'completed', {
          status: 'variant_not_found',
          message: `No Shopify variant found for SKU ${sku} on ${storeName}`,
          sale_price_id: salePriceId,
          sku,
          store: storeName,
          store_id: storeId
        });
        return;
      }
      const variantIdNumeric = String(variantInfo.variantId).split('/').pop();

      const variantPayload = { variant: { id: variantIdNumeric } };
      const targetPrice = toMoneyString(originalRetail);
      const targetCompare = (originalCompareAt === null || originalCompareAt === undefined)
        ? null
        : toMoneyString(originalCompareAt);
      if (targetPrice !== null) variantPayload.variant.price = targetPrice;
      // Note: allow null to clear compare_at on Shopify
      variantPayload.variant.compare_at_price = targetCompare;

      const resp = await fetch(`${variantsEndpoint}${variantIdNumeric}.json`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': accessToken
        },
        body: JSON.stringify(variantPayload)
      });
      const bodyText = await resp.text();
      if (!resp.ok) {
        throw new Error(`Shopify PUT variant ${variantIdNumeric} failed: ${resp.status} ${resp.statusText} - ${bodyText}`);
      }
      let bodyJson = null;
      try { bodyJson = JSON.parse(bodyText); } catch {}
      const returnedVariant = bodyJson?.variant;
      if (returnedVariant) {
        const returnedPrice = returnedVariant.price;
        const returnedCompare = returnedVariant.compare_at_price ?? null;
        const okPrice = targetPrice === null ? true : moneyEquals(returnedPrice, targetPrice);
        const okCompare = (targetCompare === null && (returnedCompare === null || returnedCompare === undefined))
          || moneyEquals(returnedCompare, targetCompare);
        if (!okPrice || !okCompare) {
          throw new Error(`Shopify updated but returned values differ. expected price=${targetPrice}, compare_at=${targetCompare}; got price=${returnedPrice}, compare_at=${returnedCompare}`);
        }
      }

      await updateTaskStatus(task.id, 'completed', {
        status: 'restored',
        message: 'Standard pricing restored on Shopify (no sale record)',
        sale_price_id: salePriceId,
        sku,
        variant_id: variantIdNumeric,
        restored_price: originalRetail,
        restored_compare_at: originalCompareAt
      });
      return;
    }

    // Normal path when sale record exists
    const schedAt = task.scheduled_at;

    // Validate timing: end task must match end_at exactly
    if (!approxTimesEqual(schedAt, sale.end_at)) {
      await updateTaskStatus(task.id, 'completed', {
        status: 'skipped_stale_task',
        message: `Task scheduled_at ${schedAt} does not match sale.end_at ${sale.end_at}`,
        sale_price_id: salePriceId
      });
      return;
    }

    // Must have been applied to restore
    if (!sale.sale_price_applied_at) {
      await updateTaskStatus(task.id, 'completed', {
        status: 'skipped_not_applied',
        message: 'Sale pricing not applied yet; nothing to restore',
        sale_price_id: salePriceId
      });
      return;
    }

    // Idempotency: already restored
    if (sale.standard_price_restored_at) {
      await updateTaskStatus(task.id, 'completed', {
        status: 'already_restored',
        message: 'Standard pricing already restored',
        sale_price_id: salePriceId,
        restored_at: sale.standard_price_restored_at
      });
      return;
    }

    const sku = sale.shopify_sku;
    if (!sku) throw new Error('shopify_sku is missing on sale record');

    // Derive local variant id from DGACC SKU if applicable
    let localVariantId = null;
    const m = /^DGACC(\d+)$/.exec(sku);
    if (m) localVariantId = parseInt(m[1], 10);

    let originalRetail = null;
    let originalCompareAt = null;

    if (localVariantId) {
      const { data: variantRow, error: varErr } = await supabase
        .from('t_product_variants')
        .select('price, msrp')
        .eq('id', localVariantId)
        .single();
      if (varErr) throw new Error(`Failed to fetch original pricing for variant ${localVariantId}: ${varErr.message}`);
      if (variantRow) {
        originalRetail = variantRow.price;
        originalCompareAt = variantRow.msrp;
      }
    }

    // Fallback: if cannot resolve local variant, we still can restore to compare_at=null if sale had compare at
    // but per requirement we restore original prices from t_product_variants only.
    if (originalRetail === null && originalCompareAt === null) {
      await updateTaskStatus(task.id, 'completed', {
        status: 'skipped_no_original_prices',
        message: 'Could not determine original prices to restore from t_product_variants',
        sale_price_id: salePriceId,
        sku
      });
      return;
    }

    // Resolve Shopify variant
    const variantInfo = await findVariantBySku(sku);
    if (!variantInfo) {
      await updateTaskStatus(task.id, 'completed', {
        status: 'variant_not_found',
        message: `No Shopify variant found for SKU ${sku}`,
        sale_price_id: salePriceId,
        sku
      });
      return;
    }
    const variantIdNumeric = String(variantInfo.variantId).split('/').pop();

    const variantPayload = { variant: { id: variantIdNumeric } };
    const targetPrice = toMoneyString(originalRetail);
    const targetCompare = (originalCompareAt === null || originalCompareAt === undefined)
      ? null
      : toMoneyString(originalCompareAt);
    if (targetPrice !== null) variantPayload.variant.price = targetPrice;
    variantPayload.variant.compare_at_price = targetCompare; // null clears compare_at on Shopify

    const resp = await fetch(`${variantsEndpoint}${variantIdNumeric}.json`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': accessToken
      },
      body: JSON.stringify(variantPayload)
    });

    const bodyText = await resp.text();
    if (!resp.ok) {
      throw new Error(`Shopify PUT variant ${variantIdNumeric} failed: ${resp.status} ${resp.statusText} - ${bodyText}`);
    }
    let bodyJson = null;
    try { bodyJson = JSON.parse(bodyText); } catch {}
    const returnedVariant = bodyJson?.variant;
    if (returnedVariant) {
      const returnedPrice = returnedVariant.price;
      const returnedCompare = returnedVariant.compare_at_price ?? null;
      const okPrice = targetPrice === null ? true : moneyEquals(returnedPrice, targetPrice);
      const okCompare = (targetCompare === null && (returnedCompare === null || returnedCompare === undefined))
        || moneyEquals(returnedCompare, targetCompare);
      if (!okPrice || !okCompare) {
        throw new Error(`Shopify updated but returned values differ. expected price=${targetPrice}, compare_at=${targetCompare}; got price=${returnedPrice}, compare_at=${returnedCompare}`);
      }
    }

    const { error: updSaleErr } = await supabase
      .from('t_shopify_sale_prices')
      .update({ standard_price_restored_at: nowIso, last_restore_status: 'success' })
      .eq('id', salePriceId);
    if (updSaleErr) await logError?.('[processShopifySaleEndTask] Failed updating sale record after restore', { salePriceId, err: updSaleErr.message });

    await updateTaskStatus(task.id, 'completed', {
      status: 'restored',
      message: `Standard pricing restored on ${storeName}`,
      sale_price_id: salePriceId,
      sku,
      variant_id: variantIdNumeric,
      restored_price: originalRetail,
      restored_compare_at: originalCompareAt,
      store: storeName,
      store_id: storeId
    });
  } catch (err) {
    console.error('[processShopifySaleEndTask] Error:', err.message);
    const storeId = task?.payload?.shopify_store_id || 1;
    const { storeName } = getShopifyCredentials(storeId);

    try {
      const salePriceId = task?.payload?.sale_price_id ?? task?.payload?.salePriceId ?? task?.payload?.id;
      if (salePriceId) {
        await supabase
          .from('t_shopify_sale_prices')
          .update({ last_restore_status: `failed: ${err.message}` })
          .eq('id', salePriceId);
      }
    } catch (e2) {
      await logError?.('[processShopifySaleEndTask] Failed to update sale last_restore_status after error', { err: e2.message });
    }

    await updateTaskStatus(task.id, 'error', {
      message: `Error restoring standard pricing on ${storeName}`,
      error: err.message,
      store: storeName,
      store_id: storeId
    });
  }
}

