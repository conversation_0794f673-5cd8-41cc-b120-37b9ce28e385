# RPRO Amazon Size Tier Check

## Overview

This enhancement adds Amazon-specific validation to the `check_if_rpro_is_ready` task handler. It checks whether RPRO products that have been uploaded to Amazon have the required size tier mapping.

## Changes Made

### 1. Database Schema Change

**File:** `add_todo_for_amaz_field_to_rpro_table.sql`

Added a new column `todo_for_amaz` to the `imported_table_rpro` table to store Amazon-specific readiness issues separately from general RPRO readiness issues.

```sql
ALTER TABLE public.imported_table_rpro 
ADD COLUMN todo_for_amaz TEXT;
```

**To apply this migration:**
```bash
# Run the SQL file in your Supabase SQL editor or via RPC
node -e "
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
require('dotenv').config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);
const sql = fs.readFileSync('add_todo_for_amaz_field_to_rpro_table.sql', 'utf8');

supabase.rpc('exec_sql', { sql_query: sql })
  .then(({ error }) => {
    if (error) console.error('Error:', error);
    else console.log('Migration applied successfully');
  });
"
```

### 2. Task Handler Enhancement

**File:** `processCheckIfRproIsReadyTask.js`

Enhanced the `check_if_rpro_is_ready` task handler to:

1. **Fetch the new field:** Added `todo_for_amaz` to the SELECT query when fetching RPRO records
2. **Check Amazon listings:** Query `t_amaz_acc_listings` to find if this RPRO product has been uploaded to Amazon
3. **Validate size tier mapping:** Check if a corresponding entry exists in `tj_rpro_product_to_size_tier`
4. **Update todo_for_amaz field:** Write appropriate message based on the check results

## Logic Flow

```
For each RPRO record being checked:
  1. Query t_amaz_acc_listings WHERE rpro_id = ivno AND uploaded_to_amazon_at IS NOT NULL
  2. If Amazon listings found:
     a. Query tj_rpro_product_to_size_tier WHERE rpro_id = ivno
     b. If NO size tier join found:
        - Set todo_for_amaz = "uploaded to amazon product needs a size tier join (checked on [timestamp])"
     c. If size tier join exists:
        - Set todo_for_amaz = "No Amazon Issues Found on [timestamp]"
  3. If NO Amazon listings found:
     - Set todo_for_amaz = "No Amazon Issues Found on [timestamp]"
  4. Update imported_table_rpro with both todo and todo_for_amaz
```

## SQL Query Equivalent

The check performed by the task handler is equivalent to:

```sql
SELECT 
  r.ivno,
  r.todo,
  r.todo_for_amaz,
  a.listing_sku,
  a.uploaded_to_amazon_at,
  j.rpro_id as has_size_tier
FROM imported_table_rpro r
JOIN t_amaz_acc_listings a ON a.rpro_id = r.ivno
LEFT JOIN tj_rpro_product_to_size_tier j ON j.rpro_id = r.ivno
WHERE a.uploaded_to_amazon_at IS NOT NULL
  AND j.rpro_id IS NULL;
```

Records returned by this query will have `todo_for_amaz` set to indicate they need a size tier join.

## Example Output

### Case 1: Product uploaded to Amazon without size tier
```
ivno: 12345
todo: "No Issues Found on 2025-01-27T10:30:00.000Z"
todo_for_amaz: "uploaded to amazon product needs a size tier join (checked on 2025-01-27T10:30:00.000Z)"
```

### Case 2: Product uploaded to Amazon with size tier
```
ivno: 12346
todo: "No Issues Found on 2025-01-27T10:30:00.000Z"
todo_for_amaz: "No Amazon Issues Found on 2025-01-27T10:30:00.000Z"
```

### Case 3: Product not uploaded to Amazon
```
ivno: 12347
todo: "No Issues Found on 2025-01-27T10:30:00.000Z"
todo_for_amaz: "No Amazon Issues Found on 2025-01-27T10:30:00.000Z"
```

## Testing

To test the new functionality:

1. **Apply the database migration** (see above)
2. **Restart the task queue worker** to pick up the code changes
3. **Enqueue a test task** for an RPRO record that has Amazon listings:

```javascript
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testAmazonCheck() {
  // Find an RPRO record with Amazon listings
  const { data: testRecord } = await supabase
    .from('t_amaz_acc_listings')
    .select('rpro_id')
    .not('uploaded_to_amazon_at', 'is', null)
    .limit(1)
    .single();

  if (!testRecord) {
    console.log('No test records found');
    return;
  }

  // Enqueue a check task
  const { data, error } = await supabase
    .from('t_task_queue')
    .insert({
      task_type: 'check_if_rpro_is_ready',
      payload: { ivno: testRecord.rpro_id },
      status: 'pending',
      scheduled_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      enqueued_by: 'test_amazon_check'
    })
    .select()
    .single();

  console.log('Test task enqueued:', data);
}

testAmazonCheck();
```

4. **Check the results** in the `imported_table_rpro` table:

```sql
SELECT ivno, todo, todo_for_amaz
FROM imported_table_rpro
WHERE ivno = [test_ivno];
```

## Monitoring

To find all RPRO records with Amazon size tier issues:

```sql
SELECT 
  r.ivno,
  r.todo_for_amaz,
  a.listing_sku,
  a.uploaded_to_amazon_at
FROM imported_table_rpro r
JOIN t_amaz_acc_listings a ON a.rpro_id = r.ivno
WHERE r.todo_for_amaz LIKE '%needs a size tier join%'
  AND a.uploaded_to_amazon_at IS NOT NULL
ORDER BY r.ivno;
```

## Related Tables

- **imported_table_rpro**: Main RPRO data table with `todo` and `todo_for_amaz` fields
- **t_amaz_acc_listings**: Amazon listings with `rpro_id` and `uploaded_to_amazon_at` fields
- **tj_rpro_product_to_size_tier**: Junction table mapping RPRO products to size tiers
- **t_task_queue**: Task queue where `check_if_rpro_is_ready` tasks are processed

