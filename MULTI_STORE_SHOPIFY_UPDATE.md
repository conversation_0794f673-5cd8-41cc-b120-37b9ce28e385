# Multi-Store Shopify Product Updates

This document describes the implementation of multi-store support for Shopify product title and description updates.

## Overview

The system now supports updating products on multiple Shopify stores (DZ Discs and Tippmann Parts) based on the `shopify_store_id` field in `t_products`.

## Database Schema

### t_shopify_stores Table
```sql
create table public.t_shopify_stores (
  id smallint not null,
  name text not null,
  created_at timestamp with time zone not null default now(),
  product_tag_filter text null,
  constraint t_shopify_stores_pkey primary key (id)
);
```

**Store IDs:**
- `1` = DZ Discs (dzdiscs-new-releases.myshopify.com)
- `2` = Tippmann Parts (tippmannparts.myshopify.com)

### t_products.shopify_store_id
Each product in `t_products` now has a `shopify_store_id` field that references `t_shopify_stores.id`.

## Environment Variables Required

### Current (DZ Discs - Store ID 1)
```
SHOPIFY_ENDPOINT=https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/graphql.json
SHOPIFY_ACCESS_TOKEN=shpat_...
```

### Tippmann Parts (Store ID 2)
```
SHOPIFY_ENDPOINT_2=https://tippmannparts.myshopify.com/admin/api/2024-01/graphql.json
SHOPIFY_ACCESS_TOKEN_2=shpat_...
SHOPIFY_GRAPHQL_ENDPOINT_2=https://tippmannparts.myshopify.com/admin/api/2024-01/graphql.json
```

**✅ COMPLETED:** Tippmann Parts credentials added to `.env` file.

## Implementation Details

### 1. Shopify Store Configuration Helper (`shopifyStoreConfig.js`)
New utility module that:
- Maps store IDs to environment variables
- Provides credentials based on store ID
- Includes helper functions for GraphQL and REST API calls
- Validates credentials before use

### 2. Updated Task Handlers

#### `processUpdateShopifyProductTitleTask.js`
- Now reads `shopify_store_id` from task payload
- Uses `getShopifyCredentials(shopifyStoreId)` to get the correct credentials
- Updates the product on the correct Shopify store
- Includes store name in completion messages

#### `processUpdateShopifyProductDescriptionTask.js`
- Now reads `shopify_store_id` from task payload
- Uses `getShopifyCredentials(shopifyStoreId)` to get the correct credentials
- Updates the product on the correct Shopify store
- Includes store name in completion messages

### 3. Database Triggers (Supabase/Lovable)

#### Trigger Function: `enqueue_update_shopify_title_task()`
**File:** `sql/triggers/t_products_enqueue_shopify_title_update.sql`

Updates the existing function to include `shopify_store_id` in the task payload.

**Trigger:** `trg_t_products_name_after_shopify_upload`
- Fires when `t_products.name` changes
- Only when `shopify_uploaded_at IS NOT NULL`

#### Trigger Function: `enqueue_update_shopify_description_task()`
**File:** `sql/triggers/t_products_enqueue_shopify_description_update.sql`

Updates the existing function to include `shopify_store_id` in the task payload.

**Trigger:** `trg_t_products_description_after_shopify_upload`
- Fires when `t_products.description` changes
- Only when `shopify_uploaded_at IS NOT NULL`

## Task Payload Format

### update_shopify_product_title
```json
{
  "shopify_handle": "product-handle",
  "shopify_store_id": 1,
  "product_id": 123
}
```

### update_shopify_product_description
```json
{
  "shopify_handle": "product-handle",
  "shopify_store_id": 2,
  "product_id": 456
}
```

## Deployment Steps

### Step 1: Add Tippmann Parts Credentials ✅ COMPLETED
Tippmann Parts credentials have been added to `.env` file:
   ```
   SHOPIFY_ENDPOINT_2=...
   SHOPIFY_ACCESS_TOKEN_2=...
   SHOPIFY_GRAPHQL_ENDPOINT_2=...
   ```

### Step 2: Deploy SQL Trigger Updates (Lovable/Supabase)
Run these SQL files in Supabase:
1. `sql/triggers/t_products_enqueue_shopify_title_update.sql`
2. `sql/triggers/t_products_enqueue_shopify_description_update.sql`

### Step 3: Restart Worker (Augment/VS Code)
Restart the task queue worker to pick up the new code:
```bash
pm2 restart worker-daemon
```

## Testing

### Test Title Update
1. Find a product with `shopify_store_id = 1` (DZ Discs)
2. Update the `name` field in `t_products`
3. Verify task is enqueued with correct `shopify_store_id`
4. Verify worker updates the correct Shopify store

### Test Description Update
1. Find a product with `shopify_store_id = 2` (Tippmann Parts)
2. Update the `description` field in `t_products`
3. Verify task is enqueued with correct `shopify_store_id`
4. Verify worker updates the correct Shopify store

## Error Handling

The handlers will error if:
- `shopify_store_id` is missing from payload
- `shopify_store_id` is not 1 or 2
- Credentials are missing for the specified store
- Product not found on Shopify
- Shopify API returns an error

All errors are logged to `t_task_queue.result` with descriptive messages.

## Files Modified

### New Files
- `shopifyStoreConfig.js` - Store configuration helper
- `sql/triggers/t_products_enqueue_shopify_title_update.sql` - Title trigger update
- `sql/triggers/t_products_enqueue_shopify_description_update.sql` - Description trigger update
- `MULTI_STORE_HANDLER_PATTERN.md` - Pattern documentation for updating handlers
- `testMultiStoreConfig.js` - Configuration test script
- `testMultiStoreTaskHandlers.js` - Task handler test script
- `deployMultiStoreTriggers.js` - SQL deployment script

### Modified Files - ✅ COMPLETED
- `processUpdateShopifyProductTitleTask.js` - Multi-store support with backward compatibility
- `processUpdateShopifyProductDescriptionTask.js` - Multi-store support with backward compatibility
- `processDeleteSkuFromShopifyTask.js` - Multi-store support with backward compatibility
- `shopifyGraphQL.js` - Updated executeShopifyGraphQL to accept storeId parameter

### Modified Files - 🔄 REMAINING (Need Manual Update)
The following files still use hardcoded Shopify credentials and need to be updated following the pattern in `MULTI_STORE_HANDLER_PATTERN.md`:

**High Priority (Frequently Used):**
- `processDeleteVariantFromShopifyTask.js`
- `processUpdateDiscVariantPriceOnShopifyTask.js`
- `processUpdateDiscVariantMsrpOnShopifyTask.js`
- `processSyncProductVariantToShopifyTask.js`
- `processShopifySaleStartTask.js`
- `processShopifySaleEndTask.js`

**Medium Priority:**
- `processDiscSoldOrUnsoldUpdateShopifyDirectlyTask.js`
- `processDiscUpdatedDeleteFromShopifyTask.js`
- `processUpdateShopifyDiscTitleTask.js`
- `processClearShopifyCountForSoldDiscTask.js`

**Low Priority:**
- `processFixOslShopifyProductWo3OptionsTask.js`

**Shared Utilities (Partially Updated):**
- `shopifyGraphQL.js` - executeShopifyGraphQL updated, but other functions need storeId parameter added:
  - `setInventoryItemQuantity(inventoryItemId, quantity, storeId = 1)`
  - `setInventoryItemToZero(inventoryItemId, storeId = 1)`
  - `getInventoryItemIdForProduct(productId, storeId = 1)`
  - `findVariantBySku(sku, storeId = 1)`
  - `findOslProductInShopify(sku, storeId = 1)`
  - `setProductInventoryToZero(productId, storeId = 1)`

## Backward Compatibility

**All updated handlers are backward compatible!**

- If a task payload does NOT include `shopify_store_id`, the handler defaults to `storeId = 1` (DZ Discs)
- This means all existing tasks and triggers will continue to work without modification
- Only new tasks that explicitly set `shopify_store_id: 2` will use Tippmann Parts

### Example:
```javascript
// Old task (no store_id) - works fine, uses DZ Discs
{
  "task_type": "delete_sku_from_shopify",
  "payload": {
    "sku": "D12345"
  }
}

// New task (with store_id) - uses Tippmann Parts
{
  "task_type": "delete_sku_from_shopify",
  "payload": {
    "sku": "TP67890",
    "shopify_store_id": 2
  }
}
```

## Remaining Work

### For Immediate Use
The following handlers are **already updated** and ready to use with multi-store support:
- ✅ `update_shopify_product_title`
- ✅ `update_shopify_product_description`
- ✅ `delete_sku_from_shopify`

### For Future Updates
The remaining handlers listed above should be updated following the pattern in `MULTI_STORE_HANDLER_PATTERN.md` when:
1. You need to use them with Tippmann Parts store
2. You have time for a comprehensive update
3. You encounter issues with the current implementation

**Note:** Until updated, these handlers will continue to work with DZ Discs only (using hardcoded credentials).

## Next Steps

1. ✅ **COMPLETED:** Tippmann Parts credentials added to `.env`
2. ✅ **COMPLETED:** SQL trigger updates deployed via Supabase
3. ✅ **COMPLETED:** Worker restarted to load new code
4. **Test** with products from both stores
5. **Update remaining handlers** as needed (see pattern document)

