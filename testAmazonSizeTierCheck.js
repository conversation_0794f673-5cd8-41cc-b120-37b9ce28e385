// testAmazonSizeTierCheck.js
// Test script for the Amazon size tier check functionality in check_if_rpro_is_ready task

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testAmazonSizeTierCheck() {
  console.log('=== Testing Amazon Size Tier Check ===\n');

  // Step 1: Find RPRO records with Amazon listings
  console.log('Step 1: Finding RPRO records with uploaded Amazon listings...');
  const { data: amazonListings, error: listingsError } = await supabase
    .from('t_amaz_acc_listings')
    .select('rpro_id, listing_sku, uploaded_to_amazon_at')
    .not('uploaded_to_amazon_at', 'is', null)
    .limit(5);

  if (listingsError) {
    console.error('Error fetching Amazon listings:', listingsError);
    return;
  }

  if (!amazonListings || amazonListings.length === 0) {
    console.log('No Amazon listings found with uploaded_to_amazon_at set.');
    return;
  }

  console.log(`Found ${amazonListings.length} Amazon listings:\n`);
  amazonListings.forEach(listing => {
    console.log(`  - RPRO ID: ${listing.rpro_id}, SKU: ${listing.listing_sku}, Uploaded: ${listing.uploaded_to_amazon_at}`);
  });

  // Step 2: Check which ones have size tier mappings
  console.log('\nStep 2: Checking size tier mappings...');
  const rproIds = amazonListings.map(l => l.rpro_id);
  
  const { data: sizeTierMappings, error: sizeTierError } = await supabase
    .from('tj_rpro_product_to_size_tier')
    .select('rpro_id, size_tier_id')
    .in('rpro_id', rproIds);

  if (sizeTierError) {
    console.error('Error fetching size tier mappings:', sizeTierError);
    return;
  }

  const mappedRproIds = new Set(sizeTierMappings?.map(m => m.rpro_id) || []);
  
  console.log(`\nSize tier mapping status:`);
  amazonListings.forEach(listing => {
    const hasSizeTier = mappedRproIds.has(listing.rpro_id);
    console.log(`  - RPRO ID ${listing.rpro_id}: ${hasSizeTier ? '✓ HAS size tier' : '✗ MISSING size tier'}`);
  });

  // Step 3: Find a test candidate (one with and one without size tier)
  const withSizeTier = amazonListings.find(l => mappedRproIds.has(l.rpro_id));
  const withoutSizeTier = amazonListings.find(l => !mappedRproIds.has(l.rpro_id));

  console.log('\n=== Test Candidates ===');
  if (withSizeTier) {
    console.log(`With size tier: RPRO ID ${withSizeTier.rpro_id}`);
  }
  if (withoutSizeTier) {
    console.log(`Without size tier: RPRO ID ${withoutSizeTier.rpro_id}`);
  }

  // Step 4: Enqueue test tasks
  console.log('\n=== Enqueueing Test Tasks ===');
  
  const tasksToEnqueue = [];
  if (withSizeTier) {
    tasksToEnqueue.push({
      ivno: withSizeTier.rpro_id,
      description: 'RPRO with size tier (should pass)'
    });
  }
  if (withoutSizeTier) {
    tasksToEnqueue.push({
      ivno: withoutSizeTier.rpro_id,
      description: 'RPRO without size tier (should flag issue)'
    });
  }

  const enqueuedTasks = [];
  for (const testCase of tasksToEnqueue) {
    const now = new Date().toISOString();
    const { data: task, error: enqueueError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'check_if_rpro_is_ready',
        payload: { ivno: testCase.ivno },
        status: 'pending',
        scheduled_at: now,
        created_at: now,
        enqueued_by: 'testAmazonSizeTierCheck.js'
      })
      .select()
      .single();

    if (enqueueError) {
      console.error(`Error enqueueing task for ${testCase.description}:`, enqueueError);
    } else {
      console.log(`✓ Enqueued task ${task.id} for ${testCase.description} (ivno: ${testCase.ivno})`);
      enqueuedTasks.push({ ...task, description: testCase.description });
    }
  }

  // Step 5: Wait for tasks to complete and check results
  console.log('\n=== Waiting for Tasks to Complete ===');
  console.log('Waiting 30 seconds for worker to process tasks...\n');
  
  await new Promise(resolve => setTimeout(resolve, 30000));

  console.log('=== Checking Results ===\n');
  
  for (const task of enqueuedTasks) {
    // Check task status
    const { data: updatedTask, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', task.id)
      .single();

    if (taskError) {
      console.error(`Error fetching task ${task.id}:`, taskError);
      continue;
    }

    console.log(`Task ${task.id} (${task.description}):`);
    console.log(`  Status: ${updatedTask.status}`);
    if (updatedTask.result) {
      console.log(`  Result:`, JSON.stringify(updatedTask.result, null, 2));
    }

    // Check RPRO record
    const { data: rproRecord, error: rproError } = await supabase
      .from('imported_table_rpro')
      .select('ivno, todo, todo_for_amaz')
      .eq('ivno', task.payload.ivno)
      .single();

    if (rproError) {
      console.error(`  Error fetching RPRO record:`, rproError);
    } else {
      console.log(`  RPRO Record (ivno: ${rproRecord.ivno}):`);
      console.log(`    todo: ${rproRecord.todo}`);
      console.log(`    todo_for_amaz: ${rproRecord.todo_for_amaz}`);
    }
    console.log('');
  }

  console.log('=== Test Complete ===');
}

// Run the test
testAmazonSizeTierCheck().catch(err => {
  console.error('Test failed:', err);
  process.exit(1);
});

