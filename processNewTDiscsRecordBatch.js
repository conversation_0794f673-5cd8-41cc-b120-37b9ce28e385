/**
 * Batch processor for new_t_discs_record tasks
 * 
 * This module processes multiple new_t_discs_record tasks together to speed up
 * the queue clearing. Instead of processing one disc at a time, we:
 * 1. Fetch all pending new_t_discs_record tasks
 * 2. Fetch all disc records in a single query
 * 3. Build all child tasks in memory
 * 4. Insert all child tasks in a single batch operation
 */

async function processNewTDiscsRecordBatch(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processNewTDiscsRecordBatch] Processing batch of new_t_discs_record tasks, triggered by task ${task.id}`);

  try {
    const now = new Date();

    // Fetch all pending new_t_discs_record tasks
    console.log(`[processNewTDiscsRecordBatch] Fetching all pending new_t_discs_record tasks...`);

    const { data: pendingTasks, error: tasksError } = await supabase
      .from('t_task_queue')
      .select('id, payload, status')
      .eq('task_type', 'new_t_discs_record')
      .in('status', ['pending', 'processing'])
      .lte('scheduled_at', now.toISOString())
      .order('id', { ascending: true });

    if (tasksError) {
      console.error(`[processNewTDiscsRecordBatch] Error fetching tasks:`, tasksError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to fetch tasks: ${tasksError.message}`,
        error: tasksError.message
      });
      return;
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log(`[processNewTDiscsRecordBatch] No pending new_t_discs_record tasks found`);
      await updateTaskStatus(task.id, 'completed', {
        message: 'No pending tasks to process',
        tasks_processed: 0
      });
      return;
    }

    console.log(`[processNewTDiscsRecordBatch] Found ${pendingTasks.length} pending tasks`);

    // Extract disc IDs from payloads
    const discIds = [];
    const taskMap = new Map(); // Map of discId -> task

    for (const t of pendingTasks) {
      try {
        let payload = t.payload;
        if (typeof payload === 'string') {
          payload = JSON.parse(payload);
        }
        const discId = payload.id;
        if (discId) {
          discIds.push(discId);
          taskMap.set(discId, t);
        }
      } catch (err) {
        console.error(`[processNewTDiscsRecordBatch] Error parsing payload for task ${t.id}:`, err.message);
      }
    }

    if (discIds.length === 0) {
      console.log(`[processNewTDiscsRecordBatch] No valid disc IDs found in tasks`);
      await updateTaskStatus(task.id, 'completed', {
        message: 'No valid disc IDs found',
        tasks_processed: 0
      });
      return;
    }

    console.log(`[processNewTDiscsRecordBatch] Fetching ${discIds.length} disc records...`);

    // Fetch all disc records in a single query
    const { data: discRecords, error: discsError } = await supabase
      .from('t_discs')
      .select('id, image_file_name')
      .in('id', discIds);

    if (discsError) {
      console.error(`[processNewTDiscsRecordBatch] Error fetching disc records:`, discsError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to fetch disc records: ${discsError.message}`,
        error: discsError.message
      });
      return;
    }

    // Build a map of disc records for quick lookup
    const discMap = new Map();
    if (discRecords) {
      for (const disc of discRecords) {
        discMap.set(disc.id, disc);
      }
    }

    // Build all child tasks in memory
    console.log(`[processNewTDiscsRecordBatch] Building child tasks for ${discIds.length} discs...`);

    const allChildTasks = [];
    const results = [];

    for (const discId of discIds) {
      const discRecord = discMap.get(discId);
      const baseTime = now.getTime();

      // Define child tasks with delays
      const childTasks = [
        {
          task_type: 'generate_disc_title_pull_and_handle',
          scheduled_at: new Date(baseTime),
          minutes_delay: 0
        },
        {
          task_type: 'match_disc_to_osl',
          scheduled_at: new Date(baseTime + 60000),
          minutes_delay: 1
        },
        {
          task_type: 'match_disc_to_asins',
          scheduled_at: new Date(baseTime + 180000),
          minutes_delay: 3
        },
        {
          task_type: 'set_disc_carry_cost',
          scheduled_at: new Date(baseTime + 240000),
          minutes_delay: 4
        },
        {
          task_type: 'check_if_disc_is_ready',
          scheduled_at: new Date(baseTime + 360000),
          minutes_delay: 6
        }
      ];

      // Add verify_disc_image task if image exists
      if (discRecord && discRecord.image_file_name && discRecord.image_file_name.trim() !== '') {
        childTasks.push({
          task_type: 'verify_disc_image',
          scheduled_at: new Date(baseTime + 300000),
          minutes_delay: 5
        });
      }

      // Build task records for insertion
      for (const childTask of childTasks) {
        let payload = {
          id: discId,
          operation: 'INSERT'
        };

        if (childTask.task_type === 'verify_disc_image') {
          payload = { id: discId };
        }

        allChildTasks.push({
          task_type: childTask.task_type,
          payload: payload,
          status: 'pending',
          scheduled_at: childTask.scheduled_at.toISOString(),
          created_at: now.toISOString(),
          enqueued_by: `new_t_discs_record_batch_${discId}`
        });
      }

      results.push({
        disc_id: discId,
        child_tasks_count: childTasks.length,
        has_image: discRecord && discRecord.image_file_name && discRecord.image_file_name.trim() !== ''
      });
    }

    console.log(`[processNewTDiscsRecordBatch] Batch inserting ${allChildTasks.length} child tasks...`);

    // Insert all child tasks in a single batch operation
    const { error: insertError } = await supabase
      .from('t_task_queue')
      .insert(allChildTasks);

    if (insertError) {
      console.error(`[processNewTDiscsRecordBatch] Error batch inserting child tasks:`, insertError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to insert child tasks: ${insertError.message}`,
        error: insertError.message,
        tasks_processed: discIds.length,
        child_tasks_attempted: allChildTasks.length
      });
      return;
    }

    console.log(`[processNewTDiscsRecordBatch] Successfully batch inserted ${allChildTasks.length} child tasks for ${discIds.length} discs`);

    // Mark all processed tasks as completed
    const taskIds = Array.from(taskMap.values()).map(t => t.id);
    
    for (const taskId of taskIds) {
      await updateTaskStatus(taskId, 'completed', {
        message: 'Processed as part of batch',
        batch_size: discIds.length
      });
    }

    // Mark the triggering task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully processed ${discIds.length} new disc records and enqueued ${allChildTasks.length} child tasks`,
      tasks_processed: discIds.length,
      child_tasks_enqueued: allChildTasks.length,
      results: results
    });

  } catch (err) {
    const errMsg = `[processNewTDiscsRecordBatch] Exception while processing batch: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, 'Processing new_t_discs_record batch');

    await updateTaskStatus(task.id, 'error', {
      message: 'Failed to process new disc records batch due to an unexpected error.',
      error: err.message
    });
  }
}

export default processNewTDiscsRecordBatch;

