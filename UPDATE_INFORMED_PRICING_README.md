# Update Informed Pricing Task Handler

## Overview

The `update_informed_pricing` task handler updates pricing information for individual listings in Informed Repricer. It retrieves pricing data from the `v_rpro_size_tier_pricing` view and sends it to the Informed API.

## Task Type

**Task Type:** `update_informed_pricing`

## Payload Structure

```json
{
  "listing_sku": "R12345"
}
```

### Payload Fields

- **listing_sku** (required): The SKU of the listing to update in Informed. This should match a `listing_sku` in the `v_rpro_size_tier_pricing` view.

## How It Works

1. **Lookup Pricing Data**: The handler queries the `v_rpro_size_tier_pricing` view using the provided `listing_sku`
2. **Extract Values**: It retrieves three key pricing fields:
   - `map_price`: Minimum Advertised Price
   - `max_price_for_informed`: Maximum price for the listing
   - `cost_for_informed`: Cost basis for pricing calculations
3. **Validate Data**: Ensures all pricing values are present and valid
4. **Build CSV**: Creates a CSV feed with the pricing data in Informed's expected format
5. **Upload to Informed**: Sends the CSV to Informed's `/v1/feed` API endpoint
6. **Update Task Status**: Marks the task as completed or error based on the API response

## View Structure

The handler relies on the `v_rpro_size_tier_pricing` view, which should have the following structure:

```sql
CREATE VIEW v_rpro_size_tier_pricing AS
WITH base AS (
  SELECT
    r.ivno AS sku,
    '9432'::text AS marketplace_id,
    r.ivprcmap AS map_price,
    r.ivprcbtlis AS list_price,
    r.ivprcmsrp AS msrp,
    30581 AS strategy_id,
    t.size_tier,
    t.average_basic_shipping_cost AS tier_shipping,
    t.packaging_cost AS tier_packaging,
    t.total_shipping_cost AS tier_total_shipping_cost,
    r.ivavgcd::numeric(10, 2) AS rpro_carrying_cost,
    r.ivlstcd AS rpro_order_cost,
    COALESCE(r.ivlstcd, 0::numeric)::numeric(10, 2) AS order_cost,
    COALESCE(t.total_shipping_cost, 0::numeric)::numeric(10, 2) AS shipping,
    COALESCE(c.value::numeric, 0::numeric)::numeric(10, 2) AS profit,
    -- ... additional calculations
  FROM imported_table_rpro r
  JOIN tj_rpro_product_to_size_tier j ON j.rpro_id = r.ivno
  JOIN t_product_size_tiers t ON t.id = j.size_tier_id
  LEFT JOIN t_config c ON c.key = 'min_fbm_pball_profit'::text
)
SELECT
  b.sku,
  a.listing_sku,
  b.marketplace_id,
  b.map_price,
  -- ... other fields
  CASE
    WHEN a.max_price IS NULL OR a.max_price = 0::numeric 
    THEN GREATEST(/* calculated max */)
    ELSE GREATEST(a.max_price, /* calculated min */)
  END::numeric(10, 2) AS max_price_for_informed,
  GREATEST(b.map_price, round((b.order_cost + b.shipping + b.profit) / 0.85, 2)) - 0.01 AS cost_for_informed
FROM base b
LEFT JOIN t_amaz_acc_listings_fbm a ON a.rpro_id = b.sku;
```

## Informed API Integration

The handler uses the Informed Repricer API to update pricing:

- **Endpoint**: `https://api.informed.co/v1/feed`
- **Method**: POST
- **Content-Type**: `text/csv`
- **Authentication**: `x-api-key` header with the API key from `INFORMED_API_KEY` environment variable

### CSV Format

The CSV sent to Informed has the following format:

```csv
sku,marketplace_id,cost,currency,max_price,map_price
R12345,9432,15.99,USD,29.99,19.99
```

**Note**: The `currency` field is required when the `cost` field is included.

## Environment Variables

The following environment variable must be set in `.env`:

```
INFORMED_API_KEY=Us79BiL2il9qXJTWPWhfbAgXn5GfYMFFaFiFtiCH
```

## Files

- **processUpdateInformedPricingTask.js**: Main task handler implementation
- **enqueueUpdateInformedPricingTask.js**: Helper script to manually enqueue tasks
- **testUpdateInformedPricing.js**: Test script to verify the handler works correctly

## Usage Examples

### Manually Enqueue a Task

```javascript
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

await supabase
  .from('t_task_queue')
  .insert({
    task_type: 'update_informed_pricing',
    payload: {
      listing_sku: 'R12345'
    },
    status: 'pending',
    scheduled_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    enqueued_by: 'manual'
  });
```

### Using the Helper Script

```bash
node enqueueUpdateInformedPricingTask.js R12345
```

### Enqueue with Delay

```javascript
// Schedule task to run 5 minutes from now
const scheduledAt = new Date();
scheduledAt.setMinutes(scheduledAt.getMinutes() + 5);

await supabase
  .from('t_task_queue')
  .insert({
    task_type: 'update_informed_pricing',
    payload: {
      listing_sku: 'R12345'
    },
    status: 'pending',
    scheduled_at: scheduledAt.toISOString(),
    created_at: new Date().toISOString(),
    enqueued_by: 'scheduled_update'
  });
```

## Error Handling

The handler includes comprehensive error handling for:

- **Missing listing_sku**: Returns error if payload doesn't include listing_sku
- **Listing not found**: Returns error if listing_sku doesn't exist in the view
- **Invalid pricing data**: Returns error if any pricing field is null or invalid
- **API errors**: Returns error if Informed API call fails
- **Network errors**: Returns error if network request fails

All errors are logged to the task's `result` field with detailed error messages.

## Task Status

- **pending**: Task is waiting to be processed
- **processing**: Task is currently being processed
- **completed**: Task completed successfully, pricing updated in Informed
- **error**: Task failed, check the `result` field for error details

## Testing

Run the test script to verify the handler works:

```bash
node testUpdateInformedPricing.js
```

This will:
1. Check if the view has data
2. Enqueue a test task
3. Wait for the task to be processed
4. Display the results

## Integration Points

This task handler can be triggered by:

- **Database triggers**: When pricing data changes in source tables
- **Scheduled jobs**: Periodic pricing updates
- **Manual operations**: Admin interface or scripts
- **Batch operations**: Update multiple listings in sequence

## Related Documentation

- [Informed Repricer API Documentation](http://developer.informed.co)
- [Task Queue Worker Documentation](./README.md)
- [Informed Upload Handler](./informedUploadHandler.js)

## Notes

- The handler updates one listing at a time for granular control
- For bulk updates, consider using the full Informed upload workflow
- The Informed API may take a few minutes to process the feed
- Check the Informed dashboard to verify pricing changes

