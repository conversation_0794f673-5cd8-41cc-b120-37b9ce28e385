import fs from 'fs';
import path from 'path';
import { parse as csvParse } from 'csv-parse/sync';

// Parse the mysterypacks CSV (Stack, Disc description with #ID)
function parseMysteryPacksCsv(filePath) {
  const raw = fs.readFileSync(filePath, 'utf8');
  const records = csvParse(raw, { columns: true, skip_empty_lines: true, trim: true });
  return records.map(r => {
    const stack = parseInt(r.Stack, 10);
    const discStr = r.Disc;
    const m = discStr && discStr.match(/#(\d{6})/);
    const discId = m ? parseInt(m[1], 10) : null;
    return { stack, discStr, discId };
  });
}

// Generic CSV reader -> array of objects keyed by header
function readCsvObjects(csvPath) {
  const raw = fs.readFileSync(csvPath, 'utf8');
  return csvParse(raw, { columns: true, skip_empty_lines: true, trim: true });
}

function formatMoney(n) {
  if (n == null || Number.isNaN(n)) return 'N/A';
  return `$${Number(n).toFixed(2)}`;
}

async function main() {
  const csvPath = path.join('data', 'external data', 'delete', 'mysterypacks.csv');
  if (!fs.existsSync(csvPath)) {
    console.error(`CSV not found at ${csvPath}`);
    process.exit(1);
  }

  // 1) Read the list of discs per stack
  const rows = parseMysteryPacksCsv(csvPath);
  const wantedIds = new Set(rows.map(r => r.discId).filter(Boolean));

  // 2) Read minimal data from local exports instead of hitting DB
  const discsCsv = path.join('data', 'supabase14_t_discs.csv');
  const mpsCsv = path.join('data', 'supabase09_t_mps.csv');
  const plasticsCsv = path.join('data', 'supabase07_t_plastics.csv');

  if (![discsCsv, mpsCsv, plasticsCsv].every(p => fs.existsSync(p))) {
    console.error('One or more reference CSVs are missing (t_discs, t_mps, t_plastics).');
    process.exit(1);
  }

  const discs = readCsvObjects(discsCsv);
  const mps = readCsvObjects(mpsCsv);
  const plastics = readCsvObjects(plasticsCsv);

  // 3) Build lookup maps we need
  const discIdToMpsId = new Map();
  for (const d of discs) {
    const id = Number(d.id);
    if (wantedIds.has(id)) {
      const mpsId = d.mps_id ? Number(d.mps_id) : null;
      discIdToMpsId.set(id, mpsId);
    }
  }

  const mpsIdToInfo = new Map();
  for (const m of mps) {
    const id = Number(m.id);
    const plastic_id = m.plastic_id ? Number(m.plastic_id) : null;
    const overrideRetail = m.val_override_retail_price ? Number(m.val_override_retail_price) : null;
    mpsIdToInfo.set(id, { plastic_id, overrideRetail });
  }

  const plasticIdToRetail = new Map();
  for (const p of plastics) {
    const id = Number(p.id);
    const valRetail = p.val_retail_price ? Number(p.val_retail_price) : null;
    plasticIdToRetail.set(id, valRetail);
  }

  // 4) Compute prices per disc using publish_disc rule:
  // price = mps.val_override_retail_price if present else plastics.val_retail_price
  const priceMap = new Map();
  for (const id of wantedIds) {
    const mpsId = discIdToMpsId.get(id);
    if (!mpsId) {
      priceMap.set(id, null);
      continue;
    }
    const mi = mpsIdToInfo.get(mpsId);
    if (!mi) {
      priceMap.set(id, null);
      continue;
    }
    const price = mi.overrideRetail != null ? mi.overrideRetail : (mi.plastic_id ? plasticIdToRetail.get(mi.plastic_id) : null);
    priceMap.set(id, price != null ? Number(price) : null);
  }

  // 5) Group output by stack and print
  const stacks = new Map();
  for (const r of rows) {
    if (!stacks.has(r.stack)) stacks.set(r.stack, []);
    stacks.get(r.stack).push({ ...r, price: r.discId ? priceMap.get(r.discId) : null });
  }

  for (const [stack, items] of Array.from(stacks.entries()).sort((a, b) => a[0] - b[0])) {
    console.log(`\n=== Stack ${stack} ===`);
    let total = 0;
    for (const it of items) {
      const price = (it.price != null && !Number.isNaN(it.price)) ? it.price : 0;
      total += price;
      console.log(`- ${it.discStr} → ${formatMoney(it.price)}`);
    }
    console.log(`Total for Stack ${stack}: ${formatMoney(total)}`);
  }
}

main().catch(err => {
  console.error('Error:', err.message);
  process.exit(1);
});

