# Security Fixes: Exact Changes Required

## File-by-File Changes

### 1. CREATE NEW FILE: authMiddleware.js

**Location:** Root directory (same level as adminServer.js)

**Content:**
```javascript
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function authMiddleware(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Missing or invalid authorization header' });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    const { data: roles, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .single();

    if (roleError || !roles || roles.role !== 'admin') {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    req.user = user;
    next();
  } catch (err) {
    console.error('[authMiddleware] Error:', err);
    res.status(500).json({ error: 'Authentication error' });
  }
}
```

---

### 2. MODIFY: adminServer.js

**Line 1-5 (Add import):**
```javascript
// ADD THIS LINE:
import { authMiddleware } from './authMiddleware.js';
```

**Line 32 (After express.static):**
```javascript
// CURRENT:
app.use(express.static(__dirname));

// ADD AFTER:
app.use('/api/', authMiddleware);
```

---

### 3. MODIFY: taskQueueWorker.js

**Line 71 (Change SUPABASE_KEY):**
```javascript
// CHANGE FROM:
const supabaseKey = process.env.SUPABASE_KEY;

// CHANGE TO:
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!supabaseKey) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY not set in environment');
}
```

---

### 4. MODIFY: taskQueueWorkerDaemon.js

**Line 11 (Change SUPABASE_KEY):**
```javascript
// CHANGE FROM:
const supabaseKey = process.env.SUPABASE_KEY;

// CHANGE TO:
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!supabaseKey) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY not set in environment');
}
```

---

### 5. MODIFY: enqueueWorkerStatusTask.js

**Line 9 (Change SUPABASE_KEY):**
```javascript
// CHANGE FROM:
const supabaseKey = process.env.SUPABASE_KEY;

// CHANGE TO:
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!supabaseKey) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY not set in environment');
}
```

---

### 6. DELETE: .env-tp.txt

**Action:** Delete entire file (contains hardcoded credentials)

---

### 7. MODIFY: update_task_queue_worker.js

**Lines 5-6 (Remove hardcoded credentials):**
```javascript
// DELETE THESE LINES:
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
const supabaseUrl = 'https://...supabase.co';

// REPLACE WITH:
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseUrl = process.env.SUPABASE_URL;

if (!supabaseKey || !supabaseUrl) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY and SUPABASE_URL must be set');
}
```

---

### 8. MODIFY: enqueue_match_task.js

**Lines 5-6 (Remove hardcoded credentials):**
```javascript
// DELETE THESE LINES:
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
const supabaseUrl = 'https://...supabase.co';

// REPLACE WITH:
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseUrl = process.env.SUPABASE_URL;

if (!supabaseKey || !supabaseUrl) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY and SUPABASE_URL must be set');
}
```

---

### 9. MODIFY: update_task_locking.js

**Lines 4-5 (Remove hardcoded credentials):**
```javascript
// DELETE THESE LINES:
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
const supabaseUrl = 'https://...supabase.co';

// REPLACE WITH:
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseUrl = process.env.SUPABASE_URL;

if (!supabaseKey || !supabaseUrl) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY and SUPABASE_URL must be set');
}
```

---

### 10. MODIFY: informedReportDownloader.cjs

**Lines 46-47 (Remove hardcoded credentials):**
```javascript
// DELETE THESE LINES:
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
const supabaseUrl = 'https://...supabase.co';

// REPLACE WITH:
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseUrl = process.env.SUPABASE_URL;

if (!supabaseKey || !supabaseUrl) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY and SUPABASE_URL must be set');
}
```

---

### 11. MODIFY: admin.html

**Add helper function (in script section, near top):**
```javascript
// ADD THIS FUNCTION:
function getAuthToken() {
  try {
    const session = JSON.parse(localStorage.getItem('supabase.auth.token'));
    return session?.access_token || '';
  } catch (e) {
    console.error('Failed to get auth token:', e);
    return '';
  }
}
```

**Update all fetch calls:**

**FIND:** All instances of `fetch('/api/`

**CHANGE FROM:**
```javascript
fetch('/api/endpoint', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
})
```

**CHANGE TO:**
```javascript
fetch('/api/endpoint', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAuthToken()}`
    },
    body: JSON.stringify(data)
})
```

**Note:** This needs to be done for ALL fetch calls to /api/ endpoints

---

## Environment Variables Required

Ensure your `.env` file contains:

```
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

**IMPORTANT:** Never commit `.env` file to version control!

---

## Verification Commands

After making changes:

```bash
# Check for hardcoded credentials
grep -r "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9" .

# Check for SUPABASE_KEY usage (should be SUPABASE_SERVICE_ROLE_KEY)
grep -r "SUPABASE_KEY" . --include="*.js" --include="*.cjs"

# Check for .env-tp.txt
ls -la .env-tp.txt

# Verify authMiddleware.js exists
ls -la authMiddleware.js
```

---

## Testing After Changes

```bash
# 1. Restart admin server
pm2 restart adminServer

# 2. Restart worker daemon
pm2 restart taskQueueWorkerDaemon

# 3. Check logs for errors
pm2 logs adminServer
pm2 logs taskQueueWorkerDaemon

# 4. Test admin interface
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3001/api/tasks

# 5. Test worker
pm2 logs taskQueueWorkerDaemon | grep -i "error\|permission"
```

---

## Summary of Changes

| File | Change | Priority |
|------|--------|----------|
| authMiddleware.js | CREATE | CRITICAL |
| adminServer.js | ADD IMPORT + MIDDLEWARE | CRITICAL |
| taskQueueWorker.js | CHANGE KEY | HIGH |
| taskQueueWorkerDaemon.js | CHANGE KEY | HIGH |
| enqueueWorkerStatusTask.js | CHANGE KEY | HIGH |
| .env-tp.txt | DELETE | CRITICAL |
| update_task_queue_worker.js | REMOVE CREDENTIALS | CRITICAL |
| enqueue_match_task.js | REMOVE CREDENTIALS | CRITICAL |
| update_task_locking.js | REMOVE CREDENTIALS | CRITICAL |
| informedReportDownloader.cjs | REMOVE CREDENTIALS | CRITICAL |
| admin.html | ADD AUTH HEADERS | HIGH |

---

**Total Files to Modify:** 11
**Total Files to Create:** 1
**Total Files to Delete:** 1
**Estimated Time:** 2-3 hours

