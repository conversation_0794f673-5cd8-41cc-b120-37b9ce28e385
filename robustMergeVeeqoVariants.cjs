const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const nodemailer = require('nodemailer');
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔄 ROBUST VEEQO VARIANT MERGER');
console.log('='.repeat(70));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // 204 No Content is a success response with no body
    if (response.status === 204 || method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to find sellable by SKU
async function findSellableBySku(sku) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products?query=${encodeURIComponent(sku)}`);
  
  if (!result.success) {
    return null;
  }
  
  for (const product of result.data) {
    if (product.sellables) {
      const sellable = product.sellables.find(s => s.sku_code === sku);
      if (sellable) {
        let stock = 0;
        if (sellable.stock_entries && sellable.stock_entries.length > 0) {
          stock = sellable.stock_entries.reduce((sum, entry) => sum + (entry.physical_stock_level || 0), 0);
        }
        return { product, sellable, stock };
      }
    }
  }
  
  return null;
}

// Function to get product details
async function getProductDetails(productId) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!result.success) {
    return null;
  }
  
  return result.data;
}

// Function to update channel sellable
async function updateChannelSellable(channelSellableId, newSellableId) {
  const data = {
    channel_sellable: {
      sellable_id: newSellableId
    }
  };
  
  const result = await makeVeeqoRequest(
    `https://api.veeqo.com/channel_sellables/${channelSellableId}`,
    'PUT',
    data
  );
  
  return result;
}

// Function to update stock
async function updateStockEntry(sellableId, quantity) {
  const warehouseId = 99881;
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/sellables/${sellableId}/stock_entries`);
  
  if (!result.success) {
    return { success: false, error: result.error };
  }
  
  const stockEntry = result.data.find(entry => entry.warehouse_id === warehouseId);
  
  if (!stockEntry) {
    return { success: false, error: 'Stock entry not found for warehouse 99881' };
  }
  
  const updateResult = await makeVeeqoRequest(
    `https://api.veeqo.com/stock_entries/${stockEntry.id}`,
    'PUT',
    { stock_entry: { physical_stock_level: quantity } }
  );
  
  return updateResult;
}

// Function to delete product
async function deleteProduct(productId) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`, 'DELETE');
  return result;
}

// Function to find all products that have channel sellables with a specific remote_sku
async function findProductsWithRemoteSku(remoteSku) {
  console.log(`\n🔍 Searching all products for channel sellables with remote_sku=${remoteSku}...`);
  
  const productsWithSku = [];
  let page = 1;
  const pageSize = 100;
  
  // Search through products (limit to reasonable number of pages)
  while (page <= 50) {
    const result = await makeVeeqoRequest(`https://api.veeqo.com/products?page=${page}&page_size=${pageSize}`);
    
    if (!result.success) {
      break;
    }
    
    const products = result.data;
    
    if (!products || products.length === 0) {
      break;
    }
    
    for (const product of products) {
      if (product.channel_products) {
        for (const channelProduct of product.channel_products) {
          if (channelProduct.channel_sellables) {
            const hasRemoteSku = channelProduct.channel_sellables.some(cs => cs.remote_sku === remoteSku);
            if (hasRemoteSku) {
              productsWithSku.push(product.id);
              break; // Found it in this product, move to next product
            }
          }
        }
      }
    }
    
    if (products.length < pageSize) {
      break;
    }
    
    page++;
  }
  
  const uniqueProducts = [...new Set(productsWithSku)];
  console.log(`   Found ${uniqueProducts.length} product(s) with channel sellables having remote_sku=${remoteSku}`);

  return uniqueProducts;
}

// Email notification function
async function sendEmailNotification(mergeReport) {
  try {
    // Check if email is configured
    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
      console.log('\n📧 Email not configured - skipping email notification');
      console.log('   Configure EMAIL_USER and EMAIL_PASS in .env to receive email reports');
      return { success: false, reason: 'not_configured' };
    }

    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });

    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });

    let emailBody;
    let subject;

    // Check if this is a "no merge needed" case
    if (mergeReport.noMergeNeeded) {
      subject = `✅ Veeqo Merge Check: ${mergeReport.oldSku} - No Merge Needed`;
      emailBody = `
<h2>✅ Veeqo Variant Merge Check Complete</h2>
<p><strong>Completed:</strong> ${currentDate}</p>

<div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;">
  <h3>ℹ️ No Merge Needed</h3>
  <p>The FM variant <strong>${mergeReport.oldSku}</strong> was not found in Veeqo.</p>
  <p>This means there is no duplicate to merge - everything is already correct!</p>
</div>

<h3>📦 Target Variant Information</h3>
<table style="border-collapse: collapse; width: 100%;">
  <tr style="background: #e8f5e8;">
    <td style="padding: 8px; border: 1px solid #ddd;"><strong>SKU:</strong></td>
    <td style="padding: 8px; border: 1px solid #ddd;">${mergeReport.newSku}</td>
  </tr>
  <tr>
    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Product ID:</strong></td>
    <td style="padding: 8px; border: 1px solid #ddd;">${mergeReport.newProductId}</td>
  </tr>
  <tr style="background: #e8f5e8;">
    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Sellable ID:</strong></td>
    <td style="padding: 8px; border: 1px solid #ddd;">${mergeReport.newSellableId}</td>
  </tr>
  <tr>
    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Stock:</strong></td>
    <td style="padding: 8px; border: 1px solid #ddd;">${mergeReport.stock}</td>
  </tr>
</table>

<h3>✅ Final Status</h3>
<ul>
  <li>FM variant ${mergeReport.oldSku} does not exist</li>
  <li>R variant ${mergeReport.newSku} exists and is correct</li>
  <li>No action needed - task completed successfully!</li>
</ul>

<p><small>This report was automatically generated by the Veeqo Variant Merge system.</small></p>
`;
    } else {
      // Normal merge report
      subject = `🔄 Veeqo Merge Complete: ${mergeReport.oldSku} → ${mergeReport.newSku} (${mergeReport.totalUpdates} updates)`;
      emailBody = `
<h2>🔄 Veeqo Variant Merge Complete</h2>
<p><strong>Completed:</strong> ${currentDate}</p>

<h3>📦 Variant Information</h3>
<table style="border-collapse: collapse; width: 100%;">
  <tr style="background: #f0f0f0;">
    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Old SKU:</strong></td>
    <td style="padding: 8px; border: 1px solid #ddd;">${mergeReport.oldSku}</td>
  </tr>
  <tr>
    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Old Product ID:</strong></td>
    <td style="padding: 8px; border: 1px solid #ddd;">${mergeReport.oldProductId}</td>
  </tr>
  <tr style="background: #f0f0f0;">
    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Old Sellable ID:</strong></td>
    <td style="padding: 8px; border: 1px solid #ddd;">${mergeReport.oldSellableId}</td>
  </tr>
  <tr>
    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Status:</strong></td>
    <td style="padding: 8px; border: 1px solid #ddd;">${mergeReport.deleted ? '✅ DELETED' : '⚠️ KEPT (not deleted)'}</td>
  </tr>
</table>

<table style="border-collapse: collapse; width: 100%; margin-top: 10px;">
  <tr style="background: #e8f5e8;">
    <td style="padding: 8px; border: 1px solid #ddd;"><strong>New SKU:</strong></td>
    <td style="padding: 8px; border: 1px solid #ddd;">${mergeReport.newSku}</td>
  </tr>
  <tr>
    <td style="padding: 8px; border: 1px solid #ddd;"><strong>New Product ID:</strong></td>
    <td style="padding: 8px; border: 1px solid #ddd;">${mergeReport.newProductId}</td>
  </tr>
  <tr style="background: #e8f5e8;">
    <td style="padding: 8px; border: 1px solid #ddd;"><strong>New Sellable ID:</strong></td>
    <td style="padding: 8px; border: 1px solid #ddd;">${mergeReport.newSellableId}</td>
  </tr>
  <tr>
    <td style="padding: 8px; border: 1px solid #ddd;"><strong>Stock:</strong></td>
    <td style="padding: 8px; border: 1px solid #ddd;">${mergeReport.stock}</td>
  </tr>
</table>

<h3>📊 Migration Summary</h3>
<div style="background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 10px 0;">
  <p><strong>Channel sellables migrated:</strong> ${mergeReport.channelSellablesMoved}</p>
  ${mergeReport.finalCleanup > 0 ? `<p><strong>Additional cleanup updates:</strong> ${mergeReport.finalCleanup}</p>` : ''}
  <p><strong>Grand total updates:</strong> ${mergeReport.totalUpdates}</p>
</div>

<h3>🔧 Additional Fixes</h3>
<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;">
  <p><strong>Orphaned/mismatched sellables fixed:</strong> ${mergeReport.mismatchesFixed}</p>
  ${mergeReport.mismatchesFixed > 0 ? '<p><em>Note: These were other SKUs found in the target product that were incorrectly linked and have been moved back to their correct products.</em></p>' : ''}
</div>

<h3>✅ Final Status</h3>
<ul>
  <li>All ${mergeReport.oldSku} channel sellables now point to ${mergeReport.newSku}</li>
  <li>Old variant ${mergeReport.deleted ? 'deleted' : 'preserved'}</li>
  <li>Merge completed successfully!</li>
</ul>

<p><small>This report was automatically generated by the Veeqo Variant Merge system.</small></p>
`;
    }

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: '<EMAIL>',
      subject: subject,
      html: emailBody
    };

    await transporter.sendMail(mailOptions);
    console.log('\n✅ Email notification sent successfully');
    return { success: true };

  } catch (error) {
    console.error(`\n❌ Failed to send email: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Main robust merge function
async function robustMergeVariants(oldSku, newSku, deleteOld = false) {
  console.log(`\n🔄 Robust Merge: ${oldSku} → ${newSku}`);
  console.log('─'.repeat(70));
  
  // STEP 1: Find both variants
  console.log(`\n📋 STEP 1: Finding both variants`);
  console.log('─'.repeat(70));
  
  console.log(`🔍 Searching for SKU: ${oldSku}`);
  const oldVariant = await findSellableBySku(oldSku);
  if (!oldVariant) {
    console.log(`   ℹ️  Old SKU not found: ${oldSku}`);
    console.log(`   ✅ No FM variant exists - nothing to merge!`);

    // Still verify the new SKU exists
    console.log(`🔍 Searching for SKU: ${newSku}`);
    const newVariant = await findSellableBySku(newSku);
    if (!newVariant) {
      console.error(`   ❌ Could not find new SKU: ${newSku}`);
      return false;
    }
    console.log(`   ✅ Found: Product ID ${newVariant.product.id}, Sellable ID ${newVariant.sellable.id}`);
    console.log(`      Title: ${newVariant.product.title}`);
    console.log(`      Stock: ${newVariant.stock}`);

    // Send email notification that no merge was needed
    const noMergeReport = {
      oldSku,
      newSku,
      noMergeNeeded: true,
      newProductId: newVariant.product.id,
      newSellableId: newVariant.sellable.id,
      stock: newVariant.stock
    };

    await sendEmailNotification(noMergeReport);

    console.log(`\n✅ Task complete - no merge needed (FM variant does not exist)`);
    return true;
  }
  console.log(`   ✅ Found: Product ID ${oldVariant.product.id}, Sellable ID ${oldVariant.sellable.id}`);
  console.log(`      Title: ${oldVariant.product.title}`);
  console.log(`      Stock: ${oldVariant.stock}`);

  console.log(`🔍 Searching for SKU: ${newSku}`);
  const newVariant = await findSellableBySku(newSku);
  if (!newVariant) {
    console.error(`   ❌ CRITICAL ERROR: Could not find new SKU: ${newSku}`);
    console.error(`   ❌ FM variant ${oldSku} EXISTS but R variant ${newSku} does NOT exist!`);
    console.error(`   ❌ This requires manual intervention - cannot proceed with merge.`);
    throw new Error(`FM variant ${oldSku} exists but R variant ${newSku} not found - manual intervention required`);
  }
  console.log(`   ✅ Found: Product ID ${newVariant.product.id}, Sellable ID ${newVariant.sellable.id}`);
  console.log(`   Title: ${newVariant.product.title}`);
  console.log(`      Stock: ${newVariant.stock}`);
  
  // STEP 2: Get channel sellables from old variant's product FIRST
  console.log(`\n📋 STEP 2: Getting channel sellables from ${oldSku} product`);
  console.log('─'.repeat(70));

  const oldProduct = await getProductDetails(oldVariant.product.id);
  const channelSellablesToMove = [];

  if (oldProduct && oldProduct.channel_products) {
    for (const channelProduct of oldProduct.channel_products) {
      if (channelProduct.channel_sellables) {
        for (const channelSellable of channelProduct.channel_sellables) {
          if (channelSellable.remote_sku === oldSku) {
            channelSellablesToMove.push({
              productId: oldVariant.product.id,
              channelProduct,
              channelSellable
            });
          }
        }
      }
    }
  }

  console.log(`   Found ${channelSellablesToMove.length} channel sellable(s) with remote_sku=${oldSku} in old product`);

  // STEP 3: Find ALL OTHER products that have channel sellables with oldSku
  console.log(`\n📋 STEP 3: Finding other products with ${oldSku} channel sellables`);
  console.log('─'.repeat(70));

  const productsWithOldSku = await findProductsWithRemoteSku(oldSku);

  // Remove the old product from the list since we already processed it
  const otherProducts = productsWithOldSku.filter(id => id !== oldVariant.product.id);
  console.log(`   Found ${otherProducts.length} other product(s) with ${oldSku} channel sellables`);

  // Get channel sellables from other products
  for (const productId of otherProducts) {
    console.log(`\n📦 Checking product ${productId}...`);
    const product = await getProductDetails(productId);

    if (!product) {
      console.log(`   ⚠️  Could not fetch product details`);
      continue;
    }

    console.log(`   Title: ${product.title}`);

    if (product.channel_products) {
      for (const channelProduct of product.channel_products) {
        if (channelProduct.channel_sellables) {
          for (const channelSellable of channelProduct.channel_sellables) {
            if (channelSellable.remote_sku === oldSku) {
              channelSellablesToMove.push({
                productId: product.id,
                channelProduct,
                channelSellable
              });
            }
          }
        }
      }
    }
  }

  console.log(`\n   📊 Total channel sellables to move: ${channelSellablesToMove.length}`);

  // STEP 4: Update all channel sellables with oldSku to point to newSku
  console.log(`\n📋 STEP 4: Updating all ${oldSku} channel sellables to ${newSku}`);
  console.log('─'.repeat(70));

  let totalUpdated = 0;

  for (const item of channelSellablesToMove) {
    const { productId, channelProduct, channelSellable } = item;

    console.log(`\n   📺 Channel Product: ${channelProduct.id} - ${channelProduct.remote_title}`);
    console.log(`      Product: ${productId}`);
    console.log(`      Channel: ${channelProduct.channel?.name || 'Unknown'}`);
    console.log(`      Channel Sellable: ${channelSellable.id}`);
    console.log(`      Remote SKU: ${channelSellable.remote_sku}`);
    console.log(`      Updating: ${channelSellable.sellable_id} → ${newVariant.sellable.id}`);

    const result = await updateChannelSellable(
      channelSellable.id,
      newVariant.sellable.id
    );

    if (result.success) {
      console.log(`      ✅ Updated successfully`);
      totalUpdated++;
    } else {
      console.log(`      ❌ Failed: ${result.error}`);
    }
  }

  console.log(`\n   📊 Total channel sellables updated: ${totalUpdated}`);
  
  // STEP 5: Verify and fix mismatches in the new variant's product
  console.log(`\n📋 STEP 5: Verifying ${newSku} product for mismatches`);
  console.log('─'.repeat(70));

  const newProduct = await getProductDetails(newVariant.product.id);
  let fixedCount = 0;

  if (newProduct) {
    const skuCache = new Map();
    
    if (newProduct.channel_products) {
      for (const channelProduct of newProduct.channel_products) {
        if (channelProduct.channel_sellables) {
          for (const channelSellable of channelProduct.channel_sellables) {
            const remoteSku = channelSellable.remote_sku;
            const currentSellableId = channelSellable.sellable_id;
            
            // Check if this sellable belongs to this product
            const productSellable = newProduct.sellables?.find(s => s.id === currentSellableId);
            
            // If sellable doesn't exist in this product, or SKU doesn't match
            if (!productSellable || productSellable.sku_code !== remoteSku) {
              console.log(`\n   ⚠️  Mismatch found:`);
              console.log(`      Channel Product: ${channelProduct.id} - ${channelProduct.remote_title}`);
              console.log(`      Channel Sellable: ${channelSellable.id}`);
              console.log(`      Remote SKU: ${remoteSku}`);
              console.log(`      Current Sellable: ${currentSellableId} ${productSellable ? `(${productSellable.sku_code})` : '(orphaned)'}`);
              
              // Try to find the correct sellable
              let correctSellable = skuCache.get(remoteSku);
              
              if (!correctSellable) {
                console.log(`      🔍 Looking up correct sellable for ${remoteSku}...`);
                correctSellable = await findSellableBySku(remoteSku);
                if (correctSellable) {
                  skuCache.set(remoteSku, correctSellable);
                }
              }
              
              if (correctSellable) {
                console.log(`      ✅ Found: Sellable ${correctSellable.sellable.id} in product ${correctSellable.product.id}`);
                console.log(`      🔄 Updating to correct sellable...`);
                
                const result = await updateChannelSellable(
                  channelSellable.id,
                  correctSellable.sellable.id
                );
                
                if (result.success) {
                  console.log(`      ✅ Fixed successfully`);
                  fixedCount++;
                } else {
                  console.log(`      ❌ Failed: ${result.error}`);
                }
              } else {
                console.log(`      ⚠️  Could not find sellable for ${remoteSku} - may have been deleted`);
              }
            }
          }
        }
      }
    }

    console.log(`\n   📊 Mismatches fixed: ${fixedCount}`);
  } else {
    console.log(`\n   ⚠️  Could not fetch new product for verification`);
  }
  
  // STEP 6: Delete old variant if requested
  if (deleteOld) {
    console.log(`\n📋 STEP 6: Deleting old variant ${oldSku}`);
    console.log('─'.repeat(70));

    console.log(`\n   📦 Setting stock to 0 for sellable ${oldVariant.sellable.id}`);
    const stockResult = await updateStockEntry(oldVariant.sellable.id, 0);

    if (stockResult.success) {
      console.log(`      ✅ Stock set to 0`);
    } else {
      console.log(`      ⚠️  Could not set stock to 0: ${stockResult.error}`);
    }

    console.log(`\n   🗑️  Deleting product ${oldVariant.product.id}`);
    const deleteResult = await deleteProduct(oldVariant.product.id);

    if (deleteResult.success) {
      console.log(`      ✅ Product deleted successfully`);
    } else {
      console.log(`      ❌ Failed to delete product: ${deleteResult.error}`);
      return false;
    }
  } else {
    console.log(`\n📋 STEP 6: Skipping deletion (use --delete flag to delete old variant)`);
    console.log('─'.repeat(70));
  }

  // STEP 7: Final verification - update any remaining orphaned channel sellables with old SKU
  console.log(`\n📋 STEP 7: Final verification and cleanup`);
  console.log('─'.repeat(70));

  console.log(`\n   🔍 Checking for any remaining ${oldSku} channel sellables...`);
  const finalProduct = await getProductDetails(newVariant.product.id);

  let finalUpdated = 0;

  if (finalProduct && finalProduct.channel_products) {
    for (const channelProduct of finalProduct.channel_products) {
      if (channelProduct.channel_sellables) {
        for (const channelSellable of channelProduct.channel_sellables) {
          if (channelSellable.remote_sku === oldSku) {
            // Check if it's pointing to the correct sellable
            if (channelSellable.sellable_id !== newVariant.sellable.id) {
              console.log(`\n   ⚠️  Found orphaned channel sellable:`);
              console.log(`      Channel Product: ${channelProduct.id} - ${channelProduct.remote_title}`);
              console.log(`      Channel Sellable: ${channelSellable.id}`);
              console.log(`      Remote SKU: ${channelSellable.remote_sku}`);
              console.log(`      Current Sellable: ${channelSellable.sellable_id}`);
              console.log(`      Updating to: ${newVariant.sellable.id}`);

              const result = await updateChannelSellable(
                channelSellable.id,
                newVariant.sellable.id
              );

              if (result.success) {
                console.log(`      ✅ Updated successfully`);
                finalUpdated++;
              } else {
                console.log(`      ❌ Failed: ${result.error}`);
              }
            }
          }
        }
      }
    }
  }

  if (finalUpdated > 0) {
    console.log(`\n   📊 Final cleanup: ${finalUpdated} additional channel sellable(s) updated`);
  } else {
    console.log(`\n   ✅ No additional cleanup needed`);
  }

  // Generate comprehensive report
  console.log(`\n${'='.repeat(70)}`);
  console.log(`📊 MERGE COMPLETE - COMPREHENSIVE REPORT`);
  console.log(`${'='.repeat(70)}`);

  console.log(`\n📦 VARIANT INFORMATION:`);
  console.log(`   Old SKU: ${oldSku}`);
  console.log(`   Old Product ID: ${oldVariant.product.id}`);
  console.log(`   Old Sellable ID: ${oldVariant.sellable.id}`);
  console.log(`   Status: ${deleteOld ? '✅ DELETED' : '⚠️  KEPT (not deleted)'}`);

  console.log(`\n   New SKU: ${newSku}`);
  console.log(`   New Product ID: ${newVariant.product.id}`);
  console.log(`   New Sellable ID: ${newVariant.sellable.id}`);
  console.log(`   Stock: ${newVariant.stock}`);

  console.log(`\n📊 CHANNEL SELLABLES MIGRATED:`);
  console.log(`   Total channel sellables moved: ${totalUpdated}`);
  if (finalUpdated > 0) {
    console.log(`   Additional cleanup updates: ${finalUpdated}`);
  }
  console.log(`   Grand total updates: ${totalUpdated + finalUpdated}`);

  console.log(`\n🔧 MISMATCHES FIXED:`);
  console.log(`   Orphaned/mismatched sellables fixed: ${fixedCount}`);

  console.log(`\n✅ FINAL STATUS:`);
  console.log(`   All ${oldSku} channel sellables now point to ${newSku}`);
  console.log(`   Old variant ${deleteOld ? 'deleted' : 'preserved'}`);
  console.log(`   Merge completed successfully!`);

  console.log(`\n${'='.repeat(70)}`);

  // Send email notification
  const mergeReport = {
    oldSku,
    oldProductId: oldVariant.product.id,
    oldSellableId: oldVariant.sellable.id,
    deleted: deleteOld,
    newSku,
    newProductId: newVariant.product.id,
    newSellableId: newVariant.sellable.id,
    stock: newVariant.stock,
    channelSellablesMoved: totalUpdated,
    finalCleanup: finalUpdated,
    totalUpdates: totalUpdated + finalUpdated,
    mismatchesFixed: fixedCount
  };

  await sendEmailNotification(mergeReport);

  return true;
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 2) {
  console.log(`
Usage: node robustMergeVeeqoVariants.cjs <old-sku> <new-sku> [--delete]

Arguments:
  old-sku   - The SKU of the variant to merge from (e.g., FM6872)
  new-sku   - The SKU of the variant to merge to (e.g., R06872)
  --delete  - Optional flag to delete the old variant after merging

Example:
  node robustMergeVeeqoVariants.cjs FM6872 R06872 --delete

This will:
  1. Find both variants
  2. Search ALL products for channel sellables with the old SKU
  3. Update all found channel sellables to point to the new variant
  4. Verify and fix any mismatches in the new variant's product
  5. Delete the old variant (if --delete flag is used)
`);
  process.exit(1);
}

const oldSku = args[0];
const newSku = args[1];
const deleteOld = args.includes('--delete');

robustMergeVariants(oldSku, newSku, deleteOld)
  .then(success => {
    if (success) {
      console.log(`\n✅ All done!`);
      process.exit(0);
    } else {
      console.log(`\n❌ Merge failed`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

