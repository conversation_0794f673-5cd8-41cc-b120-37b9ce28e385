-- Batch functions for getting order costs and shipping multipliers
-- These functions are used by the processSetDiscCarryCostBatch task handler
-- to efficiently fetch data for multiple discs at once

-- Function to get order costs for multiple MPS IDs
CREATE OR REPLACE FUNCTION get_disc_order_costs_batch(
    mps_ids INTEGER[]
)
RETURNS TABLE (
    mps_id INTEGER,
    order_cost NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT m.id, COALESCE(m.val_override_order_cost, p.val_order_cost) AS order_cost
    FROM public.t_mps m
    JOIN public.t_plastics p ON m.plastic_id = p.id
    WHERE m.id = ANY(mps_ids);
END;
$$ LANGUAGE plpgsql;

-- Function to get shipping multipliers for multiple shipment IDs
CREATE OR REPLACE FUNCTION get_shipment_multipliers_batch(
    shipment_ids INTEGER[]
)
RETURNS TABLE (
    shipment_id SMALLINT,
    shipping_multiplier NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT s.id, (i.total_amount / NULLIF(i.subtotal, 0)) AS shipping_multiplier
    FROM public.t_shipments s
    JOIN public.t_invoices i ON s.invoice_id = i.id
    WHERE s.id = ANY(shipment_ids);
END;
$$ LANGUAGE plpgsql;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Batch carry cost helper functions created.';
END $$;

