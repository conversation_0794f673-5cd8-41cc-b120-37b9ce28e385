const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔍 SEARCH CHANNEL PRODUCTS BY SKU');
console.log('='.repeat(70));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // 204 No Content is a success response with no body
    if (response.status === 204 || method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to search channel products by query
async function searchChannelProducts(query) {
  console.log(`\n🔍 Searching channel products with query: ${query}`);
  console.log('─'.repeat(70));
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/channel_products?query=${encodeURIComponent(query)}`);

  if (!result.success) {
    console.error(`   ❌ Error: ${result.error}`);
    return [];
  }

  let channelProducts = result.data;

  // Handle if response is an object with items property
  if (!Array.isArray(channelProducts)) {
    if (channelProducts && channelProducts.items) {
      channelProducts = channelProducts.items;
      console.log(`   Total count: ${channelProducts.total_count || channelProducts.length}`);
    } else if (channelProducts && channelProducts.channel_products) {
      channelProducts = channelProducts.channel_products;
    } else {
      console.error(`   ❌ Unexpected response format`);
      console.log(`   Response keys: ${Object.keys(channelProducts || {}).join(', ')}`);
      return [];
    }
  }

  console.log(`   Found ${channelProducts.length} channel product(s)`);

  if (channelProducts.length === 0) {
    return [];
  }
  
  // Display each channel product
  for (const cp of channelProducts) {
    console.log(`\n${'─'.repeat(70)}`);
    console.log(`📺 Channel Product ID: ${cp.id}`);
    console.log(`   Title: ${cp.remote_title || 'N/A'}`);
    console.log(`   Remote ID: ${cp.remote_id || 'N/A'}`);
    console.log(`   Channel: ${cp.channel?.name || 'Unknown'}`);
    console.log(`   Status: ${cp.status || 'Unknown'}`);
    console.log(`   Product ID: ${cp.product_id || 'UNLINKED'}`);
    
    if (cp.channel_sellables && cp.channel_sellables.length > 0) {
      console.log(`\n   Channel Sellables:`);
      for (const cs of cp.channel_sellables) {
        console.log(`      - Channel Sellable ID: ${cs.id}`);
        console.log(`        Remote SKU: ${cs.remote_sku}`);
        console.log(`        Sellable ID: ${cs.sellable_id}`);
        
        // If there's a product, check if the sellable exists
        if (cp.product_id) {
          const productResult = await makeVeeqoRequest(`https://api.veeqo.com/products/${cp.product_id}`);
          if (productResult.success) {
            const product = productResult.data;
            const sellable = product.sellables?.find(s => s.id === cs.sellable_id);
            if (sellable) {
              console.log(`        Sellable SKU: ${sellable.sku_code} ${sellable.sku_code === cs.remote_sku ? '✅' : '⚠️  MISMATCH'}`);
            } else {
              console.log(`        Sellable: ❌ ORPHANED (doesn't exist in product)`);
            }
          }
        } else {
          console.log(`        Product: ⚠️  UNLINKED`);
        }
      }
    } else {
      console.log(`   No channel sellables`);
    }
  }
  
  return channelProducts;
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 1) {
  console.log(`
Usage: node searchChannelProducts.cjs <query>

Arguments:
  query - The search query (SKU, title, etc.)

Example:
  node searchChannelProducts.cjs FM6872

This will search for channel products using Veeqo's query parameter.
`);
  process.exit(1);
}

const query = args[0];

searchChannelProducts(query)
  .then(results => {
    console.log(`\n${'='.repeat(70)}`);
    console.log(`✅ Search complete! Found ${results.length} channel product(s)`);
    console.log(`${'='.repeat(70)}`);
    process.exit(0);
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

