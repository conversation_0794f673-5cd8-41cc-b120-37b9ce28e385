-- Fix RLS policies on gt_todo table to allow authenticated users full access

-- First, check if <PERSON><PERSON> is enabled
ALTER TABLE public.gt_todo ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow authenticated users to select gt_todo" ON public.gt_todo;
DROP POLICY IF EXISTS "Allow authenticated users to insert gt_todo" ON public.gt_todo;
DROP POLICY IF EXISTS "Allow authenticated users to update gt_todo" ON public.gt_todo;
DROP POLICY IF EXISTS "Allow authenticated users to delete gt_todo" ON public.gt_todo;
DROP POLICY IF EXISTS "gt_todo_select_policy" ON public.gt_todo;
DROP POLICY IF EXISTS "gt_todo_insert_policy" ON public.gt_todo;
DROP POLICY IF EXISTS "gt_todo_update_policy" ON public.gt_todo;
DROP POLICY IF EXISTS "gt_todo_delete_policy" ON public.gt_todo;

-- Create new permissive policies that allow all authenticated users full access
CREATE POLICY "Allow authenticated users to select gt_todo" 
ON public.gt_todo FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Allow authenticated users to insert gt_todo" 
ON public.gt_todo FOR INSERT 
TO authenticated 
WITH CHECK (true);

CREATE POLICY "Allow authenticated users to update gt_todo" 
ON public.gt_todo FOR UPDATE 
TO authenticated 
USING (true) 
WITH CHECK (true);

CREATE POLICY "Allow authenticated users to delete gt_todo" 
ON public.gt_todo FOR DELETE 
TO authenticated 
USING (true);

-- Grant necessary permissions to authenticated role
GRANT SELECT, INSERT, UPDATE, DELETE ON public.gt_todo TO authenticated;

-- Confirmation
DO $$
BEGIN
    RAISE NOTICE 'RLS policies for gt_todo table have been updated to allow authenticated users full access.';
END $$;

