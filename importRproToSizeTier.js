// importRproToSizeTier.js
// Import rpro_id_legacy_id.txt (TSV) into tj_rpro_product_to_size_tier
// Source: C:\Users\<USER>\OneDrive\Desktop\Delete\rpro_id_legacy_id.txt
//
// Mapping:
//   rpro_id -> tj_rpro_product_to_size_tier.rpro_id (direct)
//   legacy_id -> lookup in t_product_size_tiers.legacy_id to get id -> tj_rpro_product_to_size_tier.size_tier_id

import fs from 'fs';
import path from 'path';
import Papa from 'papaparse';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const FILE_PATH = 'C:\\Users\\<USER>\\OneDrive\\Desktop\\Delete\\rpro_id_legacy_id.txt';
const CHUNK_SIZE = 500;

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

function toIntOrNull(v) {
  if (v === undefined || v === null || v === '') return null;
  const s = String(v).trim();
  const n = Number(s);
  return Number.isFinite(n) ? Math.trunc(n) : null;
}

function parseTsv(filePath) {
  const raw = fs.readFileSync(filePath, 'utf-8');
  const { data, errors } = Papa.parse(raw, {
    header: true,
    skipEmptyLines: true,
    delimiter: '\t',
    dynamicTyping: false,
  });
  if (errors && errors.length) {
    console.warn('[importRproToSizeTier] TSV parse warnings (first 5):', errors.slice(0, 5));
  }
  return data;
}

function chunk(arr, size) {
  const out = [];
  for (let i = 0; i < arr.length; i += size) out.push(arr.slice(i, i + size));
  return out;
}

async function main() {
  console.log('[importRproToSizeTier] Starting import...');
  
  // Check if file exists
  if (!fs.existsSync(FILE_PATH)) {
    console.error(`❌ File not found: ${FILE_PATH}`);
    process.exit(1);
  }

  // Parse TSV
  console.log('[importRproToSizeTier] Parsing TSV file...');
  const rows = parseTsv(FILE_PATH);
  console.log(`[importRproToSizeTier] Parsed ${rows.length} rows from TSV`);

  if (rows.length === 0) {
    console.log('[importRproToSizeTier] No data to import');
    return;
  }

  // Load t_product_size_tiers lookup
  console.log('[importRproToSizeTier] Loading t_product_size_tiers lookup...');
  const { data: sizeTiers, error: sizeTiersError } = await supabase
    .from('t_product_size_tiers')
    .select('id, legacy_id');

  if (sizeTiersError) {
    console.error('❌ Error loading t_product_size_tiers:', sizeTiersError);
    process.exit(1);
  }

  // Create lookup map: legacy_id -> id
  const legacyIdToSizeTierId = {};
  sizeTiers.forEach(tier => {
    if (tier.legacy_id !== null) {
      legacyIdToSizeTierId[tier.legacy_id] = tier.id;
    }
  });

  console.log(`[importRproToSizeTier] Loaded ${Object.keys(legacyIdToSizeTierId).length} size tier mappings`);

  // Load valid rpro_id values from imported_table_rpro
  console.log('[importRproToSizeTier] Loading valid rpro_id values from imported_table_rpro...');
  const validRproIds = new Set();
  let offset = 0;
  const chunkSize = 1000;

  while (true) {
    const { data: rproChunk, error: rproError } = await supabase
      .from('imported_table_rpro')
      .select('ivno')
      .range(offset, offset + chunkSize - 1);

    if (rproError) {
      console.error('❌ Error loading imported_table_rpro:', rproError);
      process.exit(1);
    }

    if (!rproChunk || rproChunk.length === 0) {
      break;
    }

    rproChunk.forEach(row => {
      if (row.ivno !== null) {
        validRproIds.add(row.ivno);
      }
    });

    offset += chunkSize;

    if (rproChunk.length < chunkSize) {
      break;
    }
  }

  console.log(`[importRproToSizeTier] Loaded ${validRproIds.size} valid rpro_id values`);

  // Transform rows
  const recordsToInsert = [];
  const errors = [];

  for (let i = 0; i < rows.length; i++) {
    const row = rows[i];
    const rpro_id = toIntOrNull(row.rpro_id);
    const legacy_id = toIntOrNull(row.legacy_id);

    if (rpro_id === null) {
      errors.push({ row: i + 2, reason: 'rpro_id is null or invalid', data: row });
      continue;
    }

    // Check if rpro_id is within valid integer range
    if (rpro_id > 2147483647 || rpro_id < -2147483648) {
      errors.push({ row: i + 2, reason: `rpro_id ${rpro_id} is out of range for integer type`, data: row });
      continue;
    }

    // Check if rpro_id exists in imported_table_rpro
    if (!validRproIds.has(rpro_id)) {
      errors.push({ row: i + 2, reason: `rpro_id ${rpro_id} not found in imported_table_rpro`, data: row });
      continue;
    }

    if (legacy_id === null) {
      errors.push({ row: i + 2, reason: 'legacy_id is null or invalid', data: row });
      continue;
    }

    const size_tier_id = legacyIdToSizeTierId[legacy_id];
    if (size_tier_id === undefined) {
      errors.push({ row: i + 2, reason: `legacy_id ${legacy_id} not found in t_product_size_tiers`, data: row });
      continue;
    }

    recordsToInsert.push({
      rpro_id,
      size_tier_id,
      created_by: 'importRproToSizeTier.js'
    });
  }

  console.log(`[importRproToSizeTier] Prepared ${recordsToInsert.length} records for insert`);
  if (errors.length > 0) {
    console.warn(`[importRproToSizeTier] ⚠️  ${errors.length} rows had errors:`);
    errors.slice(0, 10).forEach(err => {
      console.warn(`  Row ${err.row}: ${err.reason}`, err.data);
    });
    if (errors.length > 10) {
      console.warn(`  ... and ${errors.length - 10} more errors`);
    }
  }

  if (recordsToInsert.length === 0) {
    console.log('[importRproToSizeTier] No valid records to insert');
    return;
  }

  // Insert in chunks
  const chunks = chunk(recordsToInsert, CHUNK_SIZE);
  console.log(`[importRproToSizeTier] Inserting ${recordsToInsert.length} records in ${chunks.length} chunks...`);

  let totalInserted = 0;
  let totalFailed = 0;

  for (let i = 0; i < chunks.length; i++) {
    const chunkData = chunks[i];
    console.log(`[importRproToSizeTier] Inserting chunk ${i + 1}/${chunks.length} (${chunkData.length} records)...`);

    const { data, error } = await supabase
      .from('tj_rpro_product_to_size_tier')
      .upsert(chunkData, {
        onConflict: 'rpro_id,size_tier_id',
        ignoreDuplicates: false
      });

    if (error) {
      console.error(`❌ Error inserting chunk ${i + 1}:`, error);
      totalFailed += chunkData.length;
    } else {
      totalInserted += chunkData.length;
      console.log(`✅ Chunk ${i + 1} inserted successfully`);
    }
  }

  console.log('\n[importRproToSizeTier] Import complete!');
  console.log(`  Total rows in TSV: ${rows.length}`);
  console.log(`  Valid records prepared: ${recordsToInsert.length}`);
  console.log(`  Successfully inserted: ${totalInserted}`);
  console.log(`  Failed to insert: ${totalFailed}`);
  console.log(`  Rows with errors: ${errors.length}`);
}

main().catch(err => {
  console.error('Fatal error:', err);
  process.exit(1);
});

