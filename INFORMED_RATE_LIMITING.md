# Informed Pricing Rate Limiting

## Overview

The `update_informed_pricing` task handler implements **intelligent rate limiting** to prevent hitting Informed's API limits:
- **Informed API Limit**: 12 feeds per hour
- **Our Strategy**: Maximum 1 API call every 15 minutes (4 per hour, well under the limit)
- **429 Handling**: Automatically reschedules tasks based on Informed's `Retry-After` header
- **Supports**: RPRO discs (FM/FN) and DGACC accessories (FBMDGA_/FBADGA_/DGACC)

## Supported Listing Types

The handler supports two types of Amazon listings:

### RPRO Discs (FM/FN prefixes)
- **FM** = Fulfilled by Merchant (FBM) - Uses `v_rpro_informed_pricing_calculations` view
- **FN** = Fulfilled by Amazon (FBA) - Uses `v_rpro_informed_pricing_calculations` view
- Upload status checked in `t_amaz_acc_listings` table
- Example SKUs: `FM11918`, `FN12674`

### DGACC Accessories (FBMDGA_/FBADGA_/DGACC prefixes)
- **FBMDGA_** = Fulfilled by Merchant (FBM) - Uses `v_dgacc_informed_pricing_calculations` view
- **FBADGA_** = Fulfilled by Amazon (FBA) - Uses `v_dgacc_informed_pricing_calculations` view
- **DGACC** = Generic accessories prefix - Uses `v_dgacc_informed_pricing_calculations` view
- Upload status checked in `t_amaz_dgacc_listings` table
- Example SKUs: `FBMDGA_738_B077KCCBJ3`, `FBADGA_123_B01234567`, `DGACC456`

Both types are processed in the same batch and sent to Informed in a single CSV upload.

## How It Works

### Three-Layer Rate Limiting

#### Layer 1: Check for 429 Reschedules
Before processing any tasks, the handler checks if there are pending tasks scheduled in the future. This indicates a previous 429 rate limit response.

**If found:**
- All ready tasks are rescheduled to match the future scheduled time
- Handler exits without making an API call
- Respects Informed's explicit rate limit timing

#### Layer 2: Check Recent API Calls (15-minute window)
If no 429 reschedules exist, check if an Informed API call was made in the last 15 minutes.

**If found:**
- Calculate next allowed time (last call + 15 minutes)
- Reschedule all ready tasks to that time
- Handler exits without making an API call

#### Layer 3: Process Batch
If both checks pass, proceed to process all pending tasks in a single batch.

### 429 Error Handling

When Informed returns a 429 rate limit error:

```
HTTP 429 Too Many Requests
Retry-After: Tue, 28 Oct 2025 00:41:50 GMT
Body: Feeds are limited to 12 per hour. A new feed can be submitted at Tue, 28 Oct 2025 00:41:50 GMT
```

**Handler response:**
1. Parse the `Retry-After` header (can be date string or seconds)
2. Add 15-minute buffer to be safe (in case of other Informed uploads)
3. Reschedule ALL tasks to that time
4. Exit without marking tasks as error

**Example:**
- Retry-After: `00:41:50`
- Buffer added: `+15 minutes`
- Tasks rescheduled to: `00:56:50`

## Task Flow

### Normal Flow (No Rate Limit)

```
00:00 - 10 tasks created → scheduled immediately
00:00 - Worker picks up tasks → rate limit check passes → API call ✅
00:00 - All 10 tasks marked completed

00:05 - 15 tasks created → scheduled immediately  
00:05 - Worker picks up tasks → rate limit check FAILS (last call was 5 min ago)
00:05 - Tasks rescheduled to 00:15 (15 min after last call)

00:15 - Worker picks up tasks → rate limit check passes → API call ✅
00:15 - All 15 tasks marked completed
```

### 429 Error Flow

```
00:00 - 10 tasks created → scheduled immediately
00:00 - Worker picks up tasks → API call → 429 response ❌
00:00 - Retry-After header: 00:41:50
00:00 - Tasks rescheduled to 00:56:50 (Retry-After + 15 min buffer)

00:05 - 15 tasks created → scheduled immediately
00:05 - Worker picks up tasks → sees future scheduled tasks (00:56:50)
00:05 - Tasks rescheduled to 00:56:50 (match existing reschedule)

00:56 - Worker picks up all 25 tasks → rate limit check passes → API call ✅
00:56 - All 25 tasks marked completed
```

## Task Enqueueing

Tasks are enqueued **immediately** (not delayed) by:
- `processRproCostPriceUpdatedTask` - when RPRO cost prices change
- `processSizeTierShippingOrPackingUpdatedTask` - when size tier costs change
- Database triggers (various pricing changes)

**Why immediate scheduling?**
- Rate limiting is handled by the processor, not the enqueuer
- Allows tasks to accumulate naturally
- Simpler logic - one place handles all rate limiting

## Monitoring

### Check Current Rate Limit Status

```javascript
// Check for recent API calls
const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000).toISOString();

const { data } = await supabase
  .from('t_task_queue')
  .select('id, processed_at, result')
  .eq('task_type', 'update_informed_pricing')
  .eq('status', 'completed')
  .gte('processed_at', fifteenMinutesAgo)
  .order('processed_at', { ascending: false })
  .limit(1);

// Check if it has informed_response (actual API call)
const result = JSON.parse(data[0].result);
if (result.informed_response) {
  const lastCall = new Date(data[0].processed_at);
  const nextAllowed = new Date(lastCall.getTime() + 15 * 60 * 1000);
  console.log(`Next allowed API call: ${nextAllowed.toISOString()}`);
}
```

### Check for 429 Reschedules

```javascript
const now = new Date();

const { data } = await supabase
  .from('t_task_queue')
  .select('id, scheduled_at')
  .eq('task_type', 'update_informed_pricing')
  .eq('status', 'pending')
  .gt('scheduled_at', now.toISOString())
  .order('scheduled_at', { ascending: true })
  .limit(1);

if (data && data.length > 0) {
  console.log(`Tasks rescheduled due to 429 until: ${data[0].scheduled_at}`);
}
```

## Key Benefits

✅ **Prevents rate limit errors** - Stays well under Informed's 12/hour limit
✅ **Automatic recovery** - Handles 429 errors gracefully without manual intervention
✅ **Batching efficiency** - Collects multiple tasks into single API calls
✅ **Respects Informed's timing** - Uses Retry-After header when provided
✅ **Safety buffer** - Adds 15 minutes to Retry-After to account for other uploads
✅ **No task loss** - Tasks are rescheduled, never marked as error due to rate limits

## Configuration

### Rate Limit Window
Currently hardcoded to **15 minutes** in `processUpdateInformedPricingTask.js`:

```javascript
const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000).toISOString();
```

### 429 Buffer Time
Currently hardcoded to **15 minutes** added to Retry-After:

```javascript
retryTime = new Date(retryTime.getTime() + 15 * 60 * 1000);
```

**Why 15 minutes?**
- Informed allows 12 feeds/hour = 1 feed every 5 minutes
- 15-minute window = 4 feeds/hour (well under limit)
- Provides safety margin for other Informed uploads
- Prevents cascading rate limit errors

## Troubleshooting

### Tasks stuck in pending status
**Check:** Are they scheduled in the future?
```sql
SELECT id, scheduled_at, created_at 
FROM t_task_queue 
WHERE task_type = 'update_informed_pricing' 
  AND status = 'pending' 
  AND scheduled_at > NOW()
ORDER BY scheduled_at;
```

**Likely cause:** Previous 429 error or recent API call

### Multiple 429 errors
**Check:** Is the 15-minute window too short?
- Consider increasing to 20 minutes
- Check for other systems making Informed API calls
- Verify Retry-After header is being parsed correctly

### Tasks not batching
**Check:** Are tasks being enqueued with future scheduled_at times?
- Tasks should be scheduled immediately (NOW())
- Batching happens in the processor, not the enqueuer
- Check `processRproCostPriceUpdatedTask` and `processSizeTierShippingOrPackingUpdatedTask`

