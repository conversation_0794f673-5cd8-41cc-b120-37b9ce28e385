// deployMultiStoreTriggers.js
// Deploy the updated trigger functions to Supabase for multi-store support

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function deployTriggers() {
  console.log('🚀 Deploying multi-store Shopify trigger updates...\n');

  try {
    // Read the SQL files
    const titleTriggerSql = fs.readFileSync(
      'sql/triggers/t_products_enqueue_shopify_title_update.sql',
      'utf8'
    );
    const descriptionTriggerSql = fs.readFileSync(
      'sql/triggers/t_products_enqueue_shopify_description_update.sql',
      'utf8'
    );

    // Deploy title trigger function
    console.log('📝 Deploying enqueue_update_shopify_title_task function...');
    const { error: titleError } = await supabase.rpc('exec_sql', {
      sql_query: titleTriggerSql
    });

    if (titleError) {
      console.error('❌ Error deploying title trigger:', titleError);
      throw titleError;
    }
    console.log('✅ Title trigger function deployed successfully\n');

    // Deploy description trigger function
    console.log('📝 Deploying enqueue_update_shopify_description_task function...');
    const { error: descriptionError } = await supabase.rpc('exec_sql', {
      sql_query: descriptionTriggerSql
    });

    if (descriptionError) {
      console.error('❌ Error deploying description trigger:', descriptionError);
      throw descriptionError;
    }
    console.log('✅ Description trigger function deployed successfully\n');

    console.log('🎉 All trigger functions deployed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Add Tippmann Parts credentials to .env file');
    console.log('2. Restart the worker daemon: pm2 restart worker-daemon');
    console.log('3. Test with products from both stores');

  } catch (error) {
    console.error('\n❌ Deployment failed:', error.message);
    process.exit(1);
  }
}

deployTriggers();

