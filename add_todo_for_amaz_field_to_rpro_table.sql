-- Add todo_for_amaz field to imported_table_rpro table
-- This field will store Amazon-specific readiness check results for RPRO records

-- Add the todo_for_amaz column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'imported_table_rpro' 
        AND column_name = 'todo_for_amaz'
    ) THEN
        ALTER TABLE public.imported_table_rpro 
        ADD COLUMN todo_for_amaz TEXT;
        
        -- Add a comment to the column
        COMMENT ON COLUMN public.imported_table_rpro.todo_for_amaz IS 'Amazon-specific readiness check results and todo items for RPRO records';
        
        RAISE NOTICE 'Added todo_for_amaz column to imported_table_rpro table';
    ELSE
        RAISE NOTICE 'todo_for_amaz column already exists in imported_table_rpro table';
    END IF;
END $$;

-- Create an index on the todo_for_amaz field for better query performance
CREATE INDEX IF NOT EXISTS idx_imported_table_rpro_todo_for_amaz 
ON public.imported_table_rpro(todo_for_amaz);

-- Grant necessary permissions
GRANT SELECT, UPDATE ON public.imported_table_rpro TO authenticated;

