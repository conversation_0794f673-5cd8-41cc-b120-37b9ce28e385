// processUpdateShopifyProductTitleTask.js
// Update a Shopify product's title by handle
// Source for new title:
// - Built from brand name + product name (brand optional)
// - Uses exact t_products.shopify_handle match; if not found, task errors with an explanation
// Supports multiple Shopify stores via shopify_store_id in payload

import { getShopifyCredentials, getProductsBaseUrl, shopifyGraphQLRequest as makeGraphQLRequest } from './shopifyStoreConfig.js';

function extractNumericIdFromGid(gid) {
  if (!gid) return null;
  const parts = String(gid).split('/');
  return parts[parts.length - 1];
}

export default async function processUpdateShopifyProductTitleTask(task, { supabase, updateTaskStatus, logError }) {
  try {
    // Parse payload
    const payload = typeof task.payload === 'object' && task.payload !== null ? task.payload : JSON.parse(task.payload || '{}');
    const handle = payload.shopify_handle;
    const shopifyStoreId = payload.shopify_store_id;

    if (!handle) {
      await updateTaskStatus(task.id, 'error', { message: 'Missing shopify_handle in payload' });
      return;
    }

    if (!shopifyStoreId) {
      await updateTaskStatus(task.id, 'error', { message: 'Missing shopify_store_id in payload' });
      return;
    }

    await updateTaskStatus(task.id, 'processing');

    // Get credentials for the specified store
    let credentials;
    try {
      credentials = getShopifyCredentials(shopifyStoreId);
    } catch (error) {
      await updateTaskStatus(task.id, 'error', { message: `Failed to get Shopify credentials: ${error.message}` });
      return;
    }

    const { endpoint, accessToken, storeName } = credentials;

    // Find Shopify product by handle
    const data = await makeGraphQLRequest(`
      query getProductByHandle($handle: String!) {
        productByHandle(handle: $handle) { id handle title }
      }
    `, { handle }, shopifyStoreId);

    const product = data?.productByHandle;
    if (!product) {
      await updateTaskStatus(task.id, 'error', { message: `No Shopify product found for handle ${handle} on ${storeName}` });
      return;
    }

    const productId = extractNumericIdFromGid(product.id);
    const currentTitle = product.title || '';

    // Fetch local product by exact handle
    const { data: prodRow, error: prodErr } = await supabase
      .from('t_products')
      .select('id, name, brand_id, shopify_handle')
      .eq('shopify_handle', handle)
      .maybeSingle();

    if (prodErr) {
      await logError?.(`[processUpdateShopifyProductTitleTask] DB error: ${prodErr.message}`, { handle });
    }

    if (!prodRow) {
      await updateTaskStatus(task.id, 'error', { message: `No local t_products row found for exact shopify_handle '${handle}'` });
      return;
    }

    // Fetch brand name if available
    let brandName = null;
    if (prodRow.brand_id != null) {
      try {
        const { data: brand } = await supabase.from('t_brands').select('brand').eq('id', prodRow.brand_id).maybeSingle();
        brandName = brand?.brand || null;
      } catch {}
    }

    const productName = (prodRow.name || '').trim();
    if (!productName) {
      await updateTaskStatus(task.id, 'error', { message: `Cannot build title: t_products.name is empty for handle '${handle}'` });
      return;
    }

    const newTitle = brandName ? `${brandName} ${productName}` : productName;

    // If unchanged, short-circuit
    if (newTitle === currentTitle) {
      await updateTaskStatus(task.id, 'completed', {
        message: 'Title already correct, no update needed',
        handle,
        product_id: productId,
        title: newTitle,
        store: storeName,
        store_id: shopifyStoreId,
      });
      return;
    }

    // Update Shopify title via REST API
    const baseUrl = getProductsBaseUrl(endpoint);
    const url = `${baseUrl}/products/${productId}.json`;
    const updatePayload = { product: { id: Number(productId), title: newTitle } };

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': accessToken,
      },
      body: JSON.stringify(updatePayload),
    });

    const result = await response.json();
    if (!response.ok) {
      throw new Error(`Error updating product ${productId} on ${storeName}: ${JSON.stringify(result)}`);
    }

    const updated = result.product;

    await updateTaskStatus(task.id, 'completed', {
      message: `Updated Shopify product title on ${storeName}`,
      handle,
      product_id: productId,
      old_title: currentTitle,
      new_title: updated?.title || newTitle,
      store: storeName,
      store_id: shopifyStoreId,
    });
  } catch (error) {
    await logError?.(`[processUpdateShopifyProductTitleTask] Error: ${error.message}`, { taskId: task?.id });
    await updateTaskStatus(task.id, 'error', { message: 'Error updating Shopify product title', error: error.message });
  }
}

