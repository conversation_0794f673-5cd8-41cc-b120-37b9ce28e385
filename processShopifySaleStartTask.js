import fetch from 'node-fetch';
import { findVariantBySku } from './shopifyGraphQL.js';
import { getShopifyCredentials } from './shopifyStoreConfig.js';

function approxTimesEqual(a, b, toleranceMs = 2 * 60 * 1000) {
  if (!a || !b) return false;
  return new Date(a).getTime() === new Date(b).getTime();
}

function toMoneyString(value) {
  if (value === null || value === undefined) return null;
  const n = Number(value);
  if (Number.isNaN(n)) return null;
  return n.toFixed(2);
}

function moneyEquals(a, b) {
  if (a === null || a === undefined) return b === null || b === undefined;
  if (b === null || b === undefined) return false;
  return Math.abs(Number(a) - Number(b)) < 0.005; // tolerate rounding
}


export default async function processShopifySaleStartTask(task, { supabase, updateTaskStatus, logError } = {}) {
  if (!supabase) {
    console.error('[processShopifySaleStartTask] Supabase client not provided in context');
    return;
  }

  try {
    await updateTaskStatus(task.id, 'processing');

    const salePriceId = task?.payload?.sale_price_id ?? task?.payload?.salePriceId ?? task?.payload?.id;
    if (!salePriceId) throw new Error('sale_price_id is required in payload');

    const storeId = task?.payload?.shopify_store_id || 1; // Default to DZ Discs for backward compatibility
    const { endpoint, accessToken, storeName } = getShopifyCredentials(storeId);
    const variantsEndpoint = endpoint?.replace('graphql.json', 'variants/');

    console.log(`[processShopifySaleStartTask] Processing sale start for ${storeName} (store ${storeId})`);

    const { data: sale, error: saleErr } = await supabase
      .from('t_shopify_sale_prices')
      .select('*')
      .eq('id', salePriceId)
      .single();

    if (saleErr) {
      // Treat not found as deleted/missing sale record: skip task
      const msg = saleErr.message || '';
      if (/no rows?/i.test(msg) || saleErr.code === 'PGRST116') {
        await updateTaskStatus(task.id, 'completed', {
          status: 'skipped_missing_sale_record',
          message: `Sale price record not found (deleted?) for id=${salePriceId}`,
          sale_price_id: salePriceId
        });
        return;
      }
      throw new Error(`Failed to fetch sale price record id=${salePriceId}: ${saleErr.message}`);
    }
    if (!sale) {
      await updateTaskStatus(task.id, 'completed', {
        status: 'skipped_missing_sale_record',
        message: `Sale price record not found (deleted?) for id=${salePriceId}`,
        sale_price_id: salePriceId
      });
      return;
    }

    const nowIso = new Date().toISOString();
    const schedAt = task.scheduled_at;

    // Validation: task should correspond to the sale start window and sale must not have ended
    if (sale.end_at && new Date(sale.end_at) <= new Date()) {
      await updateTaskStatus(task.id, 'completed', {
        status: 'skipped_already_ended',
        message: 'Sale already ended by the time start task ran',
        sale_price_id: salePriceId
      });
      return;
    }

    if (!approxTimesEqual(schedAt, sale.start_at)) {
      await updateTaskStatus(task.id, 'completed', {
        status: 'skipped_stale_task',
        message: `Task scheduled_at ${schedAt} does not match sale.start_at ${sale.start_at}`,
        sale_price_id: salePriceId
      });
      return;
    }

    // Idempotency: already applied
    if (sale.sale_price_applied_at) {
      await updateTaskStatus(task.id, 'completed', {
        status: 'already_applied',
        message: 'Sale price already applied previously',
        sale_price_id: salePriceId,
        applied_at: sale.sale_price_applied_at
      });
      return;
    }

    const sku = sale.shopify_sku;
    if (!sku) throw new Error('shopify_sku is missing on sale record');

    // Resolve Shopify variant by SKU
    const variantInfo = await findVariantBySku(sku, storeId);
    if (!variantInfo) {
      await updateTaskStatus(task.id, 'completed', {
        status: 'variant_not_found',
        message: `No Shopify variant found for SKU ${sku} on ${storeName}`,
        sale_price_id: salePriceId,
        sku,
        store: storeName,
        store_id: storeId
      });
      return;
    }

    const variantIdNumeric = String(variantInfo.variantId).split('/').pop();

    // Pricing: always read from DB record, never from task payload; normalize to money strings
    const saleRetail = sale.price;
    const saleCompare = sale.compare_at;
    const targetPrice = toMoneyString(saleRetail);
    const targetCompare = saleCompare == null ? null : toMoneyString(saleCompare);

    const variantPayload = { variant: { id: variantIdNumeric } };
    if (targetPrice !== null) variantPayload.variant.price = targetPrice;
    // Always include compare_at_price: null clears it; value sets it
    variantPayload.variant.compare_at_price = targetCompare;

    // If nothing to update, still stamp as applied to avoid reprocessing
    if (Object.keys(variantPayload.variant).length === 1) {
      const { error: updSaleNoopErr } = await supabase
        .from('t_shopify_sale_prices')
        .update({ sale_price_applied_at: nowIso, last_apply_status: 'success_no_changes' })
        .eq('id', salePriceId);
      if (updSaleNoopErr) await logError?.('[processShopifySaleStartTask] Failed to stamp sale as applied (noop)', { salePriceId, err: updSaleNoopErr.message });
      await updateTaskStatus(task.id, 'completed', {
        status: 'applied_no_changes',
        message: 'No price/compare_at provided to apply; stamped as applied',
        sale_price_id: salePriceId,
        sku
      });
      return;
    }

    // Shopify REST update
    const resp = await fetch(`${variantsEndpoint}${variantIdNumeric}.json`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': accessToken
      },
      body: JSON.stringify(variantPayload)
    });

    const bodyText = await resp.text();
    if (!resp.ok) {
      throw new Error(`Shopify PUT variant ${variantIdNumeric} failed: ${resp.status} ${resp.statusText} - ${bodyText}`);
    }

    // Validate Shopify returned values match targets
    let bodyJson = null; try { bodyJson = JSON.parse(bodyText); } catch {}
    const v = bodyJson?.variant;
    if (v) {
      const okPrice = targetPrice === null ? true : moneyEquals(v.price, targetPrice);
      const okCompare = (targetCompare === null && (v.compare_at_price == null)) || moneyEquals(v.compare_at_price, targetCompare);
      if (!okPrice || !okCompare) {
        throw new Error(`Shopify response values differ from targets. Got price=${v.price}, compare_at=${v.compare_at_price}; expected price=${targetPrice}, compare_at=${targetCompare}`);
      }
    }

    // Stamp sale as applied
    const { error: updSaleErr } = await supabase
      .from('t_shopify_sale_prices')
      .update({ sale_price_applied_at: nowIso, last_apply_status: 'success' })
      .eq('id', salePriceId);

    if (updSaleErr) await logError?.('[processShopifySaleStartTask] Failed updating sale record after apply', { salePriceId, err: updSaleErr.message });

    await updateTaskStatus(task.id, 'completed', {
      status: 'applied',
      message: `Sale pricing applied on ${storeName}`,
      sale_price_id: salePriceId,
      sku,
      variant_id: variantIdNumeric,
      applied_price: targetPrice,
      applied_compare_at: targetCompare,
      store: storeName,
      store_id: storeId
    });
  } catch (err) {
    console.error('[processShopifySaleStartTask] Error:', err.message);
    const storeId = task?.payload?.shopify_store_id || 1;
    const { storeName } = getShopifyCredentials(storeId);

    try {
      const salePriceId = task?.payload?.sale_price_id ?? task?.payload?.salePriceId ?? task?.payload?.id;
      if (salePriceId) {
        await supabase
          .from('t_shopify_sale_prices')
          .update({ last_apply_status: `failed: ${err.message}` })
          .eq('id', salePriceId);
      }
    } catch (e2) {
      await logError?.('[processShopifySaleStartTask] Failed to update sale last_apply_status after error', { err: e2.message });
    }

    await updateTaskStatus(task.id, 'error', {
      message: `Error applying sale pricing on ${storeName}`,
      error: err.message,
      store: storeName,
      store_id: storeId
    });
  }
}

