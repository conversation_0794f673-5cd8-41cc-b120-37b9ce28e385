/**
 * Batch processor for set_disc_carry_cost tasks
 * 
 * This module processes multiple set_disc_carry_cost tasks together to speed up
 * the queue clearing. Instead of processing one disc at a time with multiple RPC calls,
 * we batch fetch all disc data and calculate carrying costs in a single operation.
 */

async function processSetDiscCarryCostBatch(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processSetDiscCarryCostBatch] Processing batch of set_disc_carry_cost tasks, triggered by task ${task.id}`);

  try {
    const now = new Date();

    // Fetch all pending set_disc_carry_cost tasks
    console.log(`[processSetDiscCarryCostBatch] Fetching all pending set_disc_carry_cost tasks...`);

    const { data: pendingTasks, error: tasksError } = await supabase
      .from('t_task_queue')
      .select('id, payload, status')
      .eq('task_type', 'set_disc_carry_cost')
      .in('status', ['pending', 'processing'])
      .lte('scheduled_at', now.toISOString())
      .order('id', { ascending: true });

    if (tasksError) {
      console.error(`[processSetDiscCarryCostBatch] Error fetching tasks:`, tasksError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to fetch tasks: ${tasksError.message}`,
        error: tasksError.message
      });
      return;
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log(`[processSetDiscCarryCostBatch] No pending set_disc_carry_cost tasks found`);
      await updateTaskStatus(task.id, 'completed', {
        message: 'No pending tasks to process',
        tasks_processed: 0
      });
      return;
    }

    console.log(`[processSetDiscCarryCostBatch] Found ${pendingTasks.length} pending tasks`);

    // Extract disc IDs from payloads
    const discIds = [];
    const taskMap = new Map(); // Map of discId -> task

    for (const t of pendingTasks) {
      try {
        let payload = t.payload;
        if (typeof payload === 'string') {
          payload = JSON.parse(payload);
        }
        const discId = payload.id;
        if (discId) {
          discIds.push(discId);
          taskMap.set(discId, t);
        }
      } catch (err) {
        console.error(`[processSetDiscCarryCostBatch] Error parsing payload for task ${t.id}:`, err.message);
      }
    }

    if (discIds.length === 0) {
      console.log(`[processSetDiscCarryCostBatch] No valid disc IDs found in tasks`);
      await updateTaskStatus(task.id, 'completed', {
        message: 'No valid disc IDs found',
        tasks_processed: 0
      });
      return;
    }

    console.log(`[processSetDiscCarryCostBatch] Fetching ${discIds.length} disc records...`);

    // Fetch all disc records in a single query
    const { data: discRecords, error: discsError } = await supabase
      .from('t_discs')
      .select('id, mps_id, shipment_id')
      .in('id', discIds);

    if (discsError) {
      console.error(`[processSetDiscCarryCostBatch] Error fetching disc records:`, discsError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to fetch disc records: ${discsError.message}`,
        error: discsError.message
      });
      return;
    }

    // Build a map of disc records for quick lookup
    const discMap = new Map();
    const validDiscIds = [];
    if (discRecords) {
      for (const disc of discRecords) {
        // Only include discs with both mps_id and shipment_id
        if (disc.mps_id && disc.shipment_id) {
          discMap.set(disc.id, disc);
          validDiscIds.push(disc.id);
        }
      }
    }

    if (validDiscIds.length === 0) {
      console.log(`[processSetDiscCarryCostBatch] No discs with both mps_id and shipment_id found`);
      await updateTaskStatus(task.id, 'completed', {
        message: 'No valid discs to process (missing mps_id or shipment_id)',
        tasks_processed: discIds.length,
        valid_discs: 0
      });
      return;
    }

    console.log(`[processSetDiscCarryCostBatch] Processing ${validDiscIds.length} valid discs...`);

    // Fetch all order costs in a single RPC call
    const { data: orderCosts, error: orderCostError } = await supabase.rpc(
      'get_disc_order_costs_batch',
      { mps_ids: validDiscIds.map(id => discMap.get(id).mps_id) }
    );

    if (orderCostError) {
      console.error(`[processSetDiscCarryCostBatch] Error fetching order costs:`, orderCostError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to fetch order costs: ${orderCostError.message}`,
        error: orderCostError.message
      });
      return;
    }

    // Fetch all shipping multipliers in a single RPC call
    const { data: shippingMultipliers, error: shippingError } = await supabase.rpc(
      'get_shipment_multipliers_batch',
      { shipment_ids: validDiscIds.map(id => discMap.get(id).shipment_id) }
    );

    if (shippingError) {
      console.error(`[processSetDiscCarryCostBatch] Error fetching shipping multipliers:`, shippingError);
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to fetch shipping multipliers: ${shippingError.message}`,
        error: shippingError.message
      });
      return;
    }

    // Build maps for quick lookup
    const orderCostMap = new Map();
    const shippingMultiplierMap = new Map();

    if (orderCosts) {
      for (const oc of orderCosts) {
        orderCostMap.set(oc.mps_id, oc.order_cost);
      }
    }

    if (shippingMultipliers) {
      for (const sm of shippingMultipliers) {
        // shipment_id is SMALLINT in the database, ensure it's treated as a number
        shippingMultiplierMap.set(Number(sm.shipment_id), sm.shipping_multiplier);
      }
    }

    // Calculate carrying costs and prepare updates
    const updates = [];
    const results = [];

    for (const discId of validDiscIds) {
      const disc = discMap.get(discId);
      const orderCost = orderCostMap.get(disc.mps_id);
      const shippingMultiplier = shippingMultiplierMap.get(disc.shipment_id);

      if (orderCost !== null && orderCost !== undefined && shippingMultiplier !== null && shippingMultiplier !== undefined) {
        const carryingCost = parseFloat((orderCost * shippingMultiplier).toFixed(4));
        updates.push({
          id: discId,
          carrying_cost: carryingCost
        });
        results.push({
          disc_id: discId,
          success: true,
          carrying_cost: carryingCost,
          order_cost: orderCost,
          shipping_multiplier: shippingMultiplier
        });
      } else {
        results.push({
          disc_id: discId,
          success: false,
          error: 'Missing order cost or shipping multiplier'
        });
      }
    }

    console.log(`[processSetDiscCarryCostBatch] Batch updating ${updates.length} discs with carrying costs...`);

    // Batch update all discs
    if (updates.length > 0) {
      const { error: updateError } = await supabase
        .from('t_discs')
        .upsert(updates, { onConflict: 'id' });

      if (updateError) {
        console.error(`[processSetDiscCarryCostBatch] Error batch updating discs:`, updateError);
        await updateTaskStatus(task.id, 'error', {
          message: `Failed to update discs: ${updateError.message}`,
          error: updateError.message,
          discs_processed: validDiscIds.length,
          discs_updated: 0
        });
        return;
      }
    }

    console.log(`[processSetDiscCarryCostBatch] Successfully batch updated ${updates.length} discs`);

    // Mark all processed tasks as completed
    const taskIds = Array.from(taskMap.values()).map(t => t.id);
    
    for (const taskId of taskIds) {
      await updateTaskStatus(taskId, 'completed', {
        message: 'Processed as part of batch',
        batch_size: discIds.length
      });
    }

    // Mark the triggering task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully processed ${discIds.length} carry cost tasks and updated ${updates.length} discs`,
      tasks_processed: discIds.length,
      discs_updated: updates.length,
      results: results
    });

  } catch (err) {
    const errMsg = `[processSetDiscCarryCostBatch] Exception while processing batch: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, 'Processing set_disc_carry_cost batch');

    await updateTaskStatus(task.id, 'error', {
      message: 'Failed to process carry cost batch due to an unexpected error.',
      error: err.message
    });
  }
}

export default processSetDiscCarryCostBatch;

