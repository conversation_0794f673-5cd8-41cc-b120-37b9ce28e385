import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function enqueueVerifyTask() {
  try {
    console.log('📋 Enqueueing verify_t_images_image task for image 174888...\n');

    // Enqueue a verify_t_images_image task
    const { data: taskInsert, error: taskErr } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'verify_t_images_image',
        payload: { image_id: 174888 },
        status: 'pending',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'enqueue_verify_image_task'
      })
      .select();

    if (taskErr) {
      console.error('❌ Error enqueueing task:', taskErr);
      return;
    }

    console.log('✅ verify_t_images_image task enqueued');
    console.log(`   Task ID: ${taskInsert[0].id}`);
    console.log(`   Image ID: 174888`);
    console.log(`   Variant ID: 4879`);
    console.log();

    console.log('🎯 WHAT HAPPENS NEXT:');
    console.log('   1. Worker picks up the verify_t_images_image task');
    console.log('   2. Spawns verifyImage.js with --id=174888');
    console.log('   3. verifyImage.js:');
    console.log('      - Fetches t_images record 174888');
    console.log('      - Gets folder_product_variants = "dgaccessories" from t_config');
    console.log('      - Gets public_image_server = "https://s3.amazonaws.com/paintball/shopify" from t_config');
    console.log('      - Builds URL: https://s3.amazonaws.com/paintball/shopify/dgaccessories/4879.jpg');
    console.log('      - Performs HEAD request to verify image exists');
    console.log('      - Sets image_verified = true if accessible');
    console.log('   4. Trigger fires: t_images_enqueue_variant_ready_on_image_verified');
    console.log('   5. Enqueues check_if_product_variant_is_ready task for variant 4879');
    console.log('   6. That task runs and passes the image check');
    console.log('   7. Variant marked as "Ready to Publish"');
    console.log();

  } catch (err) {
    console.error('❌ Error:', err.message);
  }
}

enqueueVerifyTask();

