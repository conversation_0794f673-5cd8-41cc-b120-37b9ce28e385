import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function checkRLS() {
  try {
    console.log('Checking RLS policies on t_images...\n');

    // Check if RLS is enabled
    const { data: rls, error: rlsErr } = await supabase.rpc('exec_sql', { 
      sql_query: `SELECT schemaname, tablename, rowsecurity FROM pg_tables WHERE tablename = 't_images'` 
    });

    if (rlsErr) {
      console.error('Error checking RLS:', rlsErr);
    } else {
      console.log('RLS Status:', JSON.stringify(rls, null, 2));
    }

    // Check policies
    const { data: policies, error: polErr } = await supabase.rpc('exec_sql', { 
      sql_query: `SELECT schemaname, tablename, policyname, permissive, roles, qual, with_check FROM pg_policies WHERE tablename = 't_images'` 
    });

    if (polErr) {
      console.error('Error checking policies:', polErr);
    } else {
      console.log('\nPolicies:', JSON.stringify(policies, null, 2));
    }

  } catch (err) {
    console.error('Exception:', err.message);
  }
}

checkRLS();

