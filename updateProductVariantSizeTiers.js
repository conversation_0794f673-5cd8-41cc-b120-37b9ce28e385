// updateProductVariantSizeTiers.js
// Update t_product_variants.size_tier_id from siztiers.csv
// Source: C:\Users\<USER>\supabase_project\data\external data\delete\siztiers.csv
//
// CSV columns:
//   id -> t_product_variants.id
//   size_tier_id -> t_product_variants.size_tier_id

import fs from 'fs';
import path from 'path';
import Papa from 'papaparse';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const FILE_PATH = 'C:\\Users\\<USER>\\supabase_project\\data\\external data\\delete\\siztiers.csv';
const CHUNK_SIZE = 500;

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

function toIntOrNull(v) {
  if (v === undefined || v === null || v === '') return null;
  const s = String(v).trim();
  const n = Number(s);
  return Number.isFinite(n) ? Math.trunc(n) : null;
}

function parseCsv(filePath) {
  const raw = fs.readFileSync(filePath, 'utf-8');
  const { data, errors } = Papa.parse(raw, {
    header: true,
    skipEmptyLines: true,
    dynamicTyping: false,
  });
  if (errors && errors.length) {
    console.warn('[updateProductVariantSizeTiers] CSV parse warnings (first 5):', errors.slice(0, 5));
  }
  return data;
}

function chunk(arr, size) {
  const out = [];
  for (let i = 0; i < arr.length; i += size) out.push(arr.slice(i, i + size));
  return out;
}

async function main() {
  console.log('[updateProductVariantSizeTiers] Starting update...');
  
  // Check if file exists
  if (!fs.existsSync(FILE_PATH)) {
    console.error(`❌ File not found: ${FILE_PATH}`);
    process.exit(1);
  }

  // Parse CSV
  console.log('[updateProductVariantSizeTiers] Parsing CSV file...');
  const rows = parseCsv(FILE_PATH);
  console.log(`[updateProductVariantSizeTiers] Parsed ${rows.length} rows from CSV`);

  if (rows.length === 0) {
    console.log('[updateProductVariantSizeTiers] No data to process');
    return;
  }

  // Transform rows
  const recordsToUpdate = [];
  const errors = [];

  for (let i = 0; i < rows.length; i++) {
    const row = rows[i];
    const variant_id = toIntOrNull(row.id);
    const size_tier_id = toIntOrNull(row.size_tier_id);

    if (variant_id === null) {
      errors.push({ row: i + 2, reason: 'id is null or invalid', data: row });
      continue;
    }

    if (size_tier_id === null) {
      errors.push({ row: i + 2, reason: 'size_tier_id is null or invalid', data: row });
      continue;
    }

    recordsToUpdate.push({
      id: variant_id,
      size_tier_id: size_tier_id
    });
  }

  console.log(`[updateProductVariantSizeTiers] Prepared ${recordsToUpdate.length} records for update`);
  if (errors.length > 0) {
    console.warn(`[updateProductVariantSizeTiers] ⚠️  ${errors.length} rows had errors:`);
    errors.slice(0, 10).forEach(err => {
      console.warn(`  Row ${err.row}: ${err.reason}`, err.data);
    });
    if (errors.length > 10) {
      console.warn(`  ... and ${errors.length - 10} more errors`);
    }
  }

  if (recordsToUpdate.length === 0) {
    console.log('[updateProductVariantSizeTiers] No valid records to update');
    return;
  }

  // Use SQL batch update for efficiency
  console.log(`[updateProductVariantSizeTiers] Updating ${recordsToUpdate.length} records using SQL batch update...`);

  // Build SQL UPDATE statement using CASE
  const caseStatements = recordsToUpdate.map(r =>
    `WHEN ${r.id} THEN ${r.size_tier_id}`
  ).join('\n    ');

  const ids = recordsToUpdate.map(r => r.id).join(',');

  const updateSQL = `
    UPDATE t_product_variants
    SET size_tier_id = CASE id
      ${caseStatements}
    END
    WHERE id IN (${ids});
  `;

  console.log(`[updateProductVariantSizeTiers] Executing SQL update for ${recordsToUpdate.length} records...`);

  const { data, error } = await supabase.rpc('exec_sql', {
    sql_query: updateSQL
  });

  if (error) {
    console.error('❌ Error executing SQL update:', error);
    console.log('\nTrying chunk-by-chunk approach instead...');

    // Fallback to chunked SQL updates
    const chunks = chunk(recordsToUpdate, CHUNK_SIZE);
    let totalUpdated = 0;
    let totalFailed = 0;

    for (let i = 0; i < chunks.length; i++) {
      const chunkData = chunks[i];
      console.log(`[updateProductVariantSizeTiers] Processing chunk ${i + 1}/${chunks.length} (${chunkData.length} records)...`);

      const chunkCaseStatements = chunkData.map(r =>
        `WHEN ${r.id} THEN ${r.size_tier_id}`
      ).join('\n      ');

      const chunkIds = chunkData.map(r => r.id).join(',');

      const chunkSQL = `
        UPDATE t_product_variants
        SET size_tier_id = CASE id
          ${chunkCaseStatements}
        END
        WHERE id IN (${chunkIds});
      `;

      const { data: chunkData2, error: chunkError } = await supabase.rpc('exec_sql', {
        sql_query: chunkSQL
      });

      if (chunkError) {
        console.error(`❌ Error updating chunk ${i + 1}:`, chunkError);
        totalFailed += chunkData.length;
      } else {
        totalUpdated += chunkData.length;
        console.log(`✅ Chunk ${i + 1} updated successfully`);
      }
    }

    console.log('\n[updateProductVariantSizeTiers] Chunked update complete!');
    console.log(`  Successfully updated: ${totalUpdated}`);
    console.log(`  Failed to update: ${totalFailed}`);
  } else {
    console.log('✅ All records updated successfully!');
  }

  if (!error) {
    console.log('\n[updateProductVariantSizeTiers] Update complete!');
    console.log(`  Total rows in CSV: ${rows.length}`);
    console.log(`  Valid records prepared: ${recordsToUpdate.length}`);
    console.log(`  Rows with errors: ${errors.length}`);
  }
}

main().catch(err => {
  console.error('Fatal error:', err);
  process.exit(1);
});

