// testMultiStoreConfig.js
// Test the multi-store Shopify configuration

import { getShopifyCredentials } from './shopifyStoreConfig.js';
import dotenv from 'dotenv';

dotenv.config();

console.log('🧪 Testing Multi-Store Shopify Configuration\n');
console.log('='.repeat(60));

// Test Store 1 (DZ Discs)
console.log('\n📍 Testing Store ID 1 (DZ Discs)...');
try {
  const store1 = getShopifyCredentials(1);
  console.log('✅ Store Name:', store1.storeName);
  console.log('✅ Endpoint:', store1.endpoint);
  console.log('✅ Access Token:', store1.accessToken ? `${store1.accessToken.substring(0, 10)}...` : 'MISSING');
} catch (error) {
  console.error('❌ Error:', error.message);
}

// Test Store 2 (Tippmann Parts)
console.log('\n📍 Testing Store ID 2 (Tippmann Parts)...');
try {
  const store2 = getShopifyCredentials(2);
  console.log('✅ Store Name:', store2.storeName);
  console.log('✅ Endpoint:', store2.endpoint);
  console.log('✅ Access Token:', store2.accessToken ? `${store2.accessToken.substring(0, 10)}...` : 'MISSING');
} catch (error) {
  console.error('❌ Error:', error.message);
  console.log('\n⚠️  You need to add Tippmann Parts credentials to .env:');
  console.log('   SHOPIFY_ENDPOINT_TIPPMANN=https://tippmannparts.myshopify.com/admin/api/2024-01/graphql.json');
  console.log('   SHOPIFY_ACCESS_TOKEN_TIPPMANN=shpat_...');
}

// Test invalid store ID
console.log('\n📍 Testing Invalid Store ID (should error)...');
try {
  const store3 = getShopifyCredentials(999);
  console.log('❌ Should have thrown an error!');
} catch (error) {
  console.log('✅ Correctly rejected:', error.message);
}

console.log('\n' + '='.repeat(60));
console.log('\n✨ Configuration test complete!');

