const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔄 VEEQO VARIANT MERGER');
console.log('='.repeat(60));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // 204 No Content is a success response with no body
    if (response.status === 204 || method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to find sellable by SKU
async function findSellableBySku(sku) {
  console.log(`🔍 Searching for SKU: ${sku}`);
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products?query=${encodeURIComponent(sku)}`);
  
  if (!result.success) {
    console.error(`   ❌ Error searching: ${result.error}`);
    return null;
  }
  
  for (const product of result.data) {
    if (product.sellables) {
      const sellable = product.sellables.find(s => s.sku_code === sku);
      if (sellable) {
        console.log(`   ✅ Found: Product ID ${product.id}, Sellable ID ${sellable.id}`);
        console.log(`      Title: ${product.title}`);
        console.log(`      SKU: ${sellable.sku_code}`);
        
        // Get stock
        let stock = 0;
        if (sellable.stock_entries && sellable.stock_entries.length > 0) {
          stock = sellable.stock_entries.reduce((sum, entry) => sum + (entry.physical_stock_level || 0), 0);
        }
        console.log(`      Stock: ${stock}`);
        
        return { product, sellable, stock };
      }
    }
  }
  
  console.log(`   ❌ Not found`);
  return null;
}

// Function to get product details
async function getProductDetails(productId) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!result.success) {
    console.error(`❌ Error fetching product: ${result.error}`);
    return null;
  }
  
  return result.data;
}

// Function to update channel sellable
async function updateChannelSellable(channelSellableId, newSellableId) {
  const data = {
    channel_sellable: {
      sellable_id: newSellableId
    }
  };
  
  const result = await makeVeeqoRequest(
    `https://api.veeqo.com/channel_sellables/${channelSellableId}`,
    'PUT',
    data
  );
  
  return result;
}

// Function to update stock
async function updateStockEntry(sellableId, quantity) {
  const warehouseId = 99881;
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/sellables/${sellableId}/stock_entries`);
  
  if (!result.success) {
    return { success: false, error: result.error };
  }
  
  const stockEntry = result.data.find(entry => entry.warehouse_id === warehouseId);
  
  if (!stockEntry) {
    return { success: false, error: 'Stock entry not found for warehouse 99881' };
  }
  
  const updateResult = await makeVeeqoRequest(
    `https://api.veeqo.com/stock_entries/${stockEntry.id}`,
    'PUT',
    { stock_entry: { physical_stock_level: quantity } }
  );
  
  return updateResult;
}

// Function to delete product
async function deleteProduct(productId) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`, 'DELETE');
  return result;
}

// Main merge function
async function mergeVariants(oldSku, newSku, deleteOld = false) {
  console.log(`\n🔄 Merging variants: ${oldSku} → ${newSku}`);
  console.log('─'.repeat(60));
  
  // Step 1: Find both SKUs
  console.log(`\n📋 STEP 1: Finding both variants`);
  console.log('─'.repeat(60));
  
  const oldVariant = await findSellableBySku(oldSku);
  if (!oldVariant) {
    console.error(`\n❌ Could not find old SKU: ${oldSku}`);
    return false;
  }
  
  const newVariant = await findSellableBySku(newSku);
  if (!newVariant) {
    console.error(`\n❌ Could not find new SKU: ${newSku}`);
    return false;
  }
  
  // Step 2: Get full product details for old variant
  console.log(`\n📋 STEP 2: Getting channel products for ${oldSku}`);
  console.log('─'.repeat(60));
  
  const oldProduct = await getProductDetails(oldVariant.product.id);
  if (!oldProduct) {
    return false;
  }
  
  // Find all channel sellables with the old SKU
  const channelSellablesToUpdate = [];
  
  if (oldProduct.channel_products) {
    for (const channelProduct of oldProduct.channel_products) {
      if (channelProduct.channel_sellables) {
        for (const channelSellable of channelProduct.channel_sellables) {
          if (channelSellable.remote_sku === oldSku) {
            channelSellablesToUpdate.push({
              channelProduct,
              channelSellable
            });
          }
        }
      }
    }
  }
  
  console.log(`   Found ${channelSellablesToUpdate.length} channel sellable(s) with SKU ${oldSku}`);
  
  // Step 3: Relink all channel sellables
  if (channelSellablesToUpdate.length > 0) {
    console.log(`\n📋 STEP 3: Relinking ${channelSellablesToUpdate.length} channel sellable(s) to ${newSku}`);
    console.log('─'.repeat(60));
    
    let successCount = 0;
    for (const item of channelSellablesToUpdate) {
      const { channelProduct, channelSellable } = item;
      
      console.log(`\n   📺 Channel Product: ${channelProduct.id}`);
      console.log(`      Title: ${channelProduct.remote_title}`);
      console.log(`      Channel: ${channelProduct.channel?.name || 'Unknown'}`);
      console.log(`      Channel Sellable: ${channelSellable.id}`);
      console.log(`      Remote SKU: ${channelSellable.remote_sku}`);
      console.log(`      Updating sellable: ${channelSellable.sellable_id} → ${newVariant.sellable.id}`);
      
      const result = await updateChannelSellable(
        channelSellable.id,
        newVariant.sellable.id
      );
      
      if (result.success) {
        console.log(`      ✅ Updated successfully`);
        successCount++;
      } else {
        console.log(`      ❌ Failed: ${result.error}`);
      }
    }
    
    console.log(`\n   📊 Summary: ${successCount} of ${channelSellablesToUpdate.length} channel sellables relinked`);
    
    if (successCount !== channelSellablesToUpdate.length) {
      console.error(`\n   ⚠️  Some channel sellables failed to relink`);
    }
  } else {
    console.log(`\n📋 STEP 3: No channel sellables to relink`);
    console.log('─'.repeat(60));
  }
  
  // Step 4: Verify the channel sellables were moved correctly
  console.log(`\n📋 STEP 4: Verifying channel sellables in ${newSku} product`);
  console.log('─'.repeat(60));

  const newProduct = await getProductDetails(newVariant.product.id);
  if (newProduct) {
    // Only check for channel sellables that have the old SKU's remote_sku
    // but are pointing to a deleted/wrong sellable
    const needsFixing = [];

    if (newProduct.channel_products) {
      for (const channelProduct of newProduct.channel_products) {
        if (channelProduct.channel_sellables) {
          for (const channelSellable of channelProduct.channel_sellables) {
            // Only fix if remote_sku matches the old SKU we're merging
            if (channelSellable.remote_sku === oldSku &&
                channelSellable.sellable_id !== newVariant.sellable.id) {
              needsFixing.push({
                channelProduct,
                channelSellable
              });
            }
          }
        }
      }
    }

    if (needsFixing.length > 0) {
      console.log(`   Found ${needsFixing.length} channel sellable(s) with remote_sku=${oldSku} that need fixing`);

      let fixedCount = 0;
      for (const item of needsFixing) {
        const { channelProduct, channelSellable } = item;

        console.log(`\n   📺 Channel Product: ${channelProduct.id} - ${channelProduct.remote_title}`);
        console.log(`      Channel Sellable: ${channelSellable.id}`);
        console.log(`      Remote SKU: ${channelSellable.remote_sku}`);
        console.log(`      Fixing sellable: ${channelSellable.sellable_id} → ${newVariant.sellable.id}`);

        const result = await updateChannelSellable(
          channelSellable.id,
          newVariant.sellable.id
        );

        if (result.success) {
          console.log(`      ✅ Fixed successfully`);
          fixedCount++;
        } else {
          console.log(`      ❌ Failed: ${result.error}`);
        }
      }

      console.log(`\n   📊 Summary: ${fixedCount} of ${needsFixing.length} channel sellables fixed`);
    } else {
      console.log(`   ✅ All channel sellables with remote_sku=${oldSku} are correctly linked`);
    }
  }
  
  // Step 5: Delete old variant/product if requested
  if (deleteOld) {
    console.log(`\n📋 STEP 5: Deleting old variant ${oldSku}`);
    console.log('─'.repeat(60));
    
    // First, set stock to 0
    console.log(`\n   📦 Setting stock to 0 for sellable ${oldVariant.sellable.id}`);
    const stockResult = await updateStockEntry(oldVariant.sellable.id, 0);
    
    if (stockResult.success) {
      console.log(`      ✅ Stock set to 0`);
    } else {
      console.log(`      ⚠️  Could not set stock to 0: ${stockResult.error}`);
    }
    
    // Delete the product
    console.log(`\n   🗑️  Deleting product ${oldVariant.product.id}`);
    const deleteResult = await deleteProduct(oldVariant.product.id);
    
    if (deleteResult.success) {
      console.log(`      ✅ Product deleted successfully`);
    } else {
      console.log(`      ❌ Failed to delete product: ${deleteResult.error}`);
      return false;
    }
  } else {
    console.log(`\n📋 STEP 5: Skipping deletion (use --delete flag to delete old variant)`);
    console.log('─'.repeat(60));
  }
  
  console.log(`\n${'='.repeat(60)}`);
  console.log(`✅ MERGE COMPLETE!`);
  console.log(`${'='.repeat(60)}`);
  console.log(`   Old SKU: ${oldSku} ${deleteOld ? '(deleted)' : '(kept)'}`);
  console.log(`   New SKU: ${newSku}`);
  console.log(`   Channel sellables relinked: ${channelSellablesToUpdate.length}`);
  
  return true;
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 2) {
  console.log(`
Usage: node mergeVeeqoVariants.cjs <old-sku> <new-sku> [--delete]

Arguments:
  old-sku   - The SKU of the variant to merge from (e.g., FM1784)
  new-sku   - The SKU of the variant to merge to (e.g., R01784)
  --delete  - Optional flag to delete the old variant after merging

Example:
  node mergeVeeqoVariants.cjs FM1784 R01784 --delete

This will:
  1. Find both variants
  2. Relink all channel sellables from FM1784 to R01784
  3. Fix any orphaned channel sellables in the R01784 product
  4. Delete the FM1784 product (if --delete flag is used)
`);
  process.exit(1);
}

const oldSku = args[0];
const newSku = args[1];
const deleteOld = args.includes('--delete');

mergeVariants(oldSku, newSku, deleteOld)
  .then(success => {
    if (success) {
      console.log(`\n✅ All done!`);
      process.exit(0);
    } else {
      console.log(`\n❌ Merge failed`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });

