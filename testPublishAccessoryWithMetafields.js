// Test script to enqueue a publish_product_accessory task and verify metafields are set
// This will test the complete flow including metafield setting

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testPublishAccessoryWithMetafields() {
  console.log('🧪 Testing publish_product_accessory task with metafields...\n');

  try {
    // 1. Find a suitable test product
    console.log('1️⃣ Finding test accessory product...');
    const { data: products, error: prodError } = await supabase
      .from('t_products')
      .select(`
        id, name, todo,
        t_product_variants!inner(
          id, color_id, op1_name, op1_value, op2_name, op2_value, op3_name, op3_value,
          uploaded_to_shopify_at, todo
        )
      `)
      .is('shopify_uploaded_at', null)
      .is('t_product_variants.uploaded_to_shopify_at', null)
      .limit(5);

    if (prodError) {
      console.error('❌ Error fetching products:', prodError.message);
      return;
    }

    if (!products || products.length === 0) {
      console.log('⚠️ No suitable test products found.');
      return;
    }

    // Find a product that's ready to publish
    let testProduct = null;
    for (const product of products) {
      const isProductReady = /^ready to publish$/i.test((product.todo || '').trim());
      const hasReadyVariant = product.t_product_variants.some(v => 
        /^ready to publish$/i.test((v.todo || '').trim())
      );
      
      if (isProductReady && hasReadyVariant) {
        testProduct = product;
        break;
      }
    }

    if (!testProduct) {
      console.log('⚠️ No products found with "Ready to Publish" status.');
      console.log('Available products:');
      products.forEach(p => {
        console.log(`   - ${p.name} (ID: ${p.id}) - Product todo: "${p.todo}", Variant todos: [${p.t_product_variants.map(v => `"${v.todo}"`).join(', ')}]`);
      });
      return;
    }

    console.log(`✅ Found test product: ${testProduct.name} (ID: ${testProduct.id})`);
    
    const readyVariants = testProduct.t_product_variants.filter(v => 
      /^ready to publish$/i.test((v.todo || '').trim())
    );
    
    console.log(`   - Ready variants: ${readyVariants.length}`);
    readyVariants.forEach(v => {
      console.log(`     * Variant ${v.id}: Color ID ${v.color_id}, Options: ${v.op1_name}=${v.op1_value}, ${v.op2_name}=${v.op2_value}`);
    });

    // 2. Enqueue the task
    console.log('\n2️⃣ Enqueueing publish_product_accessory task...');
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'publish_product_accessory',
        payload: { 
          id: testProduct.id,
          shopify_store_id: 1 // DZ Discs store
        },
        status: 'pending',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'testPublishAccessoryWithMetafields.js'
      })
      .select()
      .single();

    if (taskError) {
      console.error('❌ Error enqueueing task:', taskError.message);
      return;
    }

    console.log(`✅ Successfully enqueued task with ID: ${task.id}`);
    console.log(`⏰ Task scheduled at: ${task.scheduled_at}`);
    
    console.log('\n📋 Task Details:');
    console.log(`   - Task Type: ${task.task_type}`);
    console.log(`   - Product ID: ${task.payload.id}`);
    console.log(`   - Store ID: ${task.payload.shopify_store_id}`);
    console.log(`   - Status: ${task.status}`);

    console.log('\n✅ Task enqueued successfully!');
    console.log('\n📝 Expected metafields to be set:');
    readyVariants.forEach(v => {
      console.log(`   Variant ${v.id}:`);
      if (v.color_id) {
        console.log(`     - dz_disc_variant.color_family (from color_id ${v.color_id})`);
      }
      
      let sizeValue = null;
      if (v.op1_name === 'Size' && v.op1_value) sizeValue = v.op1_value;
      else if (v.op2_name === 'Size' && v.op2_value) sizeValue = v.op2_value;
      else if (v.op3_name === 'Size' && v.op3_value) sizeValue = v.op3_value;
      
      if (sizeValue) {
        console.log(`     - dz_disc_variant.size = "${sizeValue}"`);
      }
    });

    console.log('\n🔄 The worker daemon will process this task and set the metafields.');
    console.log('💡 Check the worker logs to see the metafield setting in action!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testPublishAccessoryWithMetafields();
