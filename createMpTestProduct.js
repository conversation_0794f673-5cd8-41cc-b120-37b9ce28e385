/**
 * Create a test Shopify product for t_mp.id=1402
 * Product SKU: MP1402
 * Variants: All in-stock t_discs matching the mold and plastic
 */

import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const shopifyEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/products.json';
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

async function createMpTestProduct() {
  try {
    console.log('🔍 Fetching t_mp record with id=1402...');
    
    // Get t_mp record
    const { data: mpRecord, error: mpError } = await supabase
      .from('t_mp')
      .select('*')
      .eq('id', 1402)
      .single();
    
    if (mpError) throw mpError;
    console.log('✅ MP Record found:', mpRecord.id);

    // Get related data
    const { data: moldData } = await supabase
      .from('t_molds')
      .select('*')
      .eq('id', mpRecord.mold_id)
      .single();

    const { data: plasticData } = await supabase
      .from('t_plastics')
      .select('*')
      .eq('id', mpRecord.plastic_id)
      .single();

    const { data: brandData } = await supabase
      .from('t_brands')
      .select('*')
      .eq('id', mpRecord.brand_id)
      .single();

    console.log(`✅ Found: ${brandData.brand} - ${moldData.mold} - ${plasticData.plastic}`);

    // Get all t_mps records with same mold and plastic
    const { data: mpsRecords } = await supabase
      .from('t_mps')
      .select('id, stamp_id')
      .eq('mold_id', mpRecord.mold_id)
      .eq('plastic_id', mpRecord.plastic_id);

    const mpsIds = mpsRecords.map(m => m.id);
    console.log(`✅ Found ${mpsIds.length} MPS records with this mold/plastic combination`);

    // Get in-stock discs for these MPS records that have images
    const { data: discs } = await supabase
      .from('t_discs')
      .select('id, weight, color_id, mps_id, shopify_sku, image_file_name')
      .in('mps_id', mpsIds)
      .is('sold_date', null)
      .not('image_file_name', 'is', null)
      .order('weight', { ascending: true });

    console.log(`✅ Found ${discs.length} in-stock discs with images`);

    if (discs.length === 0) {
      console.log('⚠️  No in-stock discs found. Creating product with no variants.');
    }

    // Get config for image URLs
    const { data: publicServerConfig } = await supabase
      .from('t_config')
      .select('value')
      .eq('key', 'public_image_server')
      .single();

    const { data: folderDiscsConfig } = await supabase
      .from('t_config')
      .select('value')
      .eq('key', 'folder_discs')
      .single();

    const publicImageServer = publicServerConfig?.value || 'https://d3f34rkxix3zin.cloudfront.net';
    const folderDiscs = folderDiscsConfig?.value || 'shopify/discs';

    // Get color and stamp info for each disc
    const discDetails = await Promise.all(
      discs.map(async (disc) => {
        const { data: color } = await supabase
          .from('t_colors')
          .select('color')
          .eq('id', disc.color_id)
          .single();

        const { data: mps } = await supabase
          .from('t_mps')
          .select('stamp_id')
          .eq('id', disc.mps_id)
          .single();

        const { data: stamp } = await supabase
          .from('t_stamps')
          .select('stamp')
          .eq('id', mps.stamp_id)
          .single();

        // Build image URL for disc (all discs at this point have image_file_name)
        const subfolder = disc.image_file_name.substring(0, 6);
        const imageUrl = `${publicImageServer}/${folderDiscs}/${subfolder}/${disc.image_file_name}.jpg`;

        return {
          ...disc,
          color: color?.color || 'Unknown',
          stamp: stamp?.stamp || 'Unknown',
          imageUrl: imageUrl
        };
      })
    );

    console.log(`✅ Retrieved details for ${discDetails.length} discs`);
    console.log(`📸 Sample image URL: ${discDetails[0]?.imageUrl}`);

    // Build Shopify product payload
    const productTitle = `${brandData.brand} ${moldData.mold} ${plasticData.plastic}`;
    const productHandle = `mp${mpRecord.id}`.toLowerCase();

    // Build images array first so we can reference positions
    const images = discDetails.map((disc, index) => ({
      src: disc.imageUrl,
      alt: `${brandData.brand} ${moldData.mold} ${plasticData.plastic} - ${disc.stamp} - ${disc.color} - ${disc.weight}g - Disc ${disc.id}`,
      position: index + 1
    }));

    // Build variants (without image association - we'll do that after creation)
    const variants = discDetails.map((disc, index) => ({
      sku: `MP${mpRecord.id}-D${disc.id}`,
      option1: disc.stamp,
      option2: disc.weight ? `${disc.weight}g` : 'Unknown',
      option3: `${disc.color}-Disc${disc.id}`,
      price: plasticData.val_retail_price?.toString() || '19.99',
      compare_at_price: plasticData.val_msrp?.toString() || '19.99',
      weight: disc.weight ? Math.round(disc.weight * 28.35) : 175,
      weight_unit: 'g',
      barcode: disc.shopify_sku,
      inventory_management: 'shopify',
      inventory_policy: 'deny',
      fulfillment_service: 'manual',
      requires_shipping: true,
      inventory_quantity: 1,
      taxable: true
    }));

    // Get unique option values
    const option1Values = [...new Set(variants.map(v => v.option1))];
    const option2Values = [...new Set(variants.map(v => v.option2))];
    const option3Values = [...new Set(variants.map(v => v.option3))];

    const payload = {
      product: {
        title: productTitle,
        handle: productHandle,
        body_html: moldData.description || 'Test product',
        vendor: brandData.brand,
        product_type: 'Disc',
        tags: ['test', 'mp-product', `brand_${brandData.brand}`],
        status: 'active',
        published: true,
        options: [
          { name: 'Stamp', values: option1Values },
          { name: 'Weight', values: option2Values },
          { name: 'Color & Disc ID', values: option3Values }
        ],
        variants: variants,
        images: images
      }
    };

    console.log(`\n📦 Creating Shopify product with ${variants.length} variants...`);
    console.log(`   Title: ${productTitle}`);
    console.log(`   Handle: ${productHandle}`);
    console.log(`   SKU: MP${mpRecord.id}`);

    const response = await fetch(shopifyEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    if (!response.ok) {
      console.error('❌ Error creating product:', result);
      throw new Error(`Shopify API error: ${JSON.stringify(result)}`);
    }

    console.log('✅ Product created successfully!');
    console.log(`   Shopify Product ID: ${result.product.id}`);
    console.log(`   Handle: ${result.product.handle}`);
    console.log(`   Variants: ${result.product.variants.length}`);

    // Now associate images with variants
    console.log('\n🖼️  Associating images with variants...');
    const productId = result.product.id;
    const createdImages = result.product.images || [];
    const createdVariants = result.product.variants || [];

    console.log(`   Found ${createdImages.length} images and ${createdVariants.length} variants`);

    // Create a map of SKU to variant
    const skuToVariant = new Map();
    createdVariants.forEach(v => {
      skuToVariant.set(v.sku, v);
    });

    // Create a map of image src to image ID
    const imageSrcToId = new Map();
    createdImages.forEach(img => {
      imageSrcToId.set(img.src, img.id);
    });

    // Helper function to delay
    const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

    // Associate each disc's image with its variant
    let associatedCount = 0;
    const failedAssociations = [];

    for (const disc of discDetails) {
      const sku = `MP${mpRecord.id}-D${disc.id}`;
      const variant = skuToVariant.get(sku);

      // Find the image that was created from this disc's URL
      // Shopify converts the URL, so we need to find it by looking for the image that contains the disc filename
      const discFilename = disc.image_file_name;
      let imageId = null;

      for (const [imageSrc, id] of imageSrcToId.entries()) {
        if (imageSrc.includes(discFilename)) {
          imageId = id;
          break;
        }
      }

      if (variant && imageId) {
        try {
          const variantUpdateUrl = `https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/variants/${variant.id}.json`;
          const updateResponse = await fetch(variantUpdateUrl, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'X-Shopify-Access-Token': shopifyAccessToken
            },
            body: JSON.stringify({
              variant: {
                id: variant.id,
                image_id: imageId
              }
            })
          });

          if (updateResponse.ok) {
            console.log(`   ✅ Variant ${variant.id} (${sku}) associated with image ${imageId}`);
            associatedCount++;
          } else {
            const errorText = await updateResponse.text();
            console.warn(`   ⚠️  Failed to associate variant ${variant.id}: ${errorText}`);
            failedAssociations.push({ variant, imageId, sku });
          }
        } catch (err) {
          console.warn(`   ⚠️  Error associating variant ${variant.id}: ${err.message}`);
          failedAssociations.push({ variant, imageId, sku });
        }
      } else {
        if (!variant) console.warn(`   ⚠️  Variant not found for SKU: ${sku}`);
        if (!imageId) console.warn(`   ⚠️  Image not found for filename: ${discFilename}`);
      }

      // Add a small delay between requests to avoid "product is being modified" errors
      await delay(200);
    }

    // Retry failed associations after a longer delay
    if (failedAssociations.length > 0) {
      console.log(`\n⏳ Retrying ${failedAssociations.length} failed associations after 5 second delay...`);
      await delay(5000);

      for (const { variant, imageId, sku } of failedAssociations) {
        try {
          const variantUpdateUrl = `https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/variants/${variant.id}.json`;
          const updateResponse = await fetch(variantUpdateUrl, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'X-Shopify-Access-Token': shopifyAccessToken
            },
            body: JSON.stringify({
              variant: {
                id: variant.id,
                image_id: imageId
              }
            })
          });

          if (updateResponse.ok) {
            console.log(`   ✅ Retry: Variant ${variant.id} (${sku}) associated with image ${imageId}`);
            associatedCount++;
          } else {
            console.warn(`   ⚠️  Retry failed for variant ${variant.id}`);
          }
        } catch (err) {
          console.warn(`   ⚠️  Retry error for variant ${variant.id}: ${err.message}`);
        }

        await delay(200);
      }
    }

    console.log(`\n✅ All done! Associated ${associatedCount}/${discDetails.length} variant images`);

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

createMpTestProduct();

